{"name": "bridge-backend-v2", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:create:init": "npx prisma migrate dev --create-only --name init", "migrate:dev": "prisma migrate dev", "migrate:dev:create": "prisma migrate dev --create-only", "migrate:reset": "prisma migrate reset", "migrate:deploy": "npx prisma migrate deploy", "migrate:status": "npx prisma migrate status", "migrate:resolve": "npx prisma migrate resolve", "prisma:studio": "npx prisma studio", "prisma:generate": "npx prisma generate", "prisma:generate:watch": "npx prisma generate --watch", "start:db": "yarn run migrate:up && yarn run prisma:generate && yarn run seed", "db:seed": "prisma db seed", "postinstall": "yarn run prisma:generate", "docker:seed": "docker exec -it bridge_api npm run seed", "docker": "docker compose up -d", "docker:build": "docker build -t bridge_api .", "docker:stop": "docker compose down", "docker:run:container": "docker run -d -p 8100:3000 --name=bridge_api bridge_api", "docker:remove": "docker rm -f bridge_api", "gcp:deploy": "gcloud run deploy --source .", "stripe:listen-webhook": "stripe listen --forward-to localhost:8900/stripe/webhook"}, "dependencies": {"@api/developers-hub": "file:.api/apis/developers-hub", "@apollo/server": "^4.9.5", "@faker-js/faker": "^8.3.1", "@golevelup/nestjs-stripe": "^0.6.4", "@golevelup/nestjs-webhooks": "0.2.17", "@nestjs-modules/mailer": "^1.11.2", "@nestjs/apollo": "^12.0.11", "@nestjs/axios": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.4", "@nestjs/graphql": "^12.0.11", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.0", "@prisma/client": "^6.2.1", "@types/cookie-parser": "^1.4.6", "agora-access-token": "^2.0.4", "apollo-server-express": "^3.13.0", "axios": "^1.7.2", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "firebase-admin": "^12.0.0", "fishery": "^2.2.2", "geolib": "^3.3.4", "graphql": "^16.8.1", "graphql-redis-subscriptions": "^2.6.0", "husky": "^8.0.0", "ioredis": "^5.3.2", "nodemailer": "^6.9.11", "pusher": "^5.2.0", "ramda": "^0.29.1", "ramda-adjunct": "^4.1.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "service": "^0.1.4", "sql-template-strings": "^2.2.2", "stripe": "11.14.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.2", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.14", "@types/ramda": "^0.29.12", "@types/stripe": "^8.0.417", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^6.2.1", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}