## Description

Bridge Backend version 2.0 built with NestJS + Prisma + PostgreSQL

## Installation

```bash
$ yarn install
```

## Running the app

```bash
# development
$ yarn run start

# watch mode
$ yarn run start:dev

# production mode
$ yarn run start:prod
```

## Test

```bash
# unit tests
$ yarn run test

# e2e tests
$ yarn run test:e2e

# test coverage
$ yarn run test:cov
```

## Prisma setup 
   1. Setup instructions for Prisma can be found [here](https://docs.nestjs.com/recipes/prisma#set-up-prisma)

[//]: # ($npm set-script prepare "" && npm ci --only=production)
### Read about auto generator
npm install --save-dev prisma-nestjs-graphql

### Read about creating stripe session url
https://stripe.com/docs/api/checkout/sessions/create?lang=node

test stripe triggers 
```bash
$ stripe trigger customer.subscription.updated
$ stripe trigger customer.subscription.cancelled
```

Add this to top of migration init file
```sql

CREATE EXTENSION IF NOT EXISTS citext;

CREATE EXTENSION IF NOT EXISTS pgcrypto;

CREATE EXTENSION IF NOT EXISTS postgis;

```

### Read about postgis extension in postgres
https://postgis.net/documentation/getting_started/
    
```sql
 CREATE EXTENSION postgis;
```

CREATE EXTENSION IF NOT EXISTS citext;

CREATE EXTENSION IF NOT EXISTS pgcrypto;

CREATE EXTENSION IF NOT EXISTS postgis;

principal://iam.googleapis.com/projects/803455956816/locations/global/workloadIdentityPools/bridge-backend-github/subject/SUBJECT_ATTRIBUTE_VALUE


Sample Raw SQL for JobAdvert with categories filtering out liked job adverts
```sql
SELECT
    "ja"."id" AS "jobAdvertId",
    "ja".*,
    json_agg(json_build_object('id', "jc"."id", 'name', "jc"."name")) AS categories,
    MAX(CAST("c"."id" AS TEXT)) AS "company_id",
    MAX("c"."name") AS "company_name",
    MAX("c"."address") AS "company_address",
    MAX("c"."logoImageUrl") AS "company_logoImageUrl",
    MAX("c"."headerImageUrl") AS "company_headerImageUrl",
    MAX("c"."detailContent") AS "company_detailContent",
    MAX("c"."foundingYear") AS "company_foundingYear",
    MAX("c"."totalEmployees") AS "company_totalEmployees",
    MAX("c"."city") AS "company_city",
    ROW_NUMBER() OVER(PARTITION BY "ja"."id" ORDER BY RANDOM()) as row_number
FROM (
    SELECT DISTINCT "ja"."id"
    FROM "JobAdvert" "ja"
    LEFT JOIN "_JobAdvertToJobCategory" "jatjc" ON "ja"."id" = "jatjc"."A"
    LEFT JOIN "JobCategory" "jc" ON "jatjc"."B" = "jc"."id"
    WHERE "ja"."active" = true AND "ja"."paused" = false AND "ja"."approved" = true
) filtered_ads
LEFT JOIN "JobAdvert" "ja" ON "ja"."id" = filtered_ads."id"
LEFT JOIN "_JobAdvertToJobCategory" "jatjc" ON "ja"."id" = "jatjc"."A"
LEFT JOIN "JobCategory" "jc" ON "jatjc"."B" = "jc"."id"
LEFT JOIN "Company" "c" ON "ja"."company_id" = "c"."id"
LEFT JOIN "JobAction" "jaction" ON "ja"."id" = "jaction"."job_advert_id"
WHERE NOT EXISTS (
    SELECT 1 FROM "JobAction" ja2
    WHERE ja2."job_advert_id" = "ja"."id"
    AND ja2."applicant_id" = '1811ea43-4df7-49d8-a60d-d8228b15d072'
    AND ja2."state" = 'LIKED'
)
GROUP BY "ja"."id"
ORDER BY RANDOM()
LIMIT 50;
```


# Models and DTOs

## Jobs and Fairs
### JobAdvert
- Model: Represents job advertisements with detailed information
- DTO: `CreateJobAdvertInput` - For creating new job listings with validation

### Fair
- Model: Represents career/job fairs with scheduling and location details
- DTO: `CreateFairInput` - For creating new fair events

### FairJob
- Model: Represents job position templates for fairs
- DTO: `CreateFairJobInput` - For creating basic job templates

### CompanyFairJob
- Model: Links companies' specific jobs to fairs
- DTO: `CreateCompanyFairJobInput` - For associating jobs with fair participants

## Contact Management
### ContactPerson
- Model: Represents company contact persons
- DTO: `CreateContactPersonInput` - For creating new contact persons

### CompanyFairContactPerson
- Model: Links contact persons to fair participations
- DTO: `CreateCompanyFairContactPersonInput` - For assigning contact persons to fairs

## Scheduling
### Timeslot
- Model: Represents available time slots for fairs
- DTO: `CreateTimeslotInput` - For creating fair schedule slots

### ContactPersonTimeslot
- Model: Represents contact person availability
- DTO: `CreateContactPersonTimeslotInput` - For setting contact person schedules

### Appointment
- Model: Represents booked appointments with contact persons
- DTO: `CreateAppointmentInput` - For creating appointment bookings

## Administration
### FGAdmin
- Model: Represents fair/job platform administrators
- DTO: `CreateFGAdminInput` - For creating admin accounts

## Participation
### CompanyFairParticipation
- Model: Represents company participation in fairs
- DTO: `CreateCompanyFairParticipationInput` - For registering companies in fairs