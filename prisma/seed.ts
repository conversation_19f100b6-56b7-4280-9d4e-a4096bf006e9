import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const seedData = [
    {
        name: '<PERSON><PERSON>, Architektur & Vermessung',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FBau%2C%20Architekt%26%20Vermessung%20%E2%80%93%201.png?alt=media&token=fab76a4e-73e4-48ee-8307-843f19e52956',
    },
    {
        name: 'Bil<PERSON><PERSON>, Pädagogik & Sprachen',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FBildung%20%26%20Pa%CC%88dagogik%20%E2%80%93%201.png?alt=media&token=64ce1bc2-556d-471a-bf01-9d5bb0693de5',
    },
    {
        name: '<PERSON><PERSON><PERSON>, Biologie & Ernährung',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FChemie%2C%20Bio%26%20Erna%CC%88hrung%20%E2%80%93%201.png?alt=media&token=ca31ef2c-7399-4a6b-916f-b1120bac1045',
    },
    {
        name: 'Design, Musik & Kunst',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FDesign%2C%20Musik%20%26%20Kunst%20%E2%80%93%201.png?alt=media&token=b3bc5e53-5f5b-487e-a08c-e83fc8dd8d7a',
    },
    {
        name: 'Garten, Landwirtschaft & Natur',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FLandwirschaft%2CNatur%20%26%20Garten%20%E2%80%93%201.png?alt=media&token=71b9f785-69d3-4cec-897f-d0b9dda3e03b',
    },
    {
        name: 'Handwerk, Holz & Produktion',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FHandwerk%2C%20Holz%20%26%20Produktion%20%E2%80%93%201.png?alt=media&token=d375c39b-d6f2-4424-bce3-552e3af74fd0',
    },
    {
        name: 'Hotel, Tourismus & Gastronomie',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FHotel%2C%20Tourismus%20%26Gastronomie%20%E2%80%93%201.png?alt=media&token=5bea166e-3755-4a01-9d31-16f20f6bec83',
    },
    {
        name: 'IT, Informatik & Elektrotechnik',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FIT%2C%20Informatik%20%26%20Technik%20%E2%80%93%201.png?alt=media&token=26c5fd5d-cda9-4ba6-9f77-0138d12f0020',
    },
    {
        name: 'Medien, Verlag & Druck',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FMedien%2C%20Verlag%26%20Druck%20%E2%80%93%201.png?alt=media&token=7c6e00de-9b24-4a90-acf9-bb90991ea7e5',
    },
    {
        name: 'Kaufmännisches, Handel & Vertrieb',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FHandel%2C%20Bu%CC%88ro%20%26%20Vertrieb%20%E2%80%93%201.png?alt=media&token=479b7b49-7462-4c0e-ac77-11f303928dad',
    },
    {
        name: 'Logistik, Transport & Verkehr',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FLogistik%20%26%20Transport%20%E2%80%93%201.png?alt=media&token=eba18325-c82d-4422-ae2b-f2fb12050d85',
    },
    {
        name: 'Medizin, Pflege & Gesundheit',
        imageUrl:
            'https://firebasestorage.googleapis.com/v0/b/bridge-public.appspot.com/o/jobCategories%2FMedizin%2C%20Pflege%26%20Gesundheit%20%E2%80%93%201.png?alt=media&token=19e3eef2-3d6a-4405-9351-d9188e0f41d9',
    },
]

async function main() {
    for (const category of seedData) {
        await prisma.jobCategory.create({
            data: category,
        })
    }
}

main()
    .catch((e) => {
        console.error(e)
        process.exit(1)
    })
    .finally(async () => {
        await prisma.$disconnect()
    })
