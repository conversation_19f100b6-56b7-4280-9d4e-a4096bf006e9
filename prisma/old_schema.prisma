// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  firebaseUid String       @unique
  status      Status?      @default(ACTIVE)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  companyUser CompanyUser?
  applicant   Applicant?
  superUser   SuperUser?
  FGAdmin     FGAdmin?
}

model SuperUser {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email          String?  @unique
  name           String?
  avatarImageUrl String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  user           User     @relation(fields: [userId], references: [id])
  userId         String   @unique @map("user_id") @db.Uuid
}

model CompanyUser {
  id              String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email           String?        @unique
  name            String?
  avatarImageUrl  String?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt
  activeCompanyId String?
  companies       Company[]
  user            User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  jobAdvert       JobAdvert[]
  userId          String         @unique @map("user_id") @db.Uuid
  messages        Message[]
  userRights      UserRights[]
  Notification    Notification[]
}

model Company {
  id                       String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  address                  String
  city                     String
  country                  String
  name                     String
  latitude                 Float?
  longitude                Float?
  detailContent            String?
  foundingYear             Int?
  headerImageUrl           String?
  logoImageUrl             String?
  totalEmployees           Int?
  dynamicLink              String?
  stripeCustomerId         String?
  isFairManaged            Boolean?                   @default(true)
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime                   @updatedAt
  companyUserId            String?                    @unique @map("company_user_id") @db.Uuid
  jobAdverts               JobAdvert[]
  subscriptions            StripeSubscription[]
  companyUsers             CompanyUser[]
  userRights               UserRights[]
  CompanyFairParticipation CompanyFairParticipation[]
  ContactPerson            ContactPerson[]
}

model Applicant {
  id                   String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user                 User                @relation(fields: [userId], references: [id])
  userId               String              @unique @map("user_id") @db.Uuid
  availableFrom        DateTime?
  birthDate            DateTime?
  city                 String?
  description          String?
  phoneNumber          String?
  graduation           String?
  environment          Int?
  firstName            String?
  lastName             String?
  lastActive           DateTime            @default(now())
  canLike              Boolean?            @default(false)
  profileImageUrl      String?
  personality          Int?
  receiveNotifications Boolean             @default(true)
  isDeleted            Boolean?            @default(false)
  deletedAt            DateTime?
  schoolName           String?
  strengths            String[]
  subjects             String[]
  weaknesses           String[]
  applicantDocuments   ApplicantDocument[]
  jobAction            JobAction[]
  jobsFilter           JobsFilter?
  deviceTokens         DeviceToken[]
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  messages             Message[]
  Notification         Notification[]
  appointment          Appointment[]

  @@index([userId])
}

model JobAdvert {
  id                String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title             String
  address           String?
  approved          Boolean?             @default(false)
  paused            Boolean?             @default(false)
  categories        JobCategory[]
  city              String
  latitude          Float?
  longitude         Float?
  geoLatitude       Float?
  geoLongitude      Float?
  isDeclined        Boolean?             @default(false) // TODO:: make it not nullable
  isDeleted         Boolean?             @default(false)
  isDraft           Boolean?             @default(false)
  deletedAt         DateTime?
  description       String
  declineReason     String?
  detailDescription String
  district          String
  educationDuration Int?
  headerImageUrl    String?
  dynamicLink       String?
  holidayDays       Int?
  impressions       Int?
  gehalt            Float[]
  workHours         Int?
  responsibleUsers  CompanyUser[]
  companyUserId     String?
  startDate         DateTime
  activeFromDate    DateTime?
  type              JobAdvertType
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  company           Company?             @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId         String?              @map("company_id") @db.Uuid
  subscriptions     StripeSubscription[]
  jobAction         JobAction[]
}

model JobAction {
  id                   String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  jobAdvert            JobAdvert          @relation(fields: [jobAdvertId], references: [id])
  jobAdvertId          String             @map("job_advert_id") @db.Uuid
  applicant            Applicant          @relation(fields: [applicantId], references: [id])
  applicantId          String             @map("applicant_id") @db.Uuid
  status               String?
  state                ActionState
  deletedFromCompany   Boolean?           @default(false)
  deletedFromApplicant Boolean?           @default(false)
  declineReason        String?
  companyIsNew         Boolean?
  applicantIsNew       Boolean?
  chatRoom             ChatRoom?          @relation("JobActionChatRoom", fields: [chatRoomId], references: [id])
  chatRoomId           String?            @unique @map("chat_room_id") @db.Uuid
  JobActionHistory     JobActionHistory[]

  @@unique([jobAdvertId, applicantId])
}

model JobCategory {
  id                       String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                     String
  imageUrl                 String
  jobAdverts               JobAdvert[]
  jobsFilters              JobsFilter[]
  jobsFilterId             String?
  CompanyFairParticipation CompanyFairParticipation[]
}

model StripeSubscription {
  id                   String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  plan                 PlanType  @default(PREMIUM)
  checkoutSessionId    String?
  amountTotal          Int?
  paymentStatus        String?
  status               String?
  currency             String?
  invoiceId            String?
  stripeCustomerId     String?
  stripeSubscriptionId String
  percent_off          Int?      @default(0)
  isActive             Boolean?  @default(false)
  cancelAtPeriodEnd    Boolean?  @default(false)
  jobAdvertId          String    @map("job_advert_id") @db.Uuid
  jobAdvert            JobAdvert @relation(fields: [jobAdvertId], references: [id], onDelete: Cascade)
  companyId            String?   @map("company_id") @db.Uuid
  company              Company?  @relation(fields: [companyId], references: [id], onDelete: Cascade)
  updatedAt            DateTime  @updatedAt
  createdAt            DateTime  @default(now())
  expiresAt            DateTime
}

model ApplicantDocument {
  id                 String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicant          Applicant? @relation(fields: [applicantId], references: [id])
  applicantId        String     @map("applicant_id") @db.Uuid
  documentPreviewUrl String?
  name               String?
  storage            String?
  url                String?
  createdAt          DateTime   @default(now())
  updatedAt          DateTime   @updatedAt
}

model DeviceToken {
  id          String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicant   Applicant? @relation(fields: [applicantId], references: [id])
  applicantId String     @map("applicant_id") @db.Uuid
  token       String?    @unique
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
}

model JobsFilter {
  id              String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicant       Applicant?      @relation(fields: [applicantId], references: [id])
  applicantId     String          @unique @map("applicant_id") @db.Uuid
  categories      JobCategory[]
  type            JobAdvertType[]
  latitude        Float?
  longitude       Float?
  radius          Int?
  currentLocation String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
}

model ChatRoom {
  id          String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  messages    Message[]
  status      String?
  jobAction   JobAction?   @relation("JobActionChatRoom")
  appointment Appointment? @relation("AppointmentChatRoom")
}

model Message {
  id                         String                    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  chatRoom                   ChatRoom                  @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)
  chatRoomId                 String                    @map("chat_room_id") @db.Uuid
  content                    String
  createdAt                  DateTime                  @default(now())
  updatedAt                  DateTime                  @updatedAt
  authorName                 String?
  companyUser                CompanyUser?              @relation(fields: [companyUserId], references: [id])
  companyUserId              String?                   @map("company_user_id") @db.Uuid
  companyFairContactPerson   CompanyFairContactPerson? @relation(fields: [companyFairContactPersonId], references: [id])
  companyFairContactPersonId String?                   @map("company_fair_contact_person_id") @db.Uuid
  applicant                  Applicant?                @relation(fields: [applicantId], references: [id])
  applicantId                String?                   @map("applicant_id") @db.Uuid
  isCompany                  Boolean?                  @default(false)
  isApplicant                Boolean?                  @default(false)
  isSeen                     Boolean?                  @default(false)
  isSent                     Boolean?                  @default(false)
  isDelivered                Boolean?                  @default(false)
  isDeleted                  Boolean?                  @default(false)
  deletedAt                  DateTime?
  deletedBy                  DeletedBy?
  deletedById                String?                   @map("deleted_by_id") @db.Uuid
}

model UserRights {
  id             String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  superAdmin     Boolean?    @default(false)
  companyAdmin   Boolean?    @default(false)
  createJobAd    Boolean?    @default(false)
  deleteJobAd    Boolean?    @default(false)
  updateJobAd    Boolean?    @default(false)
  editJobAd      Boolean?    @default(false)
  viewJobAd      Boolean?    @default(false)
  viewApplicants Boolean?    @default(false)
  editCompany    Boolean?    @default(false)
  createUser     Boolean?    @default(false)
  company        Company     @relation(fields: [companyId], references: [id], onDelete: Cascade)
  companyId      String      @map("company_id") @db.Uuid
  companyUser    CompanyUser @relation(fields: [companyUserId], references: [id])
  companyUserId  String      @map("company_user_id") @db.Uuid
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
}

model Notification {
  id                String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicant         Applicant?         @relation(fields: [applicantId], references: [id])
  applicantId       String?            @map("applicant_id") @db.Uuid
  companyUser       CompanyUser?       @relation(fields: [companyUserId], references: [id])
  companyUserId     String?            @map("company_user_id") @db.Uuid
  isNew             Boolean            @default(true)
  type              String?            @default("")
  pushNotification  PushNotification?
  emailNotification EmailNotification?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
}

model PushNotification {
  id             String       @id @default(uuid())
  notificationId String       @unique @map("notification_id") @db.Uuid
  title          String
  body           String
  actionUrl      String?
  notification   Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
}

model EmailNotification {
  id             String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  notificationId String       @unique @map("notification_id") @db.Uuid
  emailSubject   String
  emailBody      String
  notification   Notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
}

model JobActionHistory {
  id          String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  jobAction   JobAction @relation(fields: [jobActionId], references: [id], onDelete: Cascade)
  jobActionId String    @map("job_action_id") @db.Uuid
  prevState   String?
  newState    String?
  type        String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model Statistics {
  id               String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  createdAt        DateTime @default(now())
  companies        Int
  applicants       Int
  activeApplicants Int
  jobAdverts       Int
  activeJobAdverts Int
  likes            Int
  bookmarks        Int
  matches          Int
  chatMessages     Int
}

model StaleImage {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  url       String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// FAIR MODELS START

model FGAdmin {
  id             String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email          String       @unique
  name           String?
  type           FairUserType
  avatarImageUrl String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  user           User         @relation(fields: [userId], references: [id])
  userId         String       @unique @map("user_id") @db.Uuid
}

model Fair {
  id                        String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                      String
  startDate                 DateTime
  endDate                   DateTime
  registrationStartDate     DateTime
  registrationEndDate       DateTime
  city                      String
  location                  String
  locationName              String?
  status                    FairStatus
  description               String?
  logoImageUrl              String?
  contactPersonName         String?
  contactPersonEmail        String?
  publisherLogoImageUrl     String?
  publisherName             String?
  companyFairParticipations CompanyFairParticipation[]
  standardTimeslots         Timeslot[]
  createdAt                 DateTime                   @default(now())
  updatedAt                 DateTime                   @default(now()) @updatedAt
  fairDays                  FairDay[]
}

model CompanyFairParticipation {
  id                        String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  company                   Company                    @relation(fields: [companyId], references: [id])
  companyId                 String                     @map("company_id") @db.Uuid
  fair                      Fair                       @relation(fields: [fairId], references: [id])
  fairId                    String                     @map("fair_id") @db.Uuid
  categories                JobCategory[]
  partnerLinks              PartnerLink[]
  companyFairJobs           CompanyFairJob[]
  companyFairContactPersons CompanyFairContactPerson[]
  createdAt                 DateTime                   @default(now())
  updatedAt                 DateTime                   @default(now()) @updatedAt
}

model CompanyFairJob {
  id                         String                   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  companyFairParticipation   CompanyFairParticipation @relation(fields: [companyFairParticipationId], references: [id])
  companyFairParticipationId String                   @map("company_fair_participation_id") @db.Uuid
  description                String?
  fairJob                    FairJob                  @relation(fields: [fairJobId], references: [id])
  fairJobId                  String                   @map("fair_job_id") @db.Uuid
  createdAt                  DateTime                 @default(now())
  updatedAt                  DateTime                 @default(now()) @updatedAt
}

model FairJob {
  id              String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title           String
  companyFairJobs CompanyFairJob[]
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @default(now()) @updatedAt
}

model CompanyFairContactPerson {
  id                         String                   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  companyFairParticipation   CompanyFairParticipation @relation(fields: [companyFairParticipationId], references: [id])
  companyFairParticipationId String                   @map("company_fair_participation_id") @db.Uuid
  contactPerson              ContactPerson            @relation(fields: [contactPersonId], references: [id])
  contactPersonId            String                   @map("contact_person_id") @db.Uuid
  ContactPersonTimeslot      ContactPersonTimeslot[]
  createdAt                  DateTime                 @default(now())
  updatedAt                  DateTime                 @default(now()) @updatedAt
  messages                   Message[]
}

model ContactPerson {
  id                        String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                      String
  position                  String?
  email                     String?
  phone                     String?
  company                   Company                    @relation(fields: [companyId], references: [id])
  companyId                 String                     @map("company_id") @db.Uuid
  companyFairContactPersons CompanyFairContactPerson[]
  createdAt                 DateTime                   @default(now())
  updatedAt                 DateTime                   @default(now()) @updatedAt
}

model Timeslot {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  startTime DateTime @db.Timestamptz
  endTime   DateTime @db.Timestamptz
  fair      Fair     @relation(fields: [fairId], references: [id])
  fairId    String   @map("fair_id") @db.Uuid
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model FairDay {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  day       DateTime
  startTime DateTime @db.Timestamptz
  endTime   DateTime @db.Timestamptz
  fair      Fair     @relation(fields: [fairId], references: [id])
  fairId    String   @map("fair_id") @db.Uuid
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model ContactPersonTimeslot {
  id                         String                   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  companyFairContactPerson   CompanyFairContactPerson @relation(fields: [companyFairContactPersonId], references: [id])
  companyFairContactPersonId String                   @map("company_fair_contact_person_id") @db.Uuid
  startTime                  DateTime                 @db.Timestamptz
  endTime                    DateTime                 @db.Timestamptz
  available                  Boolean                  @default(true)
  createdAt                  DateTime                 @default(now())
  updatedAt                  DateTime                 @default(now()) @updatedAt
  Appointment                Appointment?
}

model Appointment {
  id                      String                @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicant               Applicant             @relation(fields: [applicantId], references: [id])
  applicantId             String                @map("applicant_id") @db.Uuid
  contactPersonTimeslot   ContactPersonTimeslot @relation(fields: [contactPersonTimeslotId], references: [id])
  contactPersonTimeslotId String                @unique @map("contact_person_timeslot_id") @db.Uuid
  status                  AppointmentStatus
  chatRoom                ChatRoom?             @relation("AppointmentChatRoom", fields: [chatRoomId], references: [id])
  chatRoomId              String?               @unique @map("chat_room_id") @db.Uuid
  applicantIsNew          Boolean               @default(true)
  companyIsNew            Boolean               @default(true)
  reservationDate         DateTime              @default(now()) @db.Timestamptz
  rejectReason            String?
  createdAt               DateTime              @default(now())
  updatedAt               DateTime              @default(now()) @updatedAt
}

model PartnerLink {
  id                        String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                      String
  url                       String
  companyFairParticipations CompanyFairParticipation[]
  createdAt                 DateTime                   @default(now())
  updatedAt                 DateTime                   @updatedAt
}

enum JobAdvertType {
  AUSBILDUNG
  PRAKTIKUM
}

enum DeletedBy {
  COMPANY
  APPLICANT
}

enum PlanType {
  BASIC
  PREMIUM
}

enum ActionState {
  BOOKMARKED
  LIKED
  MATCHED
  DISLIKED
  DELETED
}

enum AppointmentStatus {
  REQUESTED
  CONFIRMED
  REJECTED
  CANCELED
}

enum Status {
  ACTIVE
  INACTIVE
}

enum FairStatus {
  ACTIVE
  INACTIVE
}

enum FairUserType {
  SUPERUSER
  FGADMIN
}
