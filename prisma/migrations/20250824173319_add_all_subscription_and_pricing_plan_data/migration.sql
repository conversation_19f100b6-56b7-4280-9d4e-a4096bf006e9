-- CreateEnum
CREATE TYPE "SubscriptionType" AS ENUM ('PER_JOB_ADVERT', 'COMPANY_UNLIMITED');

-- CreateEnum
CREATE TYPE "BillingPeriod" AS ENUM ('MONTHLY', 'QUARTERLY', 'SEMI_ANNUAL', 'ANNUAL', 'ONE_TIME', 'CUSTOM');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "SubscriptionStatus" AS ENUM ('PENDING', 'ACTIVE', 'PAST_DUE', 'CANCELED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "PricingPlanType" AS ENUM ('STANDARD', 'PROMOTIONAL', 'CUSTOM', 'ENTERPRISE');

-- AlterTable
ALTER TABLE "StripeSubscription" ADD COLUMN     "canceledAt" TIMESTAMP(3),
ADD COLUMN     "currentPeriodEnd" TIMESTAMP(3),
ADD COLUMN     "currentPeriodStart" TIMESTAMP(3),
ADD COLUMN     "lastPaymentAmount" DOUBLE PRECISION,
ADD COLUMN     "lastPaymentDate" TIMESTAMP(3),
ADD COLUMN     "metadata" JSONB,
ADD COLUMN     "pricing_plan_id" UUID,
ADD COLUMN     "stripePriceId" TEXT,
ADD COLUMN     "subscriptionType" "SubscriptionType" NOT NULL DEFAULT 'COMPANY_UNLIMITED',
ALTER COLUMN "job_advert_id" DROP NOT NULL;

-- CreateTable
CREATE TABLE "PricingPlan" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "description" TEXT,
    "price" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "currency" TEXT NOT NULL DEFAULT 'EUR',
    "billingPeriod" "BillingPeriod" NOT NULL,
    "durationDays" INTEGER NOT NULL,
    "isPopular" BOOLEAN NOT NULL DEFAULT false,
    "isCustom" BOOLEAN NOT NULL DEFAULT false,
    "displayOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "unlimitedJobAdverts" BOOLEAN NOT NULL DEFAULT false,
    "hasCustomBranding" BOOLEAN NOT NULL DEFAULT false,
    "planType" "PricingPlanType" NOT NULL DEFAULT 'STANDARD',
    "stripeProductId" TEXT,
    "stripePriceId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PricingPlan_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompanySubscriptionConfig" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "company_id" UUID NOT NULL,
    "subscriptionType" "SubscriptionType" NOT NULL DEFAULT 'COMPANY_UNLIMITED',
    "notes" TEXT,
    "created_by" UUID,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CompanySubscriptionConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_CompanySubscriptionConfigToPricingPlan" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_CompanySubscriptionConfigToPricingPlan_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "CompanySubscriptionConfig_company_id_key" ON "CompanySubscriptionConfig"("company_id");

-- CreateIndex
CREATE INDEX "_CompanySubscriptionConfigToPricingPlan_B_index" ON "_CompanySubscriptionConfigToPricingPlan"("B");

-- AddForeignKey
ALTER TABLE "StripeSubscription" ADD CONSTRAINT "StripeSubscription_pricing_plan_id_fkey" FOREIGN KEY ("pricing_plan_id") REFERENCES "PricingPlan"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanySubscriptionConfig" ADD CONSTRAINT "CompanySubscriptionConfig_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompanySubscriptionConfigToPricingPlan" ADD CONSTRAINT "_CompanySubscriptionConfigToPricingPlan_A_fkey" FOREIGN KEY ("A") REFERENCES "CompanySubscriptionConfig"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompanySubscriptionConfigToPricingPlan" ADD CONSTRAINT "_CompanySubscriptionConfigToPricingPlan_B_fkey" FOREIGN KEY ("B") REFERENCES "PricingPlan"("id") ON DELETE CASCADE ON UPDATE CASCADE;
