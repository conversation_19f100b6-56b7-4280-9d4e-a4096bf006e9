/*
  Warnings:

  - You are about to drop the column `chat_room_id` on the `ContactPersonTimeslot` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[chat_room_id]` on the table `Appointment` will be added. If there are existing duplicate values, this will fail.

*/
-- DropF<PERSON><PERSON>Key
ALTER TABLE "ContactPersonTimeslot" DROP CONSTRAINT "ContactPersonTimeslot_chat_room_id_fkey";

-- DropIndex
DROP INDEX "ContactPersonTimeslot_chat_room_id_key";

-- AlterTable
ALTER TABLE "Appointment" ADD COLUMN     "chat_room_id" UUID;

-- AlterTable
ALTER TABLE "ChatRoom" ADD COLUMN     "appointment_id" UUID;

-- AlterTable
ALTER TABLE "ContactPersonTimeslot" DROP COLUMN "chat_room_id";

-- CreateIndex
CREATE UNIQUE INDEX "Appointment_chat_room_id_key" ON "Appointment"("chat_room_id");

-- AddF<PERSON>ignKey
ALTER TABLE "Appointment" ADD CONSTRAINT "Appointment_chat_room_id_fkey" FOREIGN KEY ("chat_room_id") REFERENCES "ChatRoom"("id") ON DELETE SET NULL ON UPDATE CASCADE;
