-- Create<PERSON><PERSON>
CREATE TYPE "AppointmentStatus" AS ENUM ('REQUESTED', 'CONFIRMED', 'REJECTED', 'CANCELED');

-- CreateEnum
CREATE TYPE "FairStatus" AS ENUM ('ACTIVE', 'INACTIVE');

-- CreateEnum
CREATE TYPE "FairUserType" AS ENUM ('SUPERUSER', 'FGADMIN');

-- DropForeignKey
ALTER TABLE "StripeSubscription" DROP CONSTRAINT "StripeSubscription_company_id_fkey";

-- DropForeignKey
ALTER TABLE "UserRights" DROP CONSTRAINT "UserRights_company_id_fkey";

-- CreateTable
CREATE TABLE "FGAdmin" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "email" TEXT,
    "name" TEXT,
    "type" "FairUserType" NOT NULL,
    "avatarImageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "user_id" UUID NOT NULL,

    CONSTRAINT "FGAdmin_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Fair" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "registrationStartDate" TIMESTAMP(3) NOT NULL,
    "registrationEndDate" TIMESTAMP(3) NOT NULL,
    "city" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "status" "FairStatus" NOT NULL,
    "description" TEXT,
    "logoImageUrl" TEXT,
    "contactPersonName" TEXT,
    "contactPersonEmail" TEXT,
    "publisherLogoImageUrl" TEXT,
    "publisherName" TEXT,

    CONSTRAINT "Fair_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompanyFairParticipation" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "company_id" UUID NOT NULL,
    "fair_id" UUID NOT NULL,

    CONSTRAINT "CompanyFairParticipation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompanyFairJob" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "company_fair_participation_id" UUID NOT NULL,
    "description" TEXT,
    "fair_job_id" UUID NOT NULL,

    CONSTRAINT "CompanyFairJob_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FairJob" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "title" TEXT NOT NULL,

    CONSTRAINT "FairJob_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompanyFairContactPerson" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "company_fair_participation_id" UUID NOT NULL,
    "contact_person_id" UUID NOT NULL,

    CONSTRAINT "CompanyFairContactPerson_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContactPerson" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "position" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "company_id" UUID NOT NULL,

    CONSTRAINT "ContactPerson_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Timeslot" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3) NOT NULL,
    "fair_id" UUID NOT NULL,

    CONSTRAINT "Timeslot_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContactPersonTimeslot" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "company_fair_contact_person_id" UUID NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3) NOT NULL,
    "available" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "ContactPersonTimeslot_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Appointment" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "applicant_id" UUID NOT NULL,
    "contact_person_timeslot_id" UUID NOT NULL,
    "status" "AppointmentStatus" NOT NULL,
    "reservationDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Appointment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "FGAdmin_email_key" ON "FGAdmin"("email");

-- CreateIndex
CREATE UNIQUE INDEX "FGAdmin_user_id_key" ON "FGAdmin"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "Appointment_contact_person_timeslot_id_key" ON "Appointment"("contact_person_timeslot_id");

-- AddForeignKey
ALTER TABLE "StripeSubscription" ADD CONSTRAINT "StripeSubscription_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRights" ADD CONSTRAINT "UserRights_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FGAdmin" ADD CONSTRAINT "FGAdmin_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyFairParticipation" ADD CONSTRAINT "CompanyFairParticipation_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyFairParticipation" ADD CONSTRAINT "CompanyFairParticipation_fair_id_fkey" FOREIGN KEY ("fair_id") REFERENCES "Fair"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyFairJob" ADD CONSTRAINT "CompanyFairJob_company_fair_participation_id_fkey" FOREIGN KEY ("company_fair_participation_id") REFERENCES "CompanyFairParticipation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyFairJob" ADD CONSTRAINT "CompanyFairJob_fair_job_id_fkey" FOREIGN KEY ("fair_job_id") REFERENCES "FairJob"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyFairContactPerson" ADD CONSTRAINT "CompanyFairContactPerson_company_fair_participation_id_fkey" FOREIGN KEY ("company_fair_participation_id") REFERENCES "CompanyFairParticipation"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyFairContactPerson" ADD CONSTRAINT "CompanyFairContactPerson_contact_person_id_fkey" FOREIGN KEY ("contact_person_id") REFERENCES "ContactPerson"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContactPerson" ADD CONSTRAINT "ContactPerson_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Timeslot" ADD CONSTRAINT "Timeslot_fair_id_fkey" FOREIGN KEY ("fair_id") REFERENCES "Fair"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContactPersonTimeslot" ADD CONSTRAINT "ContactPersonTimeslot_company_fair_contact_person_id_fkey" FOREIGN KEY ("company_fair_contact_person_id") REFERENCES "CompanyFairContactPerson"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Appointment" ADD CONSTRAINT "Appointment_applicant_id_fkey" FOREIGN KEY ("applicant_id") REFERENCES "Applicant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Appointment" ADD CONSTRAINT "Appointment_contact_person_timeslot_id_fkey" FOREIGN KEY ("contact_person_timeslot_id") REFERENCES "ContactPersonTimeslot"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
