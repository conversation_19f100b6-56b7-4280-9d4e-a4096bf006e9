-- AlterTable
ALTER TABLE "Company" ADD COLUMN     "isFairManaged" BOOLEAN DEFAULT false;

-- AlterTable
ALTER TABLE "_CompanyFairParticipationToJobCategory" ADD CONSTRAINT "_CompanyFairParticipationToJobCategory_AB_pkey" PRIMARY KEY ("A", "B");

-- DropIndex
DROP INDEX "_CompanyFairParticipationToJobCategory_AB_unique";

-- AlterTable
ALTER TABLE "_CompanyToCompanyUser" ADD CONSTRAINT "_CompanyToCompanyUser_AB_pkey" PRIMARY KEY ("A", "B");

-- DropIndex
DROP INDEX "_CompanyToCompanyUser_AB_unique";

-- AlterTable
ALTER TABLE "_CompanyUserToJobAdvert" ADD CONSTRAINT "_CompanyUserToJobAdvert_AB_pkey" PRIMARY KEY ("A", "B");

-- DropIndex
DROP INDEX "_CompanyUserToJobAdvert_AB_unique";

-- AlterTable
ALTER TABLE "_JobAdvertToJobCategory" ADD CONSTRAINT "_JobAdvertToJobCategory_AB_pkey" PRIMARY KEY ("A", "B");

-- DropIndex
DROP INDEX "_JobAdvertToJobCategory_AB_unique";

-- AlterTable
ALTER TABLE "_JobCategoryToJobsFilter" ADD CONSTRAINT "_JobCategoryToJobsFilter_AB_pkey" PRIMARY KEY ("A", "B");

-- DropIndex
DROP INDEX "_JobCategoryToJobsFilter_AB_unique";
