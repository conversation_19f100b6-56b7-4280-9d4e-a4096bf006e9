-- DropForeign<PERSON>ey
ALTER TABLE "StripeSubscription" DROP CONSTRAINT "StripeSubscription_company_id_fkey";

-- DropForeignKey
ALTER TABLE "UserRights" DROP CONSTRAINT "UserRights_company_id_fkey";

-- AlterTable
ALTER TABLE "StripeSubscription" ADD COLUMN     "percent_off" INTEGER DEFAULT 0;

-- AddForeignKey
ALTER TABLE "StripeSubscription" ADD CONSTRAINT "StripeSubscription_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRights" ADD CONSTRAINT "UserRights_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;
