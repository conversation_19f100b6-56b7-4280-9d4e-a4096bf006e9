/*
  Warnings:

  - A unique constraint covering the columns `[chat_room_id]` on the table `ContactPersonTimeslot` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "ContactPersonTimeslot" ADD COLUMN     "chat_room_id" UUID;

-- CreateIndex
CREATE UNIQUE INDEX "ContactPersonTimeslot_chat_room_id_key" ON "ContactPersonTimeslot"("chat_room_id");

-- AddForeignKey
ALTER TABLE "ContactPersonTimeslot" ADD CONSTRAINT "ContactPersonTimeslot_chat_room_id_fkey" FOREIGN KEY ("chat_room_id") REFERENCES "ChatRoom"("id") ON DELETE SET NULL ON UPDATE CASCADE;
