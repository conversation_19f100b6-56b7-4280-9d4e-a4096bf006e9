/*
  Warnings:

  - You are about to drop the column `appointment_id` on the `ChatRoom` table. All the data in the column will be lost.
  - You are about to drop the `_ChatRoomToCompanyUser` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "_ChatRoomToCompanyUser" DROP CONSTRAINT "_ChatRoomToCompanyUser_A_fkey";

-- DropForeignKey
ALTER TABLE "_ChatRoomToCompanyUser" DROP CONSTRAINT "_ChatRoomToCompanyUser_B_fkey";

-- AlterTable
ALTER TABLE "ChatRoom" DROP COLUMN "appointment_id";

-- AlterTable
ALTER TABLE "CompanyUser" ADD COLUMN     "chatRoomId" UUID;

-- DropTable
DROP TABLE "_ChatRoomToCompanyUser";

-- AddForeignKey
ALTER TABLE "CompanyUser" ADD CONSTRAINT "CompanyUser_chatRoomId_fkey" FOREIGN KEY ("chatRoomId") REFERENCES "ChatRoom"("id") ON DELETE SET NULL ON UPDATE CASCADE;
