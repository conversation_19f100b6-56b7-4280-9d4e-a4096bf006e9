CREATE EXTENSION IF NOT EXISTS postgis;

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "Status" AS ENUM ('ACTIVE', 'INACTIVE');

-- C<PERSON><PERSON>num
CREATE TYPE "JobAdvertType" AS ENUM ('AUSBILDUNG', 'PRAKTIKUM');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "DeletedBy" AS ENUM ('COMPANY', 'APPLICANT');

-- <PERSON>reate<PERSON>num
CREATE TYPE "PlanType" AS ENUM ('BASIC', 'PREMIUM');

-- CreateEnum
CREATE TYPE "ActionState" AS ENUM ('BOOKMARKED', 'LIKED', 'MATCHED', 'DISLIKED', 'DELETED');

-- CreateTable
CREATE TABLE "User" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "firebaseUid" TEXT NOT NULL,
    "status" "Status" DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SuperUser" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "email" TEXT,
    "name" TEXT,
    "avatarImageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "user_id" UUID NOT NULL,

    CONSTRAINT "SuperUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CompanyUser" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "email" TEXT,
    "name" TEXT,
    "avatarImageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "activeCompanyId" TEXT,
    "user_id" UUID NOT NULL,

    CONSTRAINT "CompanyUser_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Company" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "detailContent" TEXT,
    "foundingYear" INTEGER,
    "headerImageUrl" TEXT,
    "logoImageUrl" TEXT,
    "totalEmployees" INTEGER,
    "dynamicLink" TEXT,
    "stripeCustomerId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "company_user_id" UUID,

    CONSTRAINT "Company_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Applicant" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "availableFrom" TIMESTAMP(3),
    "birthDate" TIMESTAMP(3),
    "city" TEXT,
    "description" TEXT,
    "phoneNumber" TEXT,
    "graduation" TEXT,
    "environment" INTEGER,
    "firstName" TEXT,
    "lastName" TEXT,
    "lastActive" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "canLike" BOOLEAN DEFAULT false,
    "profileImageUrl" TEXT,
    "personality" INTEGER,
    "receiveNotifications" BOOLEAN NOT NULL DEFAULT true,
    "isDeleted" BOOLEAN DEFAULT false,
    "deletedAt" TIMESTAMP(3),
    "schoolName" TEXT,
    "strengths" TEXT[],
    "subjects" TEXT[],
    "weaknesses" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "chatRoomId" UUID,

    CONSTRAINT "Applicant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobAdvert" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "title" TEXT NOT NULL,
    "address" TEXT,
    "approved" BOOLEAN DEFAULT false,
    "paused" BOOLEAN DEFAULT false,
    "city" TEXT NOT NULL,
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "geoLatitude" DOUBLE PRECISION,
    "geoLongitude" DOUBLE PRECISION,
    "isDeclined" BOOLEAN DEFAULT false,
    "isDeleted" BOOLEAN DEFAULT false,
    "isDraft" BOOLEAN DEFAULT false,
    "deletedAt" TIMESTAMP(3),
    "description" TEXT NOT NULL,
    "declineReason" TEXT,
    "detailDescription" TEXT NOT NULL,
    "district" TEXT NOT NULL,
    "educationDuration" INTEGER,
    "headerImageUrl" TEXT,
    "dynamicLink" TEXT,
    "holidayDays" INTEGER,
    "impressions" INTEGER,
    "gehalt" DOUBLE PRECISION[],
    "workHours" INTEGER,
    "companyUserId" TEXT,
    "startDate" TIMESTAMP(3) NOT NULL,
    "activeFromDate" TIMESTAMP(3),
    "type" "JobAdvertType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "company_id" UUID,

    CONSTRAINT "JobAdvert_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobAction" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "job_advert_id" UUID NOT NULL,
    "applicant_id" UUID NOT NULL,
    "status" TEXT,
    "state" "ActionState" NOT NULL,
    "deletedFromCompany" BOOLEAN DEFAULT false,
    "deletedFromApplicant" BOOLEAN DEFAULT false,
    "declineReason" TEXT,
    "companyIsNew" BOOLEAN,
    "applicantIsNew" BOOLEAN,
    "chat_room_id" UUID,

    CONSTRAINT "JobAction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobCategory" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "jobsFilterId" TEXT,

    CONSTRAINT "JobCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StripeSubscription" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "plan" "PlanType" NOT NULL DEFAULT 'PREMIUM',
    "checkoutSessionId" TEXT,
    "amountTotal" INTEGER,
    "paymentStatus" TEXT,
    "status" TEXT,
    "currency" TEXT,
    "invoiceId" TEXT,
    "stripeCustomerId" TEXT,
    "stripeSubscriptionId" TEXT NOT NULL,
    "isActive" BOOLEAN DEFAULT false,
    "cancelAtPeriodEnd" BOOLEAN DEFAULT false,
    "job_advert_id" UUID NOT NULL,
    "company_id" UUID,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StripeSubscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ApplicantDocument" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "applicant_id" UUID NOT NULL,
    "documentPreviewUrl" TEXT,
    "name" TEXT,
    "storage" TEXT,
    "url" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ApplicantDocument_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DeviceToken" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "applicant_id" UUID NOT NULL,
    "token" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DeviceToken_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobsFilter" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "applicant_id" UUID NOT NULL,
    "type" "JobAdvertType"[],
    "latitude" DOUBLE PRECISION,
    "longitude" DOUBLE PRECISION,
    "radius" INTEGER,
    "currentLocation" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobsFilter_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChatRoom" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "status" TEXT,

    CONSTRAINT "ChatRoom_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Message" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "chat_room_id" UUID NOT NULL,
    "content" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "authorName" TEXT,
    "company_user_id" UUID,
    "applicant_id" UUID,
    "isCompany" BOOLEAN DEFAULT false,
    "isApplicant" BOOLEAN DEFAULT false,
    "isSeen" BOOLEAN DEFAULT false,
    "isSent" BOOLEAN DEFAULT false,
    "isDelivered" BOOLEAN DEFAULT false,
    "isDeleted" BOOLEAN DEFAULT false,
    "deletedAt" TIMESTAMP(3),
    "deletedBy" "DeletedBy",
    "deleted_by_id" UUID,

    CONSTRAINT "Message_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserRights" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "superAdmin" BOOLEAN DEFAULT false,
    "companyAdmin" BOOLEAN DEFAULT false,
    "createJobAd" BOOLEAN DEFAULT false,
    "deleteJobAd" BOOLEAN DEFAULT false,
    "updateJobAd" BOOLEAN DEFAULT false,
    "editJobAd" BOOLEAN DEFAULT false,
    "viewJobAd" BOOLEAN DEFAULT false,
    "viewApplicants" BOOLEAN DEFAULT false,
    "editCompany" BOOLEAN DEFAULT false,
    "createUser" BOOLEAN DEFAULT false,
    "company_id" UUID NOT NULL,
    "company_user_id" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserRights_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Notification" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "applicant_id" UUID,
    "company_user_id" UUID,
    "isNew" BOOLEAN NOT NULL DEFAULT true,
    "type" TEXT DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PushNotification" (
    "id" TEXT NOT NULL,
    "notification_id" UUID NOT NULL,
    "title" TEXT NOT NULL,
    "body" TEXT NOT NULL,
    "actionUrl" TEXT,

    CONSTRAINT "PushNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmailNotification" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "notification_id" UUID NOT NULL,
    "emailSubject" TEXT NOT NULL,
    "emailBody" TEXT NOT NULL,

    CONSTRAINT "EmailNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobActionHistory" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "job_action_id" UUID NOT NULL,
    "prevState" TEXT,
    "newState" TEXT,
    "type" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobActionHistory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Statistics" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "companies" INTEGER NOT NULL,
    "applicants" INTEGER NOT NULL,
    "activeApplicants" INTEGER NOT NULL,
    "jobAdverts" INTEGER NOT NULL,
    "activeJobAdverts" INTEGER NOT NULL,
    "likes" INTEGER NOT NULL,
    "bookmarks" INTEGER NOT NULL,
    "matches" INTEGER NOT NULL,
    "chatMessages" INTEGER NOT NULL,

    CONSTRAINT "Statistics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "StaleImage" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "url" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StaleImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_CompanyUserToJobAdvert" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL
);

-- CreateTable
CREATE TABLE "_CompanyToCompanyUser" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL
);

-- CreateTable
CREATE TABLE "_JobAdvertToJobCategory" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL
);

-- CreateTable
CREATE TABLE "_JobCategoryToJobsFilter" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL
);

-- CreateTable
CREATE TABLE "_ChatRoomToCompanyUser" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "User_firebaseUid_key" ON "User"("firebaseUid");

-- CreateIndex
CREATE UNIQUE INDEX "SuperUser_email_key" ON "SuperUser"("email");

-- CreateIndex
CREATE UNIQUE INDEX "SuperUser_user_id_key" ON "SuperUser"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "CompanyUser_email_key" ON "CompanyUser"("email");

-- CreateIndex
CREATE UNIQUE INDEX "CompanyUser_user_id_key" ON "CompanyUser"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "Company_company_user_id_key" ON "Company"("company_user_id");

-- CreateIndex
CREATE UNIQUE INDEX "Applicant_user_id_key" ON "Applicant"("user_id");

-- CreateIndex
CREATE INDEX "Applicant_user_id_idx" ON "Applicant"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "JobAction_chat_room_id_key" ON "JobAction"("chat_room_id");

-- CreateIndex
CREATE UNIQUE INDEX "JobAction_job_advert_id_applicant_id_key" ON "JobAction"("job_advert_id", "applicant_id");

-- CreateIndex
CREATE UNIQUE INDEX "StripeSubscription_job_advert_id_key" ON "StripeSubscription"("job_advert_id");

-- CreateIndex
CREATE UNIQUE INDEX "DeviceToken_token_key" ON "DeviceToken"("token");

-- CreateIndex
CREATE UNIQUE INDEX "JobsFilter_applicant_id_key" ON "JobsFilter"("applicant_id");

-- CreateIndex
CREATE UNIQUE INDEX "PushNotification_notification_id_key" ON "PushNotification"("notification_id");

-- CreateIndex
CREATE UNIQUE INDEX "EmailNotification_notification_id_key" ON "EmailNotification"("notification_id");

-- CreateIndex
CREATE UNIQUE INDEX "_CompanyUserToJobAdvert_AB_unique" ON "_CompanyUserToJobAdvert"("A", "B");

-- CreateIndex
CREATE INDEX "_CompanyUserToJobAdvert_B_index" ON "_CompanyUserToJobAdvert"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_CompanyToCompanyUser_AB_unique" ON "_CompanyToCompanyUser"("A", "B");

-- CreateIndex
CREATE INDEX "_CompanyToCompanyUser_B_index" ON "_CompanyToCompanyUser"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_JobAdvertToJobCategory_AB_unique" ON "_JobAdvertToJobCategory"("A", "B");

-- CreateIndex
CREATE INDEX "_JobAdvertToJobCategory_B_index" ON "_JobAdvertToJobCategory"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_JobCategoryToJobsFilter_AB_unique" ON "_JobCategoryToJobsFilter"("A", "B");

-- CreateIndex
CREATE INDEX "_JobCategoryToJobsFilter_B_index" ON "_JobCategoryToJobsFilter"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_ChatRoomToCompanyUser_AB_unique" ON "_ChatRoomToCompanyUser"("A", "B");

-- CreateIndex
CREATE INDEX "_ChatRoomToCompanyUser_B_index" ON "_ChatRoomToCompanyUser"("B");

-- AddForeignKey
ALTER TABLE "SuperUser" ADD CONSTRAINT "SuperUser_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CompanyUser" ADD CONSTRAINT "CompanyUser_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Applicant" ADD CONSTRAINT "Applicant_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobAdvert" ADD CONSTRAINT "JobAdvert_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobAction" ADD CONSTRAINT "JobAction_job_advert_id_fkey" FOREIGN KEY ("job_advert_id") REFERENCES "JobAdvert"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobAction" ADD CONSTRAINT "JobAction_applicant_id_fkey" FOREIGN KEY ("applicant_id") REFERENCES "Applicant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobAction" ADD CONSTRAINT "JobAction_chat_room_id_fkey" FOREIGN KEY ("chat_room_id") REFERENCES "ChatRoom"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StripeSubscription" ADD CONSTRAINT "StripeSubscription_job_advert_id_fkey" FOREIGN KEY ("job_advert_id") REFERENCES "JobAdvert"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StripeSubscription" ADD CONSTRAINT "StripeSubscription_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApplicantDocument" ADD CONSTRAINT "ApplicantDocument_applicant_id_fkey" FOREIGN KEY ("applicant_id") REFERENCES "Applicant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DeviceToken" ADD CONSTRAINT "DeviceToken_applicant_id_fkey" FOREIGN KEY ("applicant_id") REFERENCES "Applicant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobsFilter" ADD CONSTRAINT "JobsFilter_applicant_id_fkey" FOREIGN KEY ("applicant_id") REFERENCES "Applicant"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_chat_room_id_fkey" FOREIGN KEY ("chat_room_id") REFERENCES "ChatRoom"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_company_user_id_fkey" FOREIGN KEY ("company_user_id") REFERENCES "CompanyUser"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_applicant_id_fkey" FOREIGN KEY ("applicant_id") REFERENCES "Applicant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRights" ADD CONSTRAINT "UserRights_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRights" ADD CONSTRAINT "UserRights_company_user_id_fkey" FOREIGN KEY ("company_user_id") REFERENCES "CompanyUser"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_applicant_id_fkey" FOREIGN KEY ("applicant_id") REFERENCES "Applicant"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_company_user_id_fkey" FOREIGN KEY ("company_user_id") REFERENCES "CompanyUser"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PushNotification" ADD CONSTRAINT "PushNotification_notification_id_fkey" FOREIGN KEY ("notification_id") REFERENCES "Notification"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmailNotification" ADD CONSTRAINT "EmailNotification_notification_id_fkey" FOREIGN KEY ("notification_id") REFERENCES "Notification"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobActionHistory" ADD CONSTRAINT "JobActionHistory_job_action_id_fkey" FOREIGN KEY ("job_action_id") REFERENCES "JobAction"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompanyUserToJobAdvert" ADD CONSTRAINT "_CompanyUserToJobAdvert_A_fkey" FOREIGN KEY ("A") REFERENCES "CompanyUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompanyUserToJobAdvert" ADD CONSTRAINT "_CompanyUserToJobAdvert_B_fkey" FOREIGN KEY ("B") REFERENCES "JobAdvert"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompanyToCompanyUser" ADD CONSTRAINT "_CompanyToCompanyUser_A_fkey" FOREIGN KEY ("A") REFERENCES "Company"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompanyToCompanyUser" ADD CONSTRAINT "_CompanyToCompanyUser_B_fkey" FOREIGN KEY ("B") REFERENCES "CompanyUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_JobAdvertToJobCategory" ADD CONSTRAINT "_JobAdvertToJobCategory_A_fkey" FOREIGN KEY ("A") REFERENCES "JobAdvert"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_JobAdvertToJobCategory" ADD CONSTRAINT "_JobAdvertToJobCategory_B_fkey" FOREIGN KEY ("B") REFERENCES "JobCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_JobCategoryToJobsFilter" ADD CONSTRAINT "_JobCategoryToJobsFilter_A_fkey" FOREIGN KEY ("A") REFERENCES "JobCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_JobCategoryToJobsFilter" ADD CONSTRAINT "_JobCategoryToJobsFilter_B_fkey" FOREIGN KEY ("B") REFERENCES "JobsFilter"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ChatRoomToCompanyUser" ADD CONSTRAINT "_ChatRoomToCompanyUser_A_fkey" FOREIGN KEY ("A") REFERENCES "ChatRoom"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_ChatRoomToCompanyUser" ADD CONSTRAINT "_ChatRoomToCompanyUser_B_fkey" FOREIGN KEY ("B") REFERENCES "CompanyUser"("id") ON DELETE CASCADE ON UPDATE CASCADE;
