-- CreateTable
CREATE TABLE "PartnerLink" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PartnerLink_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_CompanyFairParticipationToPartnerLink" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL,

    CONSTRAINT "_CompanyFairParticipationToPartnerLink_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_CompanyFairParticipationToPartnerLink_B_index" ON "_CompanyFairParticipationToPartnerLink"("B");

-- AddForeignKey
ALTER TABLE "_CompanyFairParticipationToPartnerLink" ADD CONSTRAINT "_CompanyFairParticipationToPartnerLink_A_fkey" FOREIGN KEY ("A") REFERENCES "CompanyFairParticipation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompanyFairParticipationToPartnerLink" ADD CONSTRAINT "_CompanyFairParticipationToPartnerLink_B_fkey" FOREIGN KEY ("B") REFERENCES "PartnerLink"("id") ON DELETE CASCADE ON UPDATE CASCADE;
