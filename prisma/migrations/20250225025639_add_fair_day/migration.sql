-- CreateTable
CREATE TABLE "FairDay" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "day" TIMESTAMP(3) NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3) NOT NULL,
    "fair_id" UUID NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FairDay_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "FairDay" ADD CONSTRAINT "FairDay_fair_id_fkey" FOREIGN KEY ("fair_id") REFERENCES "Fair"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
