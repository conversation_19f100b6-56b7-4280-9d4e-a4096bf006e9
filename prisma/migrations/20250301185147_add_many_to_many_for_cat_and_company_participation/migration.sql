/*
  Warnings:

  - You are about to drop the column `companyFairParticipationId` on the `JobCategory` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "JobCategory" DROP CONSTRAINT "JobCategory_companyFairParticipationId_fkey";

-- AlterTable
ALTER TABLE "JobCategory" DROP COLUMN "companyFairParticipationId";

-- CreateTable
CREATE TABLE "_CompanyFairParticipationToJobCategory" (
    "A" UUID NOT NULL,
    "B" UUID NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_CompanyFairParticipationToJobCategory_AB_unique" ON "_CompanyFairParticipationToJobCategory"("A", "B");

-- CreateIndex
CREATE INDEX "_CompanyFairParticipationToJobCategory_B_index" ON "_CompanyFairParticipationToJobCategory"("B");

-- AddF<PERSON>ignKey
ALTER TABLE "_CompanyFairParticipationToJobCategory" ADD CONSTRAINT "_CompanyFairParticipationToJobCategory_A_fkey" FOREIGN KEY ("A") REFERENCES "CompanyFairParticipation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_CompanyFairParticipationToJobCategory" ADD CONSTRAINT "_CompanyFairParticipationToJobCategory_B_fkey" FOREIGN KEY ("B") REFERENCES "JobCategory"("id") ON DELETE CASCADE ON UPDATE CASCADE;
