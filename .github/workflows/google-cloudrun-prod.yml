name: Deploy to Cloud Run for production

on:
  push:
    branches: [ "production" ]

env:
  PROJECT_ID: bridge-public # TODO: update Google Cloud project id
  SERVICE: bridge-backend-prod-api # TODO: update Cloud Run service name
  REGION: europe-west3 # TODO: update Cloud Run service region

jobs:
  deploy:
    # Add 'id-token' with the intended permissions for workload identity federation
    permissions:
      contents: 'read'
      id-token: 'write'

    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      # - name: Google Auth
      #   id: auth
      #   uses: 'google-github-actions/auth@v2.0.0'
      #   with:
      #     #workload_identity_provider: '${{ secrets.WIF_PROVIDER }}' # e.g. - projects/123456789/locations/global/workloadIdentityPools/my-pool/providers/my-provider
