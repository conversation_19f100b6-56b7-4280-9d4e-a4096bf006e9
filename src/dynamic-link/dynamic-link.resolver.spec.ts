import { Test, TestingModule } from '@nestjs/testing'
import { DynamicLinkResolver } from './dynamic-link.resolver'
import { DynamicLinkService } from './dynamic-link.service'

describe('DynamicLinkResolver', () => {
    let resolver: DynamicLinkResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [DynamicLinkResolver, DynamicLinkService],
        }).compile()

        resolver = module.get<DynamicLinkResolver>(DynamicLinkResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
