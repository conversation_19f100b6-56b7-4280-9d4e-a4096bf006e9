import { Injectable } from '@nestjs/common'
import { CreateCompanyDynamicLinkInput } from './dto/create-company-dynamic-link.input'
import axios from 'axios'
import { DynamicLink } from './entities/dynamic-link.entity'
import { PrismaService } from '../prisma.service'
import { CreateJobAdDynamicLinkInput } from './dto/create-jobad-dynamic-link.input'
import configs from '../config'

@Injectable()
export class DynamicLinkService {
    constructor(private readonly prisma: PrismaService) {}

    async getCompanyDynamicLink(
        createDynamicLinkInput: CreateCompanyDynamicLinkInput
    ): Promise<DynamicLink> {
        const linkPath = `/company/${createDynamicLinkInput.companyId}`

        //check if company with id exists and has no dynamicLink
        const company = await this.prisma.company.findUnique({
            where: {
                id: createDynamicLinkInput.companyId,
            },
        })
        if (!company) {
            throw new Error('Company not found')
        } else {
            if (company.dynamicLink) {
                return { url: company.dynamicLink }
            }
        }

        const generatedLink = await this.createDynamicLink(linkPath)

        await this.prisma.company.update({
            where: {
                id: createDynamicLinkInput.companyId,
            },
            data: {
                dynamicLink: generatedLink?.url,
            },
        })
        return generatedLink
    }

    async getJobAdDynamicLink(
        createDynamicLinkInput: CreateJobAdDynamicLinkInput
    ): Promise<DynamicLink> {
        const linkPath = `/jobAd/${createDynamicLinkInput.jobAdvertId}`

        //check if company with id exists and has no dynamicLink
        const jobAdvert = await this.prisma.jobAdvert.findUnique({
            where: {
                id: createDynamicLinkInput.jobAdvertId,
            },
        })
        if (!jobAdvert) {
            throw new Error('JobAdvert not found')
        } else {
            if (jobAdvert.dynamicLink) {
                return { url: jobAdvert.dynamicLink }
            }
        }

        const generatedLink = await this.createDynamicLink(linkPath)

        await this.prisma.jobAdvert.update({
            where: {
                id: createDynamicLinkInput.jobAdvertId,
            },
            data: {
                dynamicLink: generatedLink?.url,
            },
        })
        return generatedLink
    }

    private async createDynamicLink(linkPath: string): Promise<DynamicLink> {
        const response = await axios.post<{ url: string }>(
            'https://api2.branch.io/v1/url',
            {
                branch_key: configs().BRANCH_KEY,
                data: {
                    $deeplink_path: linkPath,
                    $desktop_url: 'https://bridge-app.de',
                },
            }
        )

        return response.data
    }
}
