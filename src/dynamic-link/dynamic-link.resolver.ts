import { Resolver, Mu<PERSON>, Args } from '@nestjs/graphql'
import { DynamicLinkService } from './dynamic-link.service'
import { DynamicLink } from './entities/dynamic-link.entity'
import { CreateCompanyDynamicLinkInput } from './dto/create-company-dynamic-link.input'
import { CreateJobAdDynamicLinkInput } from './dto/create-jobad-dynamic-link.input'
import { UseGuards } from '@nestjs/common'
import { CompanyGuard } from '../guards/company-guard'

@Resolver(() => DynamicLink)
export class DynamicLinkResolver {
    constructor(private readonly dynamicLinkService: DynamicLinkService) {}

    @UseGuards(CompanyGuard)
    @Mutation(() => DynamicLink)
    createCompanyDynamicLink(
        @Args('createDynamicLinkInput')
        createDynamicLinkInput: CreateCompanyDynamicLinkInput
    ) {
        return this.dynamicLinkService.getCompanyDynamicLink(
            createDynamicLinkInput
        )
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => DynamicLink)
    createJobAdDynamicLink(
        @Args('createDynamicLinkInput')
        createDynamicLinkInput: CreateJobAdDynamicLinkInput
    ) {
        return this.dynamicLinkService.getJobAdDynamicLink(
            createDynamicLinkInput
        )
    }
}
