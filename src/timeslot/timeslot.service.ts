import {
    Injectable,
    NotFoundException,
    BadRequestException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateTimeslotInput } from './dto/create-timeslot.input'
import { UpdateTimeslotInput } from './dto/update-timeslot.input'

@Injectable()
export class TimeslotService {
    constructor(private readonly prisma: PrismaService) { }

    async create(createTimeslotInput: CreateTimeslotInput) {
        const { fairId, startTime, endTime } = createTimeslotInput

        const fair = await this.prisma.fair.findUnique({
            where: { id: fairId },
        })

        if (!fair) {
            throw new NotFoundException('Fair not found')
        }

        if (new Date(startTime) >= new Date(endTime)) {
            throw new BadRequestException('Start time must be before end time')
        }

        // const overlapping = await this.findOverlappingSlots(
        //     fairId,
        //     startTime,
        //     endTime
        // )
        // if (overlapping.length > 0) {
        //     throw new BadRequestException(
        //         'Time slot overlaps with existing slots'
        //     )
        // }

        return this.prisma.timeslot.create({
            data: {
                startTime: new Date(startTime),
                endTime: new Date(endTime),
                fair: {
                    connect: { id: fairId },
                },
            },
            include: {
                fair: true,
            },
        })
    }

    async findAll() {
        return this.prisma.timeslot.findMany()
    }

    async findOne(id: string) {
        const timeslot = await this.prisma.timeslot.findUnique({
            where: { id },
            include: {
                fair: true,
            },
        })

        if (!timeslot) {
            throw new NotFoundException('Timeslot not found')
        }

        return timeslot
    }

    async update(id: string, updateTimeslotInput: UpdateTimeslotInput) {
        const { startTime, endTime } = updateTimeslotInput

        const currentSlot = await this.findOne(id)

        if (startTime && endTime && new Date(startTime) >= new Date(endTime)) {
            throw new BadRequestException('Start time must be before end time')
        }

        // Check for overlapping slots if times are being updated
        // if (startTime || endTime) {
        //     const overlapping = await this.findOverlappingSlots(
        //         currentSlot.fairId,
        //         startTime || (currentSlot.startTime as Date),
        //         endTime || (currentSlot.endTime as Date),
        //         id
        //     )
        //
        //     if (overlapping.length > 0) {
        //         throw new BadRequestException(
        //             'Time slot overlaps with existing slots'
        //         )
        //     }
        // }

        return this.prisma.timeslot.update({
            where: { id },
            data: {
                startTime: startTime ? new Date(startTime) : undefined,
                endTime: endTime ? new Date(endTime) : undefined,
            },
            include: {
                fair: true,
            },
        })
    }

    async remove(id: string) {
        await this.findOne(id)

        return this.prisma.timeslot.delete({
            where: { id },
        })
    }

    async removeMultiple(ids: string[]): Promise<number> {
        const result = await this.prisma.timeslot.deleteMany({
            where: {
                id: {
                    in: ids,
                },
            },
        })
        return result.count
    }

    async findByFair(fairId: string) {
        return this.prisma.timeslot.findMany({
            where: { fairId },
            include: { fair: true },
        })
    }

    async findByDateRange(startDate: Date, endDate: Date) {
        return this.prisma.timeslot.findMany({
            where: {
                startTime: {
                    gte: startDate,
                },
                endTime: {
                    lte: endDate,
                },
            },
            include: {
                fair: true,
            },
        })
    }

    private async findOverlappingSlots(
        fairId: string | undefined | null | any,
        startTime: Date | string,
        endTime: Date | string,
        excludeId?: string
    ) {
        const start = new Date(startTime)
        const end = new Date(endTime)

        const overlap = await this.prisma.timeslot.findMany({
            where: {
                fairId,
                NOT: excludeId ? { id: excludeId } : undefined,
                OR: [
                    {
                        startTime: {
                            lte: end,
                        },
                        endTime: {
                            gte: start,
                        },
                    },
                ],
            },
        })
        return overlap
    }
}
