import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql'
import { TimeslotService } from './timeslot.service'
import { Timeslot } from './entities/timeslot.entity'
import { CreateTimeslotInput } from './dto/create-timeslot.input'
import { UpdateTimeslotInput } from './dto/update-timeslot.input'

@Resolver(() => Timeslot)
export class TimeslotResolver {
    constructor(private readonly timeslotService: TimeslotService) { }

    @Mutation(() => Timeslot)
    createTimeslot(
        @Args('createTimeslotInput') createTimeslotInput: CreateTimeslotInput
    ) {
        return this.timeslotService.create(createTimeslotInput)
    }

    @Query(() => [Timeslot], { name: 'allTimeslots' })
    findAll() {
        return this.timeslotService.findAll()
    }

    @Query(() => Timeslot, { name: 'getTimeslot' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.timeslotService.findOne(id)
    }

    @Mutation(() => Timeslot)
    updateTimeslot(
        @Args('updateTimeslotInput') updateTimeslotInput: UpdateTimeslotInput
    ) {
        return this.timeslotService.update(
            updateTimeslotInput.id,
            updateTimeslotInput
        )
    }

    @Mutation(() => Timeslot)
    removeTimeslot(@Args('id', { type: () => String }) id: string) {
        return this.timeslotService.remove(id)
    }

    @Mutation(() => Int)
    removeMultipleTimeslots(@Args('ids', { type: () => [String] }) ids: string[]) {
        return this.timeslotService.removeMultiple(ids)
    }

    @Query(() => [Timeslot], { name: 'findTimeslotByDateRange' })
    findByDateRange(
        @Args('startDate', { type: () => Date }) startDate: Date,
        @Args('endDate', { type: () => Date }) endDate: Date
    ) {
        return this.timeslotService.findByDateRange(startDate, endDate)
    }

    @Query(() => [Timeslot], { name: 'findTimeslotByFairId' })
    findByFairId(@Args('fairId', { type: () => String }) fairId: string) {
        return this.timeslotService.findByFair(fairId)
    }
}
