import { Test, TestingModule } from '@nestjs/testing';
import { TimeslotResolver } from './timeslot.resolver';
import { TimeslotService } from './timeslot.service';

describe('TimeslotResolver', () => {
  let resolver: TimeslotResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TimeslotResolver, TimeslotService],
    }).compile();

    resolver = module.get<TimeslotResolver>(TimeslotResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
