import { Injectable } from '@nestjs/common'
import { CreateStaleImageInput } from './dto/create-stale-image.input'
import { PrismaService } from '../prisma.service'

@Injectable()
export class StaleImagesService {
    constructor(private readonly prisma: PrismaService) {}

    create(createStaleImageInput: CreateStaleImageInput) {
        return this.prisma.staleImage.create({
            data: createStaleImageInput,
        })
    }

    async removeMultiple(ids: string[]) {
        const removed = await this.prisma.staleImage.deleteMany({
            where: {
                id: {
                    in: ids,
                },
            },
        })
        return removed.count
    }

    async removeAll() {
        const removed = await this.prisma.staleImage.deleteMany()
        return removed.count
    }
}
