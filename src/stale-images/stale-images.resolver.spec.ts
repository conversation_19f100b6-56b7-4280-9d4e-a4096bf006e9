import { Test, TestingModule } from '@nestjs/testing'
import { StaleImagesResolver } from './stale-images.resolver'
import { StaleImagesService } from './stale-images.service'

describe('StaleImagesResolver', () => {
    let resolver: StaleImagesResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [StaleImagesResolver, StaleImagesService],
        }).compile()

        resolver = module.get<StaleImagesResolver>(StaleImagesResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
