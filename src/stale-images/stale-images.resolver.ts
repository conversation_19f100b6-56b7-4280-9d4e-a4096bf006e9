import { Resolver, Mutation, Args, Int } from '@nestjs/graphql'
import { StaleImagesService } from './stale-images.service'
import { StaleImage } from './entities/stale-image.entity'
import { CreateStaleImageInput } from './dto/create-stale-image.input'
import { UseGuards } from '@nestjs/common'
import { CompanyGuard } from '../guards/company-guard'

@UseGuards(CompanyGuard)
@Resolver(() => StaleImage)
export class StaleImagesResolver {
    constructor(private readonly staleImagesService: StaleImagesService) {}

    @Mutation(() => StaleImage)
    createStaleImage(
        @Args('createStaleImageInput')
        createStaleImageInput: CreateStaleImageInput
    ) {
        return this.staleImagesService.create(createStaleImageInput)
    }

    @Mutation(() => Int)
    removeMultipleStaleImages(
        @Args('ids', { type: () => [String] }) ids: string[]
    ) {
        return this.staleImagesService.removeMultiple(ids)
    }

    @Mutation(() => Int)
    removeAllStaleImages() {
        return this.staleImagesService.removeAll()
    }
}
