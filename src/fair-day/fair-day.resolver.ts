import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { FairDay } from './entities/fair-day.entity'
import { FairDayService } from './fair-day.service'
import { CreateFairDayInput } from './dto/create-fair-day.input'
import { UpdateFairDayInput } from './dto/update-fair-day.input'

@Resolver(() => FairDay)
export class FairDayResolver {
    constructor(private readonly fairDaysService: FairDayService) {}

    @Mutation(() => FairDay)
    createFairDay(
        @Args('createFairDayInput') createFairDayInput: CreateFairDayInput
    ) {
        return this.fairDaysService.create(createFairDayInput)
    }

    @Query(() => [FairDay], { name: 'allFairDay' })
    findAll() {
        return this.fairDaysService.findAll()
    }

    @Query(() => FairDay, { name: 'getFairDay' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.fairDaysService.findOne(id)
    }

    @Mutation(() => FairDay)
    updateFairDay(
        @Args('updateFairDayInput') updateFairDayInput: UpdateFairDayInput
    ) {
        return this.fairDaysService.update(
            updateFairDayInput.id,
            updateFairDayInput
        )
    }

    @Mutation(() => FairDay)
    removeFairDay(@Args('id', { type: () => String }) id: string) {
        return this.fairDaysService.remove(id)
    }

    @Query(() => [FairDay], { name: 'getFairDayByFair' })
    findByFair(@Args('fairId', { type: () => String }) fairId: string) {
        return this.fairDaysService.findByFair(fairId)
    }

    @Query(() => [FairDay], { name: 'getFairDayByDate' })
    findByDate(@Args('date', { type: () => Date }) date: Date) {
        return this.fairDaysService.findByDate(date)
    }
}
