import { ObjectType, Field, Int } from '@nestjs/graphql'
import { Fair } from '../../fair/entities/fair.entity'

@ObjectType()
export class FairDay {
    @Field()
    id: string

    @Field(() => Date)
    day: Date

    @Field(() => Date)
    startTime: Date

    @Field(() => Date)
    endTime: Date

    @Field(() => Fair)
    fair: Fair

    @Field(() => String)
    fairId: string

    @Field(() => Date)
    createdAt: Date

    @Field(() => Date)
    updatedAt: Date
}
