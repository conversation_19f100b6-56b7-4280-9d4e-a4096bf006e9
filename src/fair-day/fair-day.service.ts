import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateFairDayInput } from './dto/create-fair-day.input'
import { UpdateFairDayInput } from './dto/update-fair-day.input'

@Injectable()
export class FairDayService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createFairDaysInput: CreateFairDayInput) {
        const { fairId } = createFairDaysInput

        const fair = await this.prisma.fair.findUnique({
            where: { id: fairId },
        })

        if (!fair) {
            throw new NotFoundException(`Fair with ID ${fairId} not found`)
        }

        return this.prisma.fairDay.create({
            data: createFairDaysInput,
            include: {
                fair: true,
            },
        })
    }

    async findAll() {
        return this.prisma.fairDay.findMany({
            include: {
                fair: true,
            },
        })
    }

    async findOne(id: string) {
        const fairDay = await this.prisma.fairDay.findUnique({
            where: { id },
            include: {
                fair: true,
            },
        })

        if (!fairDay) {
            throw new NotFoundException(`Fair day with ID ${id} not found`)
        }

        return fairDay
    }

    async update(id: string, updateFairDaysInput: UpdateFairDayInput) {
        await this.findOne(id)

        const { fairId } = updateFairDaysInput

        if (fairId) {
            const fair = await this.prisma.fair.findUnique({
                where: { id: fairId },
            })

            if (!fair) {
                throw new NotFoundException(`Fair with ID ${fairId} not found`)
            }
        }

        return this.prisma.fairDay.update({
            where: { id },
            data: updateFairDaysInput,
            include: {
                fair: true,
            },
        })
    }

    async remove(id: string) {
        await this.findOne(id)

        return this.prisma.fairDay.delete({
            where: { id },
            include: {
                fair: true,
            },
        })
    }

    async findByFair(fairId: string) {
        const fair = await this.prisma.fair.findUnique({
            where: { id: fairId },
        })

        if (!fair) {
            throw new NotFoundException(`Fair with ID ${fairId} not found`)
        }

        return this.prisma.fairDay.findMany({
            where: { fairId },
            include: {
                fair: true,
            },
        })
    }

    async findByDate(date: Date) {
        const startOfDay = new Date(date)
        startOfDay.setHours(0, 0, 0, 0)

        const endOfDay = new Date(date)
        endOfDay.setHours(23, 59, 59, 999)

        return this.prisma.fairDay.findMany({
            where: {
                day: {
                    gte: startOfDay,
                    lte: endOfDay,
                },
            },
            include: {
                fair: true,
            },
        })
    }
}
