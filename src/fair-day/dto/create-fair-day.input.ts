import { InputType, Field } from '@nestjs/graphql'
import { IsDate, IsNotEmpty, IsOptional, IsUUID } from 'class-validator'

@InputType()
export class CreateFairDayInput {
    @Field(() => Date)
    @IsDate()
    @IsNotEmpty()
    day: Date

    @Field(() => Date)
    @IsDate()
    @IsNotEmpty()
    startTime: Date

    @Field(() => Date)
    @IsDate()
    @IsNotEmpty()
    endTime: Date

    @IsOptional()
    @Field({ nullable: true })
    @IsUUID()
    fairId: string
}
