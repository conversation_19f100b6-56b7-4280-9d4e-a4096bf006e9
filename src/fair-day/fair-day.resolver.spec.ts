import { Test, TestingModule } from '@nestjs/testing';
import { FairDayResolver } from './fair-day.resolver';
import { FairDayService } from './fair-day.service';

describe('FairDayResolver', () => {
  let resolver: FairDayResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FairDayResolver, FairDayService],
    }).compile();

    resolver = module.get<FairDayResolver>(FairDayResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
