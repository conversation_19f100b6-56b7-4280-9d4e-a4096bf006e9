import { Resolver, Mutation, Args } from '@nestjs/graphql'
import { FirebaseActionsService } from './firebase-actions.service'
import { FirebaseAction } from './entities/firebase-action.entity'
import { UseGuards } from '@nestjs/common'
import { CompanyGuard } from '../guards/company-guard'

@Resolver(() => FirebaseAction)
export class FirebaseActionsResolver {
    constructor(
        private readonly firebaseActionsService: FirebaseActionsService
    ) {}

    @UseGuards(CompanyGuard)
    @Mutation(() => Boolean)
    async deleteImage(@Args('imageUrl') imageUrl: string): Promise<boolean> {
        try {
            await this.firebaseActionsService.deleteImage(imageUrl)
            return true
        } catch (error) {
            console.error('Error in deleteImage:', error)
            return false
        }
    }
}
