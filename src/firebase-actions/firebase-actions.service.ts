import { Injectable } from '@nestjs/common'
import { admin } from '../auth/firebase-admin.module'

@Injectable()
export class FirebaseActionsService {
    async deleteImage(imageUrl: string): Promise<void> {
        try {
            const bucket = admin.storage().bucket('bridge-public-v2')
            const filePath = decodeURIComponent(
                imageUrl.split('/o/')[1].split('?')[0]
            )

            await bucket.file(filePath).delete()
        } catch (error) {
            console.error('Error deleting image:', error)
            throw new Error('Failed to delete image from Firebase Storage')
        }
    }
}
