import { Module } from '@nestjs/common'
import { FirebaseActionsService } from './firebase-actions.service'
import { FirebaseActionsResolver } from './firebase-actions.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    exports: [FirebaseActionsService],
    providers: [FirebaseActionsResolver, FirebaseActionsService],
})
export class FirebaseActionsModule {}
