import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { PricingPlanService } from './pricing-plan.service'
import { PricingPlanResolver } from './pricing-plan.resolver'
import { PrismaModule } from '../prisma.module'
import { StripeModule } from '@golevelup/nestjs-stripe'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [
        AuthModule,
        PrismaModule,
        StripeModule.forRootAsync(StripeModule, {
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) =>
                configService.get('STRIPE_CONFIG'),
            inject: [ConfigService],
        }),
    ],
    providers: [PricingPlanService, PricingPlanResolver],
    exports: [PricingPlanService],
})
export class PricingPlanModule {}
