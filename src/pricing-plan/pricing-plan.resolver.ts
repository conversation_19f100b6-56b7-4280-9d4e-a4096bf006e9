import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { PricingPlanService } from './pricing-plan.service'
import {
    PricingPlan,
    CreatePricingPlanInput,
    UpdatePricingPlanInput,
} from './pricing-plan.type'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { SuperGuard } from '../guards/super-guard'

@Resolver(() => PricingPlan)
export class PricingPlanResolver {
    constructor(private readonly pricingPlanService: PricingPlanService) {}

    @Query(() => [PricingPlan], { name: 'allPricingPlans' })
    async getAllPricingPlans(): Promise<PricingPlan[]> {
        return await this.pricingPlanService.findAll()
    }

    @Query(() => [PricingPlan], { name: 'activePricingPlans' })
    async getActivePricingPlans(): Promise<PricingPlan[]> {
        return await this.pricingPlanService.findActive()
    }

    @Query(() => PricingPlan, { name: 'pricingPlan' })
    async getPricingPlan(
        @Args('id', { type: () => ID }) id: string
    ): Promise<PricingPlan> {
        return await this.pricingPlanService.findById(id)
    }

    @Mutation(() => PricingPlan, { name: 'createPricingPlan' })
    @UseGuards(GraphqlAuthGuard, SuperGuard)
    async createPricingPlan(
        @Args('input', { type: () => CreatePricingPlanInput }) input: CreatePricingPlanInput
    ): Promise<PricingPlan> {
        return await this.pricingPlanService.create(input)
    }

    @Mutation(() => PricingPlan, { name: 'updatePricingPlan' })
    @UseGuards(GraphqlAuthGuard, SuperGuard)
    async updatePricingPlan(
        @Args('input', { type: () => UpdatePricingPlanInput }) input: UpdatePricingPlanInput
    ): Promise<PricingPlan> {
        return await this.pricingPlanService.update(input)
    }

    @Mutation(() => PricingPlan, { name: 'deactivatePricingPlan' })
    @UseGuards(GraphqlAuthGuard, SuperGuard)
    async deactivatePricingPlan(
        @Args('id', { type: () => ID }) id: string
    ): Promise<PricingPlan> {
        return await this.pricingPlanService.deactivate(id)
    }
}
