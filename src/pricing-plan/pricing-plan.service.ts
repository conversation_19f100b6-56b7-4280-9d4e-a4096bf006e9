import {
    Injectable,
    Logger,
    BadRequestException,
    NotFoundException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { InjectStripeClient } from '@golevelup/nestjs-stripe'
import Stripe from 'stripe'
import { PricingPlan as PrismaPricingPlan, BillingPeriod } from '@prisma/client'
import {
    CreatePricingPlanInput,
    UpdatePricingPlanInput,
} from './pricing-plan.type'
import configs from '../config'

@Injectable()
export class PricingPlanService {
    private readonly logger = new Logger(PricingPlanService.name)

    constructor(
        @InjectStripeClient() private stripe: Stripe,
        private prisma: PrismaService
    ) {
        this.stripe = new Stripe(configs().STRIPE_CONFIG.apiKey, {
            apiVersion: '2022-11-15',
        })
    }

    private getBillingInterval(
        billingPeriod: BillingPeriod
    ): Stripe.PriceCreateParams.Recurring {
        const intervalMap: Record<
            BillingPeriod,
            Stripe.PriceCreateParams.Recurring
        > = {
            MONTHLY: { interval: 'month', interval_count: 1 },
            QUARTERLY: { interval: 'month', interval_count: 3 },
            SEMI_ANNUAL: { interval: 'month', interval_count: 6 },
            ANNUAL: { interval: 'year', interval_count: 1 },
            ONE_TIME: null, // One-time payments don't have recurring
            CUSTOM: null, // Custom will be handled separately
        }
        return intervalMap[billingPeriod]
    }

    async findAll(): Promise<PrismaPricingPlan[]> {
        return this.prisma.pricingPlan.findMany({
            orderBy: [{ displayOrder: 'asc' }, { createdAt: 'asc' }],
        })
    }

    async findActive(): Promise<PrismaPricingPlan[]> {
        return this.prisma.pricingPlan.findMany({
            where: { isActive: true },
            orderBy: [{ displayOrder: 'asc' }, { createdAt: 'asc' }],
        })
    }

    async findById(id: string): Promise<PrismaPricingPlan> {
        const plan = await this.prisma.pricingPlan.findUnique({
            where: { id },
        })

        if (!plan) {
            throw new NotFoundException(`Pricing plan with ID ${id} not found`)
        }

        return plan
    }

    async create(input: CreatePricingPlanInput): Promise<PrismaPricingPlan> {
        this.logger.log(
            'Creating pricing plan with input:',
            JSON.stringify(input)
        )
        console.log('Creating pricing plan with input:', JSON.stringify(input))

        try {
            // Validate required fields
            if (!input.name) {
                throw new BadRequestException('Plan name is required')
            }
            if (!input.displayName) {
                throw new BadRequestException('Display name is required')
            }
            if (input.price === undefined || input.price === null) {
                throw new BadRequestException('Price is required')
            }
            if (!input.billingPeriod) {
                throw new BadRequestException('Billing period is required')
            }
            if (!input.durationDays) {
                this.logger.log(
                    'durationDays not provided, setting default to 30'
                )
                input.durationDays = 30 // Default to 30 days
            }

            // Create Stripe Product
            const stripeProduct = await this.stripe.products.create({
                name: input.displayName,
                description: input.description || undefined,
                metadata: {
                    planName: input.name,
                    billingPeriod: input.billingPeriod,
                    durationDays: String(input.durationDays || 30),
                },
            })

            // Create Stripe Price
            const priceInCents = Math.round(input.price * 100) // Convert to cents
            const recurring = this.getBillingInterval(input.billingPeriod)

            let stripePrice: Stripe.Price

            if (input.billingPeriod === 'ONE_TIME') {
                // Create one-time price
                stripePrice = await this.stripe.prices.create({
                    product: stripeProduct.id,
                    unit_amount: priceInCents,
                    currency: input.currency.toLowerCase(),
                })
            } else if (recurring) {
                // Create recurring price
                stripePrice = await this.stripe.prices.create({
                    product: stripeProduct.id,
                    unit_amount: priceInCents,
                    currency: input.currency.toLowerCase(),
                    recurring: recurring,
                })
            } else {
                // For CUSTOM billing period, create without recurring
                stripePrice = await this.stripe.prices.create({
                    product: stripeProduct.id,
                    unit_amount: priceInCents,
                    currency: input.currency.toLowerCase(),
                    metadata: {
                        billingPeriod: 'CUSTOM',
                        durationDays: String(input.durationDays || 30),
                    },
                })
            }

            // Create PricingPlan in database
            const pricingPlan = await this.prisma.pricingPlan.create({
                data: {
                    ...input,
                    stripeProductId: stripeProduct.id,
                    stripePriceId: stripePrice.id,
                },
            })

            this.logger.log(
                `Created pricing plan: ${pricingPlan.id} with Stripe product: ${stripeProduct.id}`
            )
            return pricingPlan
        } catch (error) {
            this.logger.error('Error creating pricing plan:', error)
            throw new BadRequestException(
                'Failed to create pricing plan: ' + error.message
            )
        }
    }

    async update(input: UpdatePricingPlanInput): Promise<PrismaPricingPlan> {
        const { id, ...updateData } = input

        // Find existing plan
        const existingPlan = await this.findById(id)

        try {
            // Update Stripe Product if necessary
            if (
                existingPlan.stripeProductId &&
                (updateData.displayName || updateData.description !== undefined)
            ) {
                await this.stripe.products.update(
                    existingPlan.stripeProductId,
                    {
                        name: updateData.displayName || undefined,
                        description: updateData.description || undefined,
                    }
                )
            }

            // Check if we need to create a new Stripe Price
            const priceChanged =
                updateData.price !== undefined &&
                updateData.price !== existingPlan.price
            const billingPeriodChanged =
                updateData.billingPeriod !== undefined &&
                updateData.billingPeriod !== existingPlan.billingPeriod
            const currencyChanged =
                updateData.currency !== undefined &&
                updateData.currency !== existingPlan.currency

            let newStripePriceId = existingPlan.stripePriceId

            if (
                (priceChanged || billingPeriodChanged || currencyChanged) &&
                existingPlan.stripeProductId
            ) {
                // Archive old price (Stripe doesn't allow deleting prices)
                if (existingPlan.stripePriceId) {
                    await this.stripe.prices.update(
                        existingPlan.stripePriceId,
                        {
                            active: false,
                        }
                    )
                }

                // Create new price
                const price = updateData.price ?? existingPlan.price
                const priceInCents = Math.round(price * 100)
                const billingPeriod =
                    updateData.billingPeriod ?? existingPlan.billingPeriod
                const currency = (
                    updateData.currency ?? existingPlan.currency
                ).toLowerCase()
                const recurring = this.getBillingInterval(billingPeriod)

                let newStripePrice: Stripe.Price

                if (billingPeriod === 'ONE_TIME') {
                    newStripePrice = await this.stripe.prices.create({
                        product: existingPlan.stripeProductId,
                        unit_amount: priceInCents,
                        currency: currency,
                    })
                } else if (recurring) {
                    newStripePrice = await this.stripe.prices.create({
                        product: existingPlan.stripeProductId,
                        unit_amount: priceInCents,
                        currency: currency,
                        recurring: recurring,
                    })
                } else {
                    // For CUSTOM billing period
                    newStripePrice = await this.stripe.prices.create({
                        product: existingPlan.stripeProductId,
                        unit_amount: priceInCents,
                        currency: currency,
                        metadata: {
                            billingPeriod: 'CUSTOM',
                            durationDays: (
                                updateData.durationDays ??
                                existingPlan.durationDays
                            ).toString(),
                        },
                    })
                }

                newStripePriceId = newStripePrice.id
            }

            // Update PricingPlan in database
            const updatedPlan = await this.prisma.pricingPlan.update({
                where: { id },
                data: {
                    ...updateData,
                    stripePriceId: newStripePriceId,
                },
            })

            this.logger.log(`Updated pricing plan: ${id}`)
            return updatedPlan
        } catch (error) {
            this.logger.error('Error updating pricing plan:', error)
            throw new BadRequestException(
                'Failed to update pricing plan: ' + error.message
            )
        }
    }

    async deactivate(id: string): Promise<PrismaPricingPlan> {
        const existingPlan = await this.findById(id)

        try {
            // Archive Stripe Product and Price
            if (existingPlan.stripeProductId) {
                await this.stripe.products.update(
                    existingPlan.stripeProductId,
                    {
                        active: false,
                    }
                )
            }

            if (existingPlan.stripePriceId) {
                await this.stripe.prices.update(existingPlan.stripePriceId, {
                    active: false,
                })
            }

            // Deactivate PricingPlan in database
            const deactivatedPlan = await this.prisma.pricingPlan.update({
                where: { id },
                data: { isActive: false },
            })

            this.logger.log(`Deactivated pricing plan: ${id}`)
            return deactivatedPlan
        } catch (error) {
            this.logger.error('Error deactivating pricing plan:', error)
            throw new BadRequestException(
                'Failed to deactivate pricing plan: ' + error.message
            )
        }
    }
}
