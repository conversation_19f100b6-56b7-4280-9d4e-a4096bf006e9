import {
    ObjectType,
    Field,
    ID,
    Float,
    Int,
    InputType,
    registerEnumType,
} from '@nestjs/graphql'
import {
    IsString,
    IsNotEmpty,
    IsOptional,
    IsNumber,
    IsBoolean,
    IsEnum,
    IsUUID,
    Min,
    Max,
    Length,
    IsPositive,
    IsIn,
} from 'class-validator'
import { BillingPeriod, PricingPlanType } from '@prisma/client'

// Register enums for GraphQL
registerEnumType(BillingPeriod, {
    name: 'BillingPeriod',
    description: 'The billing period for a pricing plan',
})

registerEnumType(PricingPlanType, {
    name: 'PricingPlanType',
    description: 'The type of pricing plan',
})

@ObjectType()
export class PricingPlan {
    @Field(() => ID)
    id: string

    @Field()
    name: string

    @Field()
    displayName: string

    @Field({ nullable: true })
    description?: string

    @Field(() => Float)
    price: number

    @Field()
    currency: string

    @Field(() => BillingPeriod)
    billingPeriod: BillingPeriod

    @Field(() => Int)
    durationDays: number

    @Field()
    isPopular: boolean

    @Field()
    isCustom: boolean

    @Field(() => Int)
    displayOrder: number

    @Field()
    isActive: boolean

    @Field()
    unlimitedJobAdverts: boolean

    @Field()
    hasCustomBranding: boolean

    @Field(() => PricingPlanType)
    planType: PricingPlanType

    @Field({ nullable: true })
    stripeProductId?: string

    @Field({ nullable: true })
    stripePriceId?: string

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date
}

@InputType()
export class CreatePricingPlanInput {
    @Field()
    @IsString()
    @IsNotEmpty()
    @Length(1, 100)
    name: string

    @Field()
    @IsString()
    @IsNotEmpty()
    @Length(1, 200)
    displayName: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    @Length(0, 1000)
    description?: string

    @Field(() => Float)
    @IsNumber()
    @IsPositive()
    @Min(0.01)
    @Max(999999.99)
    price: number

    @Field({ defaultValue: 'EUR' })
    @IsString()
    @IsNotEmpty()
    @IsIn(['EUR', 'USD', 'GBP', 'CHF'])
    currency: string

    @Field(() => BillingPeriod)
    @IsEnum(BillingPeriod)
    billingPeriod: BillingPeriod

    @Field(() => Int)
    @IsNumber()
    @IsPositive()
    @Min(1)
    @Max(3650) // Max 10 years
    durationDays: number

    @Field({ defaultValue: false })
    @IsBoolean()
    @IsOptional()
    isPopular: boolean

    @Field({ defaultValue: false })
    @IsBoolean()
    @IsOptional()
    isCustom: boolean

    @Field(() => Int, { defaultValue: 0 })
    @IsNumber()
    @IsOptional()
    @Min(0)
    @Max(999)
    displayOrder: number

    @Field({ defaultValue: true })
    @IsBoolean()
    @IsOptional()
    isActive: boolean

    @Field({ defaultValue: false })
    @IsBoolean()
    @IsOptional()
    unlimitedJobAdverts: boolean

    @Field({ defaultValue: false })
    @IsBoolean()
    @IsOptional()
    hasCustomBranding: boolean

    @Field(() => PricingPlanType, { defaultValue: PricingPlanType.STANDARD })
    @IsEnum(PricingPlanType)
    @IsOptional()
    planType: PricingPlanType
}

@InputType()
export class UpdatePricingPlanInput {
    @Field({ nullable: false })
    @IsString()
    @IsNotEmpty()
    @IsUUID()
    id: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    @Length(1, 100)
    name?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    @Length(1, 200)
    displayName?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    @Length(0, 1000)
    description?: string

    @Field(() => Float, { nullable: true })
    @IsNumber()
    @IsOptional()
    @IsPositive()
    @Min(0.01)
    @Max(999999.99)
    price?: number

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    @IsIn(['EUR', 'USD', 'GBP', 'CHF'])
    currency?: string

    @Field(() => BillingPeriod, { nullable: true })
    @IsEnum(BillingPeriod)
    @IsOptional()
    billingPeriod?: BillingPeriod

    @Field(() => Int, { nullable: true })
    @IsNumber()
    @IsOptional()
    @IsPositive()
    @Min(1)
    @Max(3650) // Max 10 years
    durationDays?: number

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    isPopular?: boolean

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    isCustom?: boolean

    @Field(() => Int, { nullable: true })
    @IsNumber()
    @IsOptional()
    @Min(0)
    @Max(999)
    displayOrder?: number

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    isActive?: boolean

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    unlimitedJobAdverts?: boolean

    @Field({ nullable: true })
    @IsBoolean()
    @IsOptional()
    hasCustomBranding?: boolean

    @Field(() => PricingPlanType, { nullable: true })
    @IsEnum(PricingPlanType)
    @IsOptional()
    planType?: PricingPlanType
}
