import { InputType, Field } from '@nestjs/graphql'
import { IsBoolean, IsOptional } from 'class-validator'

@InputType()
export class CreateUserRightInput {
    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    superAdmin?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    createUser?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    companyAdmin?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    createJobAd?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    deleteJobAd?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    updateJobAd?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    editJobAd?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    viewJobAd?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    viewApplicants?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    editCompany?: boolean
}
