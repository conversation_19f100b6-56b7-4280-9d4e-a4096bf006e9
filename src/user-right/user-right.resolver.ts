import { Resolver, Mutation, Args } from '@nestjs/graphql'
import { UserRightService } from './user-right.service'
import { UserRight } from './entities/user-right.entity'
import { CreateUserRightInput } from './dto/create-user-right.input'
import { UpdateUserRightInput } from './dto/update-user-right.input'
import { UseGuards } from '@nestjs/common'
import { CompanyGuard } from '../guards/company-guard'

@UseGuards(CompanyGuard)
@Resolver(() => UserRight)
export class UserRightResolver {
    constructor(private readonly userRightService: UserRightService) {}

    @Mutation(() => UserRight)
    createUserRights(
        @Args('companyId', { type: () => String }) companyId: string,
        @Args('companyUserId', { type: () => String }) companyUserId: string,
        @Args('createUserRightInput') createUserRightInput: CreateUserRightInput
    ) {
        return this.userRightService.create(
            companyId,
            companyUserId,
            createUserRightInput
        )
    }

    @Mutation(() => UserRight)
    updateUserRights(
        @Args('id', { type: () => String }) id: string,
        @Args('companyId', { type: () => String }) companyId: string,
        @Args('companyUserId', { type: () => String }) companyUserId: string,
        @Args('updateUserRightInput') updateUserRightInput: UpdateUserRightInput
    ) {
        return this.userRightService.update(
            id,
            companyId,
            companyUserId,
            updateUserRightInput
        )
    }
}
