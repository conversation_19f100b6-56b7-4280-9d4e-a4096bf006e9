import { Test, TestingModule } from '@nestjs/testing'
import { UserRightResolver } from './user-right.resolver'
import { UserRightService } from './user-right.service'

describe('UserRightResolver', () => {
    let resolver: UserRightResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [UserRightResolver, UserRightService],
        }).compile()

        resolver = module.get<UserRightResolver>(UserRightResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
