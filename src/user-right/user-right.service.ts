import { Injectable } from '@nestjs/common'
import { CreateUserRightInput } from './dto/create-user-right.input'
import { UpdateUserRightInput } from './dto/update-user-right.input'
import { PrismaService } from '../prisma.service'

@Injectable()
export class UserRightService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        companyId: string,
        companyUserId: string,
        createUserRightInput: CreateUserRightInput
    ) {
        return this.prisma.userRights.create({
            data: {
                ...createUserRightInput,
                company: {
                    connect: {
                        id: companyId,
                    },
                },
                companyUser: {
                    connect: {
                        id: companyUserId,
                    },
                },
            },
        })
    }

    async update(
        id: string,
        companyId: string,
        companyUserId: string,
        updateUserRightInput: UpdateUserRightInput
    ) {
        return this.prisma.userRights.update({
            where: {
                id: id,
            },
            data: {
                ...updateUserRightInput,
                company: {
                    connect: {
                        id: companyId,
                    },
                },
                companyUser: {
                    connect: {
                        id: companyUserId,
                    },
                },
            },
        })
    }
}
