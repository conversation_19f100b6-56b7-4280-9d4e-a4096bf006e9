import { ObjectType, Field } from '@nestjs/graphql'
import { Company } from '../../company/entities/company.entity'
import { CompanyUser } from '../../company-user/entities/company-user.entity'

@ObjectType()
export class UserRight {
    @Field()
    id: string

    @Field({ nullable: true })
    superAdmin?: boolean

    @Field({ nullable: true })
    companyAdmin?: boolean

    @Field({ nullable: true })
    createJobAd?: boolean

    @Field({ nullable: true })
    createUser?: boolean

    @Field({ nullable: true })
    deleteJobAd?: boolean

    @Field({ nullable: true })
    updateJobAd?: boolean

    @Field({ nullable: true })
    editJobAd?: boolean

    @Field({ nullable: true })
    viewJobAd?: boolean

    @Field({ nullable: true })
    viewApplicants?: boolean

    @Field({ nullable: true })
    editCompany?: boolean

    @Field(() => Company)
    company: Company

    @Field()
    companyId: string

    @Field(() => CompanyUser)
    companyUser: CompanyUser
}
