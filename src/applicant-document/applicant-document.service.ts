import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateApplicantDocumentInput } from './dto/create-applicant-document.input'
import { UpdateApplicantDocumentInput } from './dto/update-applicant-document.input'
import { PrismaService } from '../prisma.service'

@Injectable()
export class ApplicantDocumentService {
    constructor(private readonly prisma: PrismaService) {}

    create(
        createApplicantDocumentInput: CreateApplicantDocumentInput,
        applicantId: string
    ) {
        return this.prisma.applicantDocument.create({
            data: {
                ...createApplicantDocumentInput,
                applicant: {
                    connect: {
                        id: applicantId,
                    },
                },
            },
            include: {
                applicant: true,
            },
        })
    }

    findAll() {
        return this.prisma.applicantDocument.findMany({
            include: {
                applicant: true,
            },
        })
    }

    findOne(id: string) {
        try {
            return this.prisma.applicantDocument.findUnique({
                where: {
                    id: id,
                },
                include: {
                    applicant: true,
                },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    findByApplicant(id: string) {
        try {
            return this.prisma.applicantDocument.findMany({
                where: {
                    applicant: {
                        id: {
                            equals: id,
                        },
                    },
                },
                include: {
                    applicant: true,
                },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    update(updateApplicantDocumentInput: UpdateApplicantDocumentInput) {
        return this.prisma.applicantDocument.update({
            where: {
                id: updateApplicantDocumentInput.id,
            },
            data: updateApplicantDocumentInput,
        })
    }

    remove(id: string) {
        const documentToDelete = this.prisma.applicantDocument.findUnique({
            where: { id: id },
        })
        if (!documentToDelete) {
            throw new NotFoundException(`Document with ID ${id} does not exist`)
        }
        return this.prisma.applicantDocument.delete({
            where: { id: id },
        })
    }
}
