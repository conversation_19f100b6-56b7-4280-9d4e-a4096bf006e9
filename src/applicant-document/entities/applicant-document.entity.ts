import { ObjectType, Field } from '@nestjs/graphql'
import { Applicant } from '../../applicant/entities/applicant.entity'

@ObjectType()
export class ApplicantDocument {
    @Field({ description: 'ID' })
    id: string

    @Field(() => Applicant)
    applicant?: Applicant

    @Field()
    documentPreviewUrl?: string

    @Field()
    name: string

    @Field()
    storage: string

    @Field()
    url: string
}
