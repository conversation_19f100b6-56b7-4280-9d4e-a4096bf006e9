import { Test, TestingModule } from '@nestjs/testing'
import { ApplicantDocumentResolver } from './applicant-document.resolver'
import { ApplicantDocumentService } from './applicant-document.service'

describe('ApplicantDocumentResolver', () => {
    let resolver: ApplicantDocumentResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [ApplicantDocumentResolver, ApplicantDocumentService],
        }).compile()

        resolver = module.get<ApplicantDocumentResolver>(
            ApplicantDocumentResolver
        )
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
