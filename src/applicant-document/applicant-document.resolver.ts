import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { ApplicantDocumentService } from './applicant-document.service'
import { ApplicantDocument } from './entities/applicant-document.entity'
import { CreateApplicantDocumentInput } from './dto/create-applicant-document.input'
import { UpdateApplicantDocumentInput } from './dto/update-applicant-document.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { UseGuards } from '@nestjs/common'

@UseGuards(GraphqlAuthGuard)
@Resolver(() => ApplicantDocument)
export class ApplicantDocumentResolver {
    constructor(
        private readonly applicantDocumentService: ApplicantDocumentService
    ) {}

    @Mutation(() => ApplicantDocument)
    createApplicantDocument(
        @Context() context: any,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string,
        @Args('createApplicantDocumentInput')
        createApplicantDocumentInput: CreateApplicantDocumentInput
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        return this.applicantDocumentService.create(
            createApplicantDocumentInput,
            bApplicantId
        )
    }

    @Query(() => [ApplicantDocument], { name: 'allApplicantDocuments' })
    findAll() {
        return this.applicantDocumentService.findAll()
    }

    @Query(() => ApplicantDocument, { name: 'applicantDocumentById' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.applicantDocumentService.findOne(id)
    }

    @Query(() => ApplicantDocument, { name: 'applicantDocumentByApplicantId' })
    findApplicantDocument(
        @Context() context: any,
        @Args('id', { type: () => String, nullable: true }) id: string
    ) {
        const bApplicantId = context?.req?.user?.applicantId || id
        return this.applicantDocumentService.findByApplicant(bApplicantId)
    }

    @Mutation(() => ApplicantDocument)
    updateApplicantDocument(
        @Args('updateApplicantDocumentInput')
        updateApplicantDocumentInput: UpdateApplicantDocumentInput
    ) {
        return this.applicantDocumentService.update(
            updateApplicantDocumentInput
        )
    }

    @Mutation(() => ApplicantDocument)
    removeApplicantDocument(@Args('id', { type: () => String }) id: string) {
        return this.applicantDocumentService.remove(id)
    }
}
