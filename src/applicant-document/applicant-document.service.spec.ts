import { Test, TestingModule } from '@nestjs/testing'
import { ApplicantDocumentService } from './applicant-document.service'

describe('ApplicantDocumentService', () => {
    let service: ApplicantDocumentService

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [ApplicantDocumentService],
        }).compile()

        service = module.get<ApplicantDocumentService>(ApplicantDocumentService)
    })

    it('should be defined', () => {
        expect(service).toBeDefined()
    })
})
