import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateCompanyFairJobInput } from './dto/create-company-fair-job.input'
import { UpdateCompanyFairJobInput } from './dto/update-company-fair-job.input'

@Injectable()
export class CompanyFairJobService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createCompanyFairJobInput: CreateCompanyFairJobInput) {
        const { companyFairParticipationId, fairJobId, description } =
            createCompanyFairJobInput

        const participation =
            await this.prisma.companyFairParticipation.findUnique({
                where: { id: companyFairParticipationId },
            })

        if (!participation) {
            throw new NotFoundException('Company Fair Participation not found')
        }

        const fairJob = await this.prisma.fairJob.findUnique({
            where: { id: fairJobId },
        })

        if (!fairJob) {
            throw new NotFoundException('Fair Job not found')
        }

        const existingJob = await this.prisma.companyFairJob.findFirst({
            where: {
                companyFairParticipationId,
                fairJobId,
            },
        })

        if (existingJob) {
            throw new Error(
                'This job is already associated with this participation'
            )
        }

        return this.prisma.companyFairJob.create({
            data: {
                description,
                companyFairParticipation: {
                    connect: { id: companyFairParticipationId },
                },
                fairJob: {
                    connect: { id: fairJobId },
                },
            },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                fairJob: true,
            },
        })
    }

    async findAll() {
        return this.prisma.companyFairJob.findMany({
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                fairJob: true,
            },
        })
    }

    async findOne(id: string) {
        const fairJob = await this.prisma.companyFairJob.findUnique({
            where: { id },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                fairJob: true,
            },
        })

        if (!fairJob) {
            throw new NotFoundException('Company fair job not found')
        }

        return fairJob
    }

    async update(
        id: string,
        updateCompanyFairJobInput: UpdateCompanyFairJobInput
    ) {
        const { description } = updateCompanyFairJobInput

        const jobToUpdate = await this.findOne(id)
        if (!jobToUpdate) {
            throw new NotFoundException('Company fair job not found')
        }

        return this.prisma.companyFairJob.update({
            where: { id },
            data: { description },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                fairJob: true,
            },
        })
    }

    async remove(id: string) {
        await this.findOne(id)

        return this.prisma.companyFairJob.delete({
            where: { id },
        })
    }

    async findByParticipation(participationId: string) {
        return this.prisma.companyFairJob.findMany({
            where: { companyFairParticipationId: participationId },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                fairJob: true,
            },
        })
    }

    async findByFairJob(fairJobId: string) {
        return this.prisma.companyFairJob.findMany({
            where: { fairJobId },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                fairJob: true,
            },
        })
    }
}
