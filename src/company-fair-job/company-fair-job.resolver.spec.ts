import { Test, TestingModule } from '@nestjs/testing';
import { CompanyFairJobResolver } from './company-fair-job.resolver';
import { CompanyFairJobService } from './company-fair-job.service';

describe('CompanyFairJobResolver', () => {
  let resolver: CompanyFairJobResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CompanyFairJobResolver, CompanyFairJobService],
    }).compile();

    resolver = module.get<CompanyFairJobResolver>(CompanyFairJobResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
