import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { CompanyFairJobService } from './company-fair-job.service';
import { CompanyFairJob } from './entities/company-fair-job.entity';
import { CreateCompanyFairJobInput } from './dto/create-company-fair-job.input';
import { UpdateCompanyFairJobInput } from './dto/update-company-fair-job.input';

@Resolver(() => CompanyFairJob)
export class CompanyFairJobResolver {
  constructor(private readonly companyFairJobService: CompanyFairJobService) {}

  @Mutation(() => CompanyFairJob)
  createCompanyFairJob(@Args('createCompanyFairJobInput') createCompanyFairJobInput: CreateCompanyFairJobInput) {
    return this.companyFairJobService.create(createCompanyFairJobInput);
  }

  @Query(() => [CompanyFairJob], { name: 'companyFairJob' })
  findAll() {
    return this.companyFairJobService.findAll();
  }

  @Query(() => CompanyFairJob, { name: 'companyFairJob' })
  findOne(@Args('id', { type: () => String}) id: string) {
    return this.companyFairJobService.findOne(id);
  }

  @Mutation(() => CompanyFairJob)
  updateCompanyFairJob(@Args('updateCompanyFairJobInput') updateCompanyFairJobInput: UpdateCompanyFairJobInput) {
    return this.companyFairJobService.update(updateCompanyFairJobInput.id, updateCompanyFairJobInput);
  }

  @Mutation(() => CompanyFairJob)
  removeCompanyFairJob(@Args('id', { type: () => String}) id: string) {
    return this.companyFairJobService.remove(id);
  }

    @Query(() => [CompanyFairJob], { name: 'getCompanyFairJobByParticipationId' })
    async getCompanyFairJobByParticipationId(@Args('companyParticipationId', { type: () => String }) companyParticipationId: string) {
        return this.companyFairJobService.findByParticipation(companyParticipationId);
    }

    @Query(() => [CompanyFairJob], { name: 'getCompanyFairJobByFairJobId' })
    async getCompanyFairJobByFairJobId(@Args('fairJobId', { type: () => String }) fairJobId: string) {
        return this.companyFairJobService.findByFairJob(fairJobId);
    }
}
