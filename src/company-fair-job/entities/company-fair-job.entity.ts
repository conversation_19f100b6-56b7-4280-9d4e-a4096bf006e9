import { ObjectType, Field } from '@nestjs/graphql'
import { CompanyFairParticipation } from '../../company-fair-participation/entities/company-fair-participation.entity'
import { FairJob } from '../../fair-job/entities/fair-job.entity'

@ObjectType()
export class CompanyFairJob {
  @Field()
  id: string

  @Field(() => CompanyFairParticipation)
  companyFairParticipation: CompanyFairParticipation

  @Field()
  companyFairParticipationId: string

  @Field({ nullable: true })
  description: string

  @Field(() => FairJob)
  fairJob: FairJob

  @Field()
  fairJobId: string
}