import { Module } from '@nestjs/common'
import { ContactPersonTimeslotService } from './contact-person-timeslot.service'
import { ContactPersonTimeslotResolver } from './contact-person-timeslot.resolver'
import { ChatRoomModule } from '../chat-room/chat-room.module'

@Module({
    imports: [ChatRoomModule],
    providers: [ContactPersonTimeslotResolver, ContactPersonTimeslotService],
    exports: [ContactPersonTimeslotService],
})
export class ContactPersonTimeslotModule {}
