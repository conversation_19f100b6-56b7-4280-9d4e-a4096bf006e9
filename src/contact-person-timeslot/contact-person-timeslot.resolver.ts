import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { ContactPersonTimeslotService } from './contact-person-timeslot.service';
import { ContactPersonTimeslot } from './entities/contact-person-timeslot.entity';
import { CreateContactPersonTimeslotInput } from './dto/create-contact-person-timeslot.input';
import { UpdateContactPersonTimeslotInput } from './dto/update-contact-person-timeslot.input';
import { BulkDeleteContactPersonTimeslotInput } from './dto/bulk-delete-contact-person-timeslot.input';
import { BulkDeleteResponse } from './entities/bulk-delete-response.entity';

@Resolver(() => ContactPersonTimeslot)
export class ContactPersonTimeslotResolver {
  constructor(private readonly contactPersonTimeslotService: ContactPersonTimeslotService) {}

  @Mutation(() => ContactPersonTimeslot)
  createContactPersonTimeslot(@Args('createContactPersonTimeslotInput') createContactPersonTimeslotInput: CreateContactPersonTimeslotInput) {
    return this.contactPersonTimeslotService.create(createContactPersonTimeslotInput);
  }

  @Query(() => [ContactPersonTimeslot], { name: 'allContactPersonTimeslots' })
  findAll() {
    return this.contactPersonTimeslotService.findAll();
  }

  @Query(() => ContactPersonTimeslot, { name: 'getContactPersonTimeslot' })
  findOne(@Args('id', { type: () => String}) id: string) {
    return this.contactPersonTimeslotService.findOne(id);
  }

  @Mutation(() => ContactPersonTimeslot)
  updateContactPersonTimeslot(@Args('updateContactPersonTimeslotInput') updateContactPersonTimeslotInput: UpdateContactPersonTimeslotInput) {
    return this.contactPersonTimeslotService.update(updateContactPersonTimeslotInput.id, updateContactPersonTimeslotInput);
  }

  @Mutation(() => ContactPersonTimeslot)
  removeContactPersonTimeslot(@Args('id', { type: () => String}) id: string) {
    return this.contactPersonTimeslotService.remove(id);
  }

  @Mutation(() => BulkDeleteResponse)
  bulkDeleteContactPersonTimeslots(@Args('input') input: BulkDeleteContactPersonTimeslotInput) {
    return this.contactPersonTimeslotService.bulkDelete(input);
  }

    @Query(() => [ContactPersonTimeslot], { name: 'findByContactPersonId' })
    findByContactPersonId(@Args('contactPersonId', { type: () => String }) contactPersonId: string) {
        return this.contactPersonTimeslotService.findByContactPerson(contactPersonId);
    }

    @Query(() => [ContactPersonTimeslot], { name: 'findByFair' })
    findByFair(@Args('fairId', { type: () => String }) fairId: string) {
        return this.contactPersonTimeslotService.findByFair(fairId);
    }

    @Query(() => [ContactPersonTimeslot], { name: 'findAvailableSlots' })
    findAvailableSlots(@Args('fairId', { type: () => String }) fairId: string) {
        return this.contactPersonTimeslotService.findAvailableSlots(fairId);
    }
}
