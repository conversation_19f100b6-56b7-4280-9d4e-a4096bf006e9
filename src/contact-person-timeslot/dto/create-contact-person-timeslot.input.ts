import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsDate, IsBoolean, IsOptional } from 'class-validator'
import { Type } from 'class-transformer'

@InputType()
export class CreateContactPersonTimeslotInput {
  @IsString()
  @Field()
  companyFairContactPersonId: string

  @Type(() => Date)
  @IsDate()
  @Field()
  startTime: Date

  @Type(() => Date)
  @IsDate()
  @Field()
  endTime: Date

  @IsBoolean()
  @IsOptional()
  @Field({ nullable: true })
  available?: boolean
}