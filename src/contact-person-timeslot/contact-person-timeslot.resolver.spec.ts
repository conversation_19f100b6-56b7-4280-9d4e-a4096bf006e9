import { Test, TestingModule } from '@nestjs/testing';
import { ContactPersonTimeslotResolver } from './contact-person-timeslot.resolver';
import { ContactPersonTimeslotService } from './contact-person-timeslot.service';

describe('ContactPersonTimeslotResolver', () => {
  let resolver: ContactPersonTimeslotResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ContactPersonTimeslotResolver, ContactPersonTimeslotService],
    }).compile();

    resolver = module.get<ContactPersonTimeslotResolver>(ContactPersonTimeslotResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
