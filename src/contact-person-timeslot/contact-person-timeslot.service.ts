import {
    Injectable,
    NotFoundException,
    BadRequestException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateContactPersonTimeslotInput } from './dto/create-contact-person-timeslot.input'
import { UpdateContactPersonTimeslotInput } from './dto/update-contact-person-timeslot.input'
import { BulkDeleteContactPersonTimeslotInput } from './dto/bulk-delete-contact-person-timeslot.input'
import { ChatRoomService } from '../chat-room/chat-room.service'

@Injectable()
export class ContactPersonTimeslotService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly chatRoomService: ChatRoomService
    ) {}

    async create(
        createContactPersonTimeslotInput: CreateContactPersonTimeslotInput
    ) {
        const { companyFairContactPersonId, startTime, endTime } =
            createContactPersonTimeslotInput

        const contactPerson =
            await this.prisma.companyFairContactPerson.findUnique({
                where: { id: companyFairContactPersonId },
            })

        if (!contactPerson) {
            throw new NotFoundException('Company fair contact person not found')
        }

        if (new Date(startTime) >= new Date(endTime)) {
            throw new BadRequestException('Start time must be before end time')
        }

        const newContactPersonTimeslot =
            await this.prisma.contactPersonTimeslot.create({
                data: {
                    startTime: new Date(startTime),
                    endTime: new Date(endTime),
                    companyFairContactPerson: {
                        connect: { id: companyFairContactPersonId },
                    },
                },
                include: {
                    companyFairContactPerson: {
                        include: {
                            contactPerson: true,
                            companyFairParticipation: {
                                include: {
                                    company: true,
                                    fair: true,
                                },
                            },
                        },
                    },
                    Appointment: true,
                },
            })

        return newContactPersonTimeslot
    }

    async findAll() {
        return this.prisma.contactPersonTimeslot.findMany({
            include: {
                companyFairContactPerson: {
                    include: {
                        contactPerson: true,
                        companyFairParticipation: {
                            include: {
                                company: true,
                                fair: true,
                            },
                        },
                    },
                },
                Appointment: true,
            },
        })
    }

    async findOne(id: string) {
        const timeslot = await this.prisma.contactPersonTimeslot.findUnique({
            where: { id },
            include: {
                companyFairContactPerson: {
                    include: {
                        contactPerson: true,
                        companyFairParticipation: {
                            include: {
                                company: true,
                                fair: true,
                            },
                        },
                    },
                },
                Appointment: true,
            },
        })

        if (!timeslot) {
            throw new NotFoundException('Contact person timeslot not found')
        }

        return timeslot
    }

    async update(
        id: string,
        updateContactPersonTimeslotInput: UpdateContactPersonTimeslotInput
    ) {
        const { startTime, endTime, available } =
            updateContactPersonTimeslotInput

        if (startTime && endTime && new Date(startTime) >= new Date(endTime)) {
            throw new BadRequestException('Start time must be before end time')
        }

        return this.prisma.contactPersonTimeslot.update({
            where: { id },
            data: {
                startTime: startTime ? new Date(startTime) : undefined,
                endTime: endTime ? new Date(endTime) : undefined,
                available,
            },
            include: {
                companyFairContactPerson: {
                    include: {
                        contactPerson: true,
                        companyFairParticipation: {
                            include: {
                                company: true,
                                fair: true,
                            },
                        },
                    },
                },
                Appointment: true,
            },
        })
    }

    async remove(id: string) {
        const timeslot = await this.findOne(id)

        if (timeslot.Appointment) {
            throw new BadRequestException(
                'Cannot delete timeslot with existing appointment'
            )
        }

        return this.prisma.contactPersonTimeslot.delete({
            where: { id },
        })
    }

    async bulkDelete(input: BulkDeleteContactPersonTimeslotInput) {
        const { ids } = input

        // Check if any timeslots have appointments
        const timeslots = await this.prisma.contactPersonTimeslot.findMany({
            where: {
                id: { in: ids },
            },
            include: {
                Appointment: true,
            },
        })

        const timeslotsWithAppointments = timeslots.filter(
            (timeslot) => timeslot.Appointment !== null
        )

        if (timeslotsWithAppointments.length > 0) {
            throw new BadRequestException(
                `Cannot delete timeslots with existing appointments: ${timeslotsWithAppointments
                    .map((t) => t.id)
                    .join(', ')}`
            )
        }

        // Delete all timeslots
        const result = await this.prisma.contactPersonTimeslot.deleteMany({
            where: {
                id: { in: ids },
            },
        })

        return {
            deletedCount: result.count,
            deletedIds: ids,
        }
    }

    async findByContactPerson(contactPersonId: string) {
        return this.prisma.contactPersonTimeslot.findMany({
            where: {
                companyFairContactPerson: {
                    contactPersonId,
                },
            },
            include: {
                companyFairContactPerson: {
                    include: {
                        contactPerson: true,
                        companyFairParticipation: {
                            include: {
                                company: true,
                                fair: true,
                            },
                        },
                    },
                },
                Appointment: true,
            },
        })
    }

    async findByFair(fairId: string) {
        return this.prisma.contactPersonTimeslot.findMany({
            where: {
                companyFairContactPerson: {
                    companyFairParticipation: {
                        fairId,
                    },
                },
            },
            include: {
                companyFairContactPerson: {
                    include: {
                        contactPerson: true,
                        companyFairParticipation: {
                            include: {
                                company: true,
                                fair: true,
                            },
                        },
                    },
                },
                Appointment: true,
            },
        })
    }

    async findAvailableSlots(fairId: string) {
        return this.prisma.contactPersonTimeslot.findMany({
            where: {
                available: true,
                Appointment: null,
                companyFairContactPerson: {
                    companyFairParticipation: {
                        fairId,
                    },
                },
            },
            include: {
                companyFairContactPerson: {
                    include: {
                        contactPerson: true,
                        companyFairParticipation: {
                            include: {
                                company: true,
                                fair: true,
                            },
                        },
                    },
                },
            },
        })
    }
}
