import { ObjectType, Field } from '@nestjs/graphql'
import { CompanyFairContactPerson } from '../../company-fair-contact-person/entities/company-fair-contact-person.entity'
import { Appointment } from '../../appointment/entities/appointment.entity'

@ObjectType()
export class ContactPersonTimeslot {
  @Field()
  id: string

  @Field(() => CompanyFairContactPerson)
  companyFairContactPerson: CompanyFairContactPerson

  @Field()
  companyFairContactPersonId: string

  @Field()
  startTime: Date

  @Field()
  endTime: Date

  @Field()
  available: boolean

  @Field(() => Appointment)
  Appointment: Appointment
}