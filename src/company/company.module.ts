import { Module } from '@nestjs/common'
import { CompanyService } from './company.service'
import { CompanyResolver } from './company.resolver'
import { AuthModule } from '../auth/auth.module'
import { EmailServerModule } from '../email-server/email-server.module'

@Module({
    imports: [AuthModule, EmailServerModule],
    exports: [CompanyService],
    providers: [CompanyResolver, CompanyService],
})
export class CompanyModule {}
