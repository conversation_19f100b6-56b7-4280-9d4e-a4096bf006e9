import { ObjectType, Field } from '@nestjs/graphql'
import { PricingPlan } from '../../pricing-plan/pricing-plan.type'
import { StripeSubscription } from '../../subscription/entities/subscription.entity'

@ObjectType()
export class CompanySubscriptionPlansResponse {
    @Field(() => [PricingPlan])
    availablePlans: PricingPlan[]

    @Field(() => [StripeSubscription])
    activeSubscriptions: StripeSubscription[]

    @Field()
    subscriptionType: string
}