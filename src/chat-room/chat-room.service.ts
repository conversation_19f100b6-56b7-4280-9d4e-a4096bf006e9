import { Injectable, NotFoundException, UseGuards } from '@nestjs/common'
import { CreateChatRoomInput } from './dto/create-chat-room.input'
import { UpdateChatRoomInput } from './dto/update-chat-room.input'
import { PrismaService } from '../prisma.service'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { ChatRoomCriteriaInput } from './dto/chatroom-criteria-input'

@UseGuards(GraphqlAuthGuard)
@Injectable()
export class ChatRoomService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        jobActionId: string,
        createChatRoomInput: CreateChatRoomInput
    ) {
        try {
            const chatRoom = await this.prisma.chatRoom.create({
                data: {
                    ...createChatRoomInput,
                    jobAction: {
                        connect: {
                            id: jobActionId,
                        },
                    },
                },
                include: {
                    jobAction: {
                        select: {
                            applicant: true,
                            jobAdvert: true,
                        },
                    },
                },
            })

            return chatRoom
        } catch (error) {
            console.error(error)
            if (error.code === 'P2002') {
                throw new Error(
                    'A chat room with the same job action ID already exists.'
                )
            } else {
                throw error
            }
        }
    }
    async createAppointmentChatRoom(
        appointmentId: string,
        createChatRoomInput: CreateChatRoomInput
    ) {
        try {
            return await this.prisma.chatRoom.create({
                data: {
                    ...createChatRoomInput,
                    appointment: {
                        connect: {
                            id: appointmentId,
                        },
                    },
                },
            })
        } catch (error) {
            console.error(error)
            if (error.code === 'P2002') {
                throw new Error(
                    'A chat room with the same appointment ID already exists.'
                )
            } else {
                throw error
            }
        }
    }
    findAll() {
        return this.prisma.chatRoom.findMany({
            include: {
                messages: true,
                jobAction: {
                    select: {
                        applicant: true,
                        jobAdvert: true,
                    },
                },
            },
        })
    }

    async findOne(id: string) {
        try {
            const chatRoom = await this.prisma.chatRoom.findUnique({
                where: {
                    id: id,
                },
                include: {
                    messages: true,
                    jobAction: {
                        select: {
                            applicant: true,
                            jobAdvert: true,
                        },
                    },
                },
            })

            if (!chatRoom) {
                throw new NotFoundException(
                    `ChatRoom with ID ${id} does not exist`
                )
            }

            return chatRoom
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }
    findContactPersonInChatRoom(id: string) {
        try {
            return this.prisma.chatRoom.findUnique({
                where: {
                    id: id,
                },
                include: {
                    appointment: {
                        include: {
                            contactPersonTimeslot: {
                                include: {
                                    companyFairContactPerson: true,
                                },
                            },
                        },
                    },
                    messages: true,
                    jobAction: {
                        select: {
                            applicant: true,
                            jobAdvert: true,
                        },
                    },
                },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }
    async chatRoomByMatch(id: string) {
        try {
            const chatRoom = await this.prisma.chatRoom.findFirst({
                where: {
                    jobAction: {
                        id: id,
                    },
                },
                include: {
                    messages: true,
                    jobAction: {
                        select: {
                            id: true,
                            applicant: true,
                            jobAdvert: true,
                        },
                    },
                },
            })
            if (!chatRoom)
                throw new NotFoundException(
                    `ChatRoom with jobActionId ${id} does not exist`
                )

            return chatRoom
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    async chatRoomByCriteria(criteria: ChatRoomCriteriaInput) {
        const { jobActionId, appointmentId } = criteria

        try {
            const chatRoom = await this.prisma.chatRoom.findFirst({
                where: {
                    OR: [
                        { jobAction: { id: jobActionId } },
                        {
                            appointment: {
                                id: appointmentId,
                            },
                        },
                    ],
                },
                include: {
                    messages: true,
                    jobAction: {
                        select: {
                            id: true,
                            applicant: true,
                            jobAdvert: true,
                        },
                    },
                    appointment: {
                        select: {
                            id: true,
                            applicant: true,
                        },
                    },
                },
            })
            if (!chatRoom)
                throw new NotFoundException(
                    `ChatRoom with jobActionId ${jobActionId} or appointmentId ${appointmentId} does not exist`
                )

            return chatRoom
        } catch (error) {
            console.log(error.message)
            throw new NotFoundException(error.message)
        }
    }

    update(id: string, updateChatRoomInput: UpdateChatRoomInput) {
        return this.prisma.chatRoom.update({
            where: {
                id: id,
            },
            data: updateChatRoomInput,
        })
    }

    async userHasAccessToChatRoom(
        isSuper: boolean,
        chatRoomId: string,
        applicantId?: string,
        companyUserId?: string
    ): Promise<boolean> {
        if (isSuper) {
            return true
        }

        const isAppointmentChatRoom =
            await this.isAppointmentChatRoom(chatRoomId)
        if (isAppointmentChatRoom) {
            return true
        }

        if (applicantId) {
            return (
                (await this.prisma.chatRoom.findFirst({
                    where: {
                        id: chatRoomId,
                        OR: [
                            {
                                jobAction: {
                                    applicantId: applicantId,
                                },
                            },
                            {
                                appointment: {
                                    applicantId: applicantId,
                                },
                            },
                        ],
                    },
                })) !== null
            )
        }

        if (companyUserId) {
            return (
                (await this.prisma.chatRoom.findFirst({
                    where: {
                        id: chatRoomId,
                        jobAction: {
                            jobAdvert: {
                                responsibleUsers: {
                                    some: {
                                        id: companyUserId,
                                    },
                                },
                            },
                        },
                    },
                })) !== null
            )
        }

        return false
    }

    async isAppointmentChatRoom(chatRoomId: string): Promise<boolean> {
        return (
            (await this.prisma.chatRoom.findFirst({
                where: {
                    id: chatRoomId,
                    appointment: {
                        isNot: null,
                    },
                },
            })) !== null
        )
    }

    remove(id: string) {
        const chatRoomToDelete = this.prisma.chatRoom.findUnique({
            where: { id: id },
        })
        if (!chatRoomToDelete) {
            throw new NotFoundException(`ChatRoom with ID ${id} does not exist`)
        }
        return this.prisma.chatRoom.delete({
            where: { id: id },
        })
    }
}
