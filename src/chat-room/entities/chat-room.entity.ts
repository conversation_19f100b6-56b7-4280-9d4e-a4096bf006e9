import { ObjectType, Field } from '@nestjs/graphql'
import { Message } from '../../message/entities/message.entity'
import { CompanyUser } from '../../company-user/entities/company-user.entity'
import { JobAction } from '../../job-actions/entities/job-action.entity'
import { Appointment } from '../../appointment/entities/appointment.entity'

@ObjectType()
export class ChatRoom {
    @Field()
    id: string

    @Field(() => JobAction, { nullable: true })
    jobAction?: JobAction

    @Field(() => Appointment, { nullable: true })
    appointment?: Appointment

    @Field(() => [Message])
    messages: Message[] = []

    @Field(() => [CompanyUser], { nullable: true })
    companyUsers?: CompanyUser[]

    @Field({ nullable: true })
    status: string
}
