import { Resolver, Query, Mutation, Args, Int, Context } from '@nestjs/graphql'
import { ChatRoomService } from './chat-room.service'
import { ChatRoom } from './entities/chat-room.entity'
import { CreateChatRoomInput } from './dto/create-chat-room.input'
import { UpdateChatRoomInput } from './dto/update-chat-room.input'
import { BadRequestException, Inject, UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { SuperGuard } from '../guards/super-guard'
import { ChatRoomCriteriaInput } from './dto/chatroom-criteria-input'

@Resolver(() => ChatRoom)
export class ChatRoomResolver {
    constructor(private readonly chatRoomService: ChatRoomService) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => ChatRoom)
    createChatRoom(
        @Context() context: any,
        @Args('jobActionId', { type: () => String }) jobActionId: string,
        @Args('companyUserId', { type: () => String, nullable: true })
        companyUserId: string,
        @Args('createChatRoomInput') createChatRoomInput: CreateChatRoomInput
    ) {
        const bCompanyUserId =
            context?.req?.user?.companyUserId || companyUserId

        return this.chatRoomService.create(
            jobActionId,
            bCompanyUserId,
        )
    }

    @UseGuards(SuperGuard)
    @Query(() => [ChatRoom], { name: 'allChatRooms' })
    findAll() {
        return this.chatRoomService.findAll()
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => ChatRoom, { name: 'chatRoomById' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.chatRoomService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => ChatRoom, { name: 'chatRoomByMatch' })
    findChatRoomByMatch(
        @Args('jobActionId', { type: () => String }) id: string
    ) {
        if (!id) {
            return null
        }
        return this.chatRoomService.chatRoomByMatch(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => ChatRoom, { name: 'chatRoomByCriteria' })
    findChatRoomByCriteria(
        @Args('criteria', { type: () => ChatRoomCriteriaInput })
        criteria: ChatRoomCriteriaInput
    ) {
        if (!criteria.jobActionId && !criteria.appointmentId) {
            throw new BadRequestException(
                'Either jobActionId or appointmentId must be provided'
            )
        }
        return this.chatRoomService.chatRoomByCriteria(criteria)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => ChatRoom)
    updateChatRoom(
        @Args('updateChatRoomInput') updateChatRoomInput: UpdateChatRoomInput
    ) {
        return this.chatRoomService.update(
            updateChatRoomInput.id,
            updateChatRoomInput
        )
    }

    @UseGuards(SuperGuard)
    @Mutation(() => ChatRoom)
    removeChatRoom(@Args('id', { type: () => String }) id: string) {
        return this.chatRoomService.remove(id)
    }
}
