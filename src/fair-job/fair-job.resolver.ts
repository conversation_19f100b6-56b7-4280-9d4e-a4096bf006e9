import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { FairJobService } from './fair-job.service';
import { FairJob } from './entities/fair-job.entity';
import { CreateFairJobInput } from './dto/create-fair-job.input';
import { UpdateFairJobInput } from './dto/update-fair-job.input';

@Resolver(() => FairJob)
export class FairJobResolver {
  constructor(private readonly fairJobService: FairJobService) {}

  @Mutation(() => FairJob)
  createFairJob(@Args('createFairJobInput') createFairJobInput: CreateFairJobInput) {
    return this.fairJobService.create(createFairJobInput);
  }

  @Query(() => [FairJob], { name: 'allFairJobs' })
  findAll() {
    return this.fairJobService.findAll();
  }

  @Query(() => FairJob, { name: 'getFairJob' })
  findOne(@Args('id', { type: () => String}) id: string) {
    return this.fairJobService.findOne(id);
  }

  @Mutation(() => FairJob)
  updateFairJob(@Args('updateFairJobInput') updateFairJobInput: UpdateFairJobInput) {
    return this.fairJobService.update(updateFairJobInput.id, updateFairJobInput);
  }

  @Mutation(() => FairJob)
  removeFairJob(@Args('id', { type: () => String}) id: string) {
    return this.fairJobService.remove(id);
  }

    @Query(() => FairJob, { name: 'getFairJobByTitle' })
    async getFairJobByTitle(@Args('title', { type: () => String }) title: string) {
        return this.fairJobService.findByTitle(title);
    }

    @Query(() => [FairJob], { name: 'getFairJobByCompanyParticipationId' })
    async getFairJobByCompanyParticipationId(@Args('companyParticipationId', { type: () => String }) companyParticipationId: string) {
        return this.fairJobService.findByCompanyParticipation(companyParticipationId);
    }

    @Query(() => [FairJob], { name: 'getFairJobByFairId' })
    async getFairJobByFairId(@Args('fairId', { type: () => String }) fairId: string) {
        return this.fairJobService.findByFair(fairId);
    }
}
