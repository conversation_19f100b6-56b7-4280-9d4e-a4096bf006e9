import { Test, TestingModule } from '@nestjs/testing';
import { FairJobResolver } from './fair-job.resolver';
import { FairJobService } from './fair-job.service';

describe('FairJobResolver', () => {
  let resolver: FairJobResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FairJobResolver, FairJobService],
    }).compile();

    resolver = module.get<FairJobResolver>(FairJobResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
