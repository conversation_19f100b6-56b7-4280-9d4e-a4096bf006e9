import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { CreateFairJobInput } from './dto/create-fair-job.input';
import { UpdateFairJobInput } from './dto/update-fair-job.input';

@Injectable()
export class FairJobService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createFairJobInput: CreateFairJobInput) {
    const { title } = createFairJobInput;

    const existingJob = await this.prisma.fairJob.findFirst({
      where: { title }
    });

    if (existingJob) {
      throw new Error('A fair job with this title already exists');
    }

    return this.prisma.fairJob.create({
      data: { title },
      include: {
        companyFairJobs: {
          include: {
            companyFairParticipation: {
              include: {
                company: true,
                fair: true
              }
            }
          }
        }
      }
    });
  }

  async findAll() {
    return this.prisma.fairJob.findMany({
      include: {
        companyFairJobs: {
          include: {
            companyFairParticipation: {
              include: {
                company: true,
                fair: true
              }
            }
          }
        }
      }
    });
  }

  async findOne(id: string) {
    const fairJob = await this.prisma.fairJob.findUnique({
      where: { id },
      include: {
        companyFairJobs: {
          include: {
            companyFairParticipation: {
              include: {
                company: true,
                fair: true
              }
            }
          }
        }
      }
    });

    if (!fairJob) {
      throw new NotFoundException('Fair job not found');
    }

    return fairJob;
  }

  async update(id: string, updateFairJobInput: UpdateFairJobInput) {
    const { title } = updateFairJobInput;

    await this.findOne(id);

    if (title) {
      const existingJob = await this.prisma.fairJob.findFirst({
        where: {
          title,
          NOT: { id }
        }
      });

      if (existingJob) {
        throw new Error('A fair job with this title already exists');
      }
    }

    return this.prisma.fairJob.update({
      where: { id },
      data: { title },
      include: {
        companyFairJobs: {
          include: {
            companyFairParticipation: {
              include: {
                company: true,
                fair: true
              }
            }
          }
        }
      }
    });
  }

  async remove(id: string) {
    await this.findOne(id);

    return this.prisma.fairJob.delete({
      where: { id }
    });
  }

  async findByTitle(title: string) {
    return this.prisma.fairJob.findFirst({
      where: { title },
      include: {
        companyFairJobs: {
          include: {
            companyFairParticipation: {
              include: {
                company: true,
                fair: true
              }
            }
          }
        }
      }
    });
  }

  async findByCompanyParticipation(participationId: string) {
    return this.prisma.fairJob.findMany({
      where: {
        companyFairJobs: {
          some: {
            companyFairParticipationId: participationId
          }
        }
      },
      include: {
        companyFairJobs: {
          include: {
            companyFairParticipation: {
              include: {
                company: true,
                fair: true
              }
            }
          }
        }
      }
    });
  }

  async findByFair(fairId: string) {
    return this.prisma.fairJob.findMany({
      where: {
        companyFairJobs: {
          some: {
            companyFairParticipation: {
              fairId
            }
          }
        }
      },
      include: {
        companyFairJobs: {
          include: {
            companyFairParticipation: {
              include: {
                company: true,
                fair: true
              }
            }
          }
        }
      }
    });
  }
}
