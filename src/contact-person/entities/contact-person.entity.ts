import { ObjectType, Field } from '@nestjs/graphql'
import { Company } from '../../company/entities/company.entity'
import { CompanyFairContactPerson } from '../../company-fair-contact-person/entities/company-fair-contact-person.entity'

@ObjectType()
export class ContactPerson {
  @Field()
  id: string

  @Field()
  name: string

  @Field({ nullable: true })
  position: string

  @Field({ nullable: true })
  email: string

  @Field({ nullable: true })
  phone: string

  @Field(() => Company)
  company: Company

  @Field()
  companyId: string

  @Field(() => [CompanyFairContactPerson])
  companyFairContactPersons: CompanyFairContactPerson[]
}