import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateContactPersonInput } from './dto/create-contact-person.input'
import { UpdateContactPersonInput } from './dto/update-contact-person.input'

@Injectable()
export class ContactPersonService {
    constructor(private readonly prisma: PrismaService) {}

    async create(createContactPersonInput: CreateContactPersonInput) {
        const { companyId, email, ...rest } = createContactPersonInput

        const company = await this.prisma.company.findUnique({
            where: { id: companyId },
        })

        if (!company) {
            throw new NotFoundException('Company not found')
        }

        return this.prisma.contactPerson.create({
            data: {
                ...rest,
                email,
                company: {
                    connect: { id: companyId },
                },
            },
            include: {
                company: true,
                companyFairContactPersons: {
                    include: {
                        companyFairParticipation: {
                            include: {
                                fair: true,
                            },
                        },
                        ContactPersonTimeslot: true,
                    },
                },
            },
        })
    }

    async findAll() {
        return this.prisma.contactPerson.findMany({
            include: {
                company: true,
                companyFairContactPersons: {
                    include: {
                        companyFairParticipation: {
                            include: {
                                fair: true,
                            },
                        },
                        ContactPersonTimeslot: true,
                    },
                },
            },
        })
    }

    async findOne(id: string) {
        const contactPerson = await this.prisma.contactPerson.findUnique({
            where: { id },
            include: {
                company: true,
                companyFairContactPersons: {
                    include: {
                        companyFairParticipation: {
                            include: {
                                fair: true,
                            },
                        },
                        ContactPersonTimeslot: true,
                    },
                },
            },
        })

        if (!contactPerson) {
            throw new NotFoundException('Contact person not found')
        }

        return contactPerson
    }

    async update(
        id: string,
        updateContactPersonInput: UpdateContactPersonInput
    ) {
        const { email, ...rest } = updateContactPersonInput

        return this.prisma.contactPerson.update({
            where: { id },
            data: {
                ...rest,
                email,
            },
            include: {
                company: true,
                companyFairContactPersons: {
                    include: {
                        companyFairParticipation: {
                            include: {
                                fair: true,
                            },
                        },
                        ContactPersonTimeslot: true,
                    },
                },
            },
        })
    }

    async remove(id: string) {
        await this.findOne(id)

        return this.prisma.contactPerson.delete({
            where: { id },
        })
    }

    async findByCompany(companyId: string) {
        return this.prisma.contactPerson.findMany({
            where: { companyId },
            include: {
                company: true,
                companyFairContactPersons: {
                    include: {
                        companyFairParticipation: {
                            include: {
                                fair: true,
                            },
                        },
                        ContactPersonTimeslot: true,
                    },
                },
            },
        })
    }

    async findByEmail(email: string) {
        return this.prisma.contactPerson.findFirst({
            where: { email },
            include: {
                company: true,
                companyFairContactPersons: {
                    include: {
                        companyFairParticipation: {
                            include: {
                                fair: true,
                            },
                        },
                        ContactPersonTimeslot: true,
                    },
                },
            },
        })
    }

    async findByFair(fairId: string) {
        return this.prisma.contactPerson.findMany({
            where: {
                companyFairContactPersons: {
                    some: {
                        companyFairParticipation: {
                            fairId,
                        },
                    },
                },
            },
            include: {
                company: true,
                companyFairContactPersons: {
                    include: {
                        companyFairParticipation: {
                            include: {
                                fair: true,
                            },
                        },
                        ContactPersonTimeslot: true,
                    },
                },
            },
        })
    }

    async isValidContactPerson(
        contactPersonId: string,
        companyId: string
    ): Promise<boolean> {
        const companyFairContactPerson =
            await this.prisma.companyFairContactPerson.findUnique({
                where: {
                    id: contactPersonId,
                },
                include: {
                    contactPerson: {
                        include: {
                            company: true,
                        },
                    },
                },
            })

        console.log(companyFairContactPerson)

        if (!companyFairContactPerson) {
            throw new NotFoundException('Contact person not found')
        }

        if (
            companyId &&
            companyFairContactPerson.contactPerson?.company?.id !== companyId
        ) {
            throw new NotFoundException(
                'Contact person does not belong to this company'
            )
        }

        return true
    }
}
