import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional, IsEmail } from 'class-validator'

@InputType()
export class CreateContactPersonInput {
  @IsString()
  @Field()
  name: string

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  position?: string

  @IsEmail()
  @IsOptional()
  @Field({ nullable: true })
  email?: string

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  phone?: string

  @IsString()
  @Field()
  companyId: string
}