import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { ContactPersonService } from './contact-person.service';
import { ContactPerson } from './entities/contact-person.entity';
import { CreateContactPersonInput } from './dto/create-contact-person.input';
import { UpdateContactPersonInput } from './dto/update-contact-person.input';

@Resolver(() => ContactPerson)
export class ContactPersonResolver {
  constructor(private readonly contactPersonService: ContactPersonService) {}

  @Mutation(() => ContactPerson)
  createContactPerson(@Args('createContactPersonInput') createContactPersonInput: CreateContactPersonInput) {
    return this.contactPersonService.create(createContactPersonInput);
  }

  @Query(() => [ContactPerson], { name: 'allContactPersons' })
  findAll() {
    return this.contactPersonService.findAll();
  }

  @Query(() => ContactPerson, { name: 'getContactPerson' })
  findOne(@Args('id', { type: () => String}) id: string) {
    return this.contactPersonService.findOne(id);
  }

  @Mutation(() => ContactPerson)
  updateContactPerson(@Args('updateContactPersonInput') updateContactPersonInput: UpdateContactPersonInput) {
    return this.contactPersonService.update(updateContactPersonInput.id, updateContactPersonInput);
  }

  @Mutation(() => ContactPerson)
  removeContactPerson(@Args('id', { type: () => String}) id: string) {
    return this.contactPersonService.remove(id);
  }

    @Query(() => [ContactPerson], { name: 'contactPersonsByCompanyId' })
    findByCompanyId(@Args('companyId', { type: () => String }) companyId: string) {
      return this.contactPersonService.findByCompany(companyId);
    }

    @Query(() => ContactPerson, { name: 'contactPersonByEmail' })
    findByEmail(@Args('email', { type: () => String }) email: string) {
      return this.contactPersonService.findByEmail(email);
    }

    @Query(() => [ContactPerson], { name: 'contactPersonsByFairId' })
    findByFairId(@Args('fairId', { type: () => String }) fairId: string) {
      return this.contactPersonService.findByFair(fairId);
    }
}
