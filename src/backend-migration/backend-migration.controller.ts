import { <PERSON>, Post, Body, BadRequestException } from '@nestjs/common'
import { BackendMigrationService } from './backend-migration.service'

@Controller('migration')
export class BackendMigrationController {
    constructor(private readonly migrationService: BackendMigrationService) {}

    @Post('run')
    async runMigration(@Body('password') password: string) {
        // Check if password is provided and correct
        if (!password || password !== 'f8wRd#p9x7f132d') {
            throw new BadRequestException('Invalid password')
        }

        await this.migrationService.runMigration()
        return { message: 'Migration completed successfully' }
    }
}
