import { Injectable } from '@nestjs/common'
import { PrismaService } from 'src/prisma.service'
import admin from 'firebase-admin'
import { firestore } from 'firebase-admin'
import { getDownloadURL } from 'firebase-admin/storage'
import { JobAdvertType, ActionState } from '@prisma/client'
// import pLimit from 'p-limit'

@Injectable()
export class BackendMigrationService {
    constructor(private readonly prisma: PrismaService) {}

    // ID Mappings to store old Firestore IDs to new SQL IDs
    private idMappings = {
        applicants: new Map<string, string>(), // Firestore ID -> SQL ID (Applicant)
        companies: new Map<string, string>(), // Firestore ID -> SQL ID (Company)
        companyUsers: new Map<string, string>(), // Firestore ID -> SQL ID (CompanyUser)
        jobAds: new Map<string, string>(), // Firestore ID -> SQL ID (JobAdvert)
        users: new Map<string, string>(), // Firestore ID -> SQL ID (User)
    }

    private bucketOld = admin.storage().bucket('bridge-public.appspot.com')
    private bucketNew = admin.storage().bucket('bridge-public-v2')

    // Copy files to the new storage location and return the new URL
    private async copyFileAndGetUrl(
        oldUrl: string,
        destination: string
    ): Promise<string> {
        try {
            const decodedUrl = decodeURIComponent(oldUrl)
            const regex = /\/o\/(.*?)\?alt=media/
            const match = decodedUrl.match(regex)
            if (!match || !match[1]) {
                throw new Error(`Invalid old file URL: ${oldUrl}`)
            }

            const oldFilePath = match[1]
            const oldFile = this.bucketOld.file(oldFilePath)
            const newFile = this.bucketNew.file(destination)
            await oldFile.copy(newFile)

            const downloadUrl = await getDownloadURL(newFile)
            console.log(`File copied from ${oldUrl} to ${destination}`)
            return downloadUrl
        } catch (error) {
            console.error(
                `Error copying file from ${oldUrl} to ${destination}:`,
                error
            )
            return
        }
    }

    // Function to set Firebase custom claims
    private async setFirebaseClaims(
        firebaseUid: string,
        claims: object
    ): Promise<void> {
        try {
            // TODO: Uncomment – commented out to test the Firestore migration without changing current claims which are still needed in old backend
            await admin.auth().setCustomUserClaims(firebaseUid, claims)
            console.log(`Claims set for user: ${firebaseUid}`, claims)
        } catch (error) {
            console.error(
                `Failed to set claims for user: ${firebaseUid}`,
                error
            )
        }
    }

    // Migration der Users und CompanyUsers
    async migrateUsersAndCompanyUsers(): Promise<void> {
        try {
            const usersRef = firestore().collection('users')
            const usersSnapshot = await usersRef.get()

            for (const doc of usersSnapshot.docs) {
                const userData = doc.data()

                // Erstelle den User in Prisma
                const user = await this.prisma.user.create({
                    data: {
                        firebaseUid: doc.id,
                        createdAt: doc.createTime.toDate(),
                    },
                })

                // Migriere den CompanyUser, falls vorhanden
                if (userData.companyId) {
                    console.log('userEmail::', userData.email)
                    const companyUser = await this.prisma.companyUser.create({
                        data: {
                            user: {
                                connect: { id: user.id },
                            },
                            email: userData.email,
                            name: userData.name,
                            createdAt: doc.createTime.toDate(),
                        },
                    })

                    this.idMappings.companyUsers.set(doc.id, companyUser.id)

                    const userClaims = {
                        companyUserId: companyUser.id,
                        bridgeUserId: user.id,
                    }

                    await this.setFirebaseClaims(doc.id, userClaims)
                }

                this.idMappings.users.set(doc.id, user.id)
            }

            console.log('Users und CompanyUsers migriert.')
        } catch (error) {
            console.error(
                `Fehler beim Migrieren der Users und CompanyUsers:`,
                error
            )
            throw error
        }
    }

    // Migration der Applicants
    async migrateApplicants(): Promise<void> {
        try {
            const lastActiveDate = firestore.Timestamp.fromDate(
                new Date('2024-01-01')
            )
            const applicantsRef = firestore()
                .collection('applicants')
                .where('lastActive', '>=', lastActiveDate)
            const applicantsSnapshot = await applicantsRef.get()

            for (const doc of applicantsSnapshot.docs) {
                const applicantData = doc.data()

                // Erstelle den User in Prisma
                const user = await this.prisma.user.create({
                    data: {
                        firebaseUid: doc.id,
                        createdAt: doc.createTime.toDate(),
                    },
                })

                // Erstelle den Applicant in Prisma
                const applicant = await this.prisma.applicant.create({
                    data: {
                        user: {
                            connect: { id: user.id },
                        },
                        firstName: applicantData.firstName,
                        lastName: applicantData.lastName,
                        birthDate: applicantData.birthday?.toDate(),
                        availableFrom: applicantData.availableFrom?.toDate(),
                        city: applicantData.city,
                        description: applicantData.description,
                        environment: applicantData.environment?.id,
                        personality: applicantData.personality?.id,
                        strengths: applicantData.strengths,
                        weaknesses: applicantData.weaknesses,
                        subjects: applicantData.subjects,
                        receiveNotifications:
                            applicantData.receiveNotifications,
                        graduation: applicantData.graduation,
                        schoolName: applicantData.schoolName,
                        createdAt: doc.createTime.toDate(),
                        lastActive: applicantData.lastActive?.toDate(),
                    },
                })

                this.idMappings.applicants.set(doc.id, applicant.id)

                // Migriere das Profilbild des Applicants
                if (
                    applicantData.profileImage &&
                    !applicantData.profileImage.includes('defaultProfileImage')
                ) {
                    const destination = `applicants/${applicant.id}/profileImage.jpeg`
                    const newProfileImageUrl = await this.copyFileAndGetUrl(
                        applicantData.profileImage,
                        destination
                    )

                    await this.prisma.applicant.update({
                        where: { id: applicant.id },
                        data: { profileImageUrl: newProfileImageUrl },
                    })
                }

                // Migriere die Dokumente des Applicants
                const documents = applicantData.documents || []
                for (const document of documents) {
                    const documentDestination = `applicants/${applicant.id}/${document.storage}`
                    const documentUrl = await this.copyFileAndGetUrl(
                        document.url,
                        documentDestination
                    )

                    let documentPreviewUrl = undefined
                    if (document.documentPreviewUrl) {
                        const documentPreviewDestination = `applicants/${applicant.id}/prev-${document.storage}`
                        documentPreviewUrl = await this.copyFileAndGetUrl(
                            document.documentPreviewUrl,
                            documentPreviewDestination
                        )
                    }

                    await this.prisma.applicantDocument.create({
                        data: {
                            applicant: { connect: { id: applicant.id } },
                            name: document.name,
                            url: documentUrl,
                            documentPreviewUrl: documentPreviewUrl,
                        },
                    })
                }

                // Setze die Firebase Claims für den Applicant
                const applicantClaims = {
                    applicantId: applicant.id,
                    bridgeUserId: user.id,
                }

                await this.setFirebaseClaims(doc.id, applicantClaims)
            }

            console.log('Applicants migriert.')
        } catch (error) {
            console.error(`Fehler beim Migrieren der Applicants:`, error)
            throw error
        }
    }

    // Migration der Companies und JobAds
    async migrateCompaniesAndJobAds(): Promise<void> {
        try {
            const jobCategories = await this.prisma.jobCategory.findMany()
            const categoryMap = new Map<string, string>()
            for (const category of jobCategories) {
                categoryMap.set(category.name, category.id)
            }

            const companiesRef = firestore().collection('companies')
            const companiesSnapshot = await companiesRef.get()

            for (const doc of companiesSnapshot.docs) {
                const companyData = doc.data()
                const oldCompanyId = doc.id

                // Erstelle die Company in Prisma
                const company = await this.prisma.company.create({
                    data: {
                        name: companyData.companyName,
                        address: companyData.address,
                        city: companyData.city,
                        country: 'Deutschland',
                        detailContent: companyData.detailContent,
                        latitude: companyData.coordinates?.latitude,
                        longitude: companyData.coordinates?.longitude,
                        foundingYear: +companyData.foundingYear,
                        totalEmployees: +companyData.mitarbeiter,
                        createdAt: doc.createTime.toDate(),
                    },
                })

                this.idMappings.companies.set(oldCompanyId, company.id)

                // Migriere Header- und Logo-Bilder der Company
                if (companyData.headerImage) {
                    const headerImageDestination = `companies/${company.id}/headerImage.jpeg`
                    const newHeaderImageUrl = await this.copyFileAndGetUrl(
                        companyData.headerImage,
                        headerImageDestination
                    )

                    await this.prisma.company.update({
                        where: { id: company.id },
                        data: { headerImageUrl: newHeaderImageUrl },
                    })
                }

                if (companyData.logoImage) {
                    const logoImageDestination = `companies/${company.id}/logoImage.jpeg`
                    const newLogoImageUrl = await this.copyFileAndGetUrl(
                        companyData.logoImage,
                        logoImageDestination
                    )

                    await this.prisma.company.update({
                        where: { id: company.id },
                        data: { logoImageUrl: newLogoImageUrl },
                    })
                }

                // Migriere die JobAds der Company
                const jobAdsRef = doc.ref
                    .collection('jobAds')
                    .where('deleted', '==', false)
                const jobAdsSnapshot = await jobAdsRef.get()

                const parseGehalt = (
                    value: string | number | null | undefined
                ): number | null => {
                    if (value === null || value === undefined || value === '')
                        return 0
                    const parsed =
                        typeof value === 'string' ? parseFloat(value) : value
                    return isNaN(parsed) ? 0 : parsed
                }

                for (const jobAdDoc of jobAdsSnapshot.docs) {
                    const jobAdData = jobAdDoc.data()
                    const oldJobAdId = jobAdDoc.id

                    const jobAdvert = await this.prisma.jobAdvert.create({
                        data: {
                            company: { connect: { id: company.id } },
                            district: jobAdData.city,
                            title: jobAdData.title,
                            description: jobAdData.description,
                            city: jobAdData.city,
                            activeFromDate: jobAdData.activeFromDate?.toDate(),
                            address: jobAdData.address,
                            approved: jobAdData.approved,
                            latitude: jobAdData.coordinates?.latitude,
                            longitude: jobAdData.coordinates?.longitude,
                            createdAt: jobAdDoc.createTime.toDate(),
                            isDeclined: jobAdData.declined ?? false,
                            detailDescription: jobAdData.detailDescription,
                            educationDuration: jobAdData.educationDuration,
                            type:
                                jobAdData.jobAdType === 'ausbildung'
                                    ? JobAdvertType.AUSBILDUNG
                                    : JobAdvertType.PRAKTIKUM,
                            gehalt:
                                jobAdData.jobAdType === 'ausbildung'
                                    ? [
                                          parseGehalt(jobAdData.gehalt),
                                          parseGehalt(jobAdData.gehalt2),
                                          parseGehalt(jobAdData.gehalt3),
                                      ]
                                    : [],
                            holidayDays: +jobAdData.holidayDays,
                            paused: jobAdData.pause,
                            startDate: jobAdData.startDate?.toDate(),
                            workHours: parseInt(jobAdData.workHours ?? '0'),
                            categories: {
                                connect: jobAdData.categories.map(
                                    (categoryTitle: string) => {
                                        const categoryId = categoryMap.get(
                                            categoryTitle.trim()
                                        )
                                        if (!categoryId) {
                                            throw new Error(
                                                `Kategorie mit Titel "${categoryTitle}" nicht gefunden`
                                            )
                                        }
                                        return { id: categoryId }
                                    }
                                ),
                            },
                            responsibleUsers: {
                                connect: jobAdData.responsibleUsersIds.map(
                                    (id: string) => {
                                        const mappedId =
                                            this.idMappings.companyUsers.get(id)
                                        if (!mappedId) {
                                            throw new Error(
                                                `CompanyUser ID mapping nicht gefunden für User ID:: ${id}`
                                            )
                                        }
                                        return { id: mappedId }
                                    }
                                ),
                            },
                        },
                    })

                    this.idMappings.jobAds.set(oldJobAdId, jobAdvert.id)

                    // Migriere das Header-Bild der JobAd
                    if (jobAdData.imageUrl) {
                        const jobAdImageDestination = `companies/${company.id}/jobAds/${jobAdvert.id}.jpeg`
                        const newJobAdImageUrl = await this.copyFileAndGetUrl(
                            jobAdData.imageUrl,
                            jobAdImageDestination
                        )

                        await this.prisma.jobAdvert.update({
                            where: { id: jobAdvert.id },
                            data: { headerImageUrl: newJobAdImageUrl },
                        })
                    }
                }
            }

            console.log('Companies und JobAds migriert.')
        } catch (error) {
            console.error(
                `Fehler beim Migrieren der Companies und JobAds:`,
                error
            )
            throw error
        }
    }

    // Aktualisiere die CompanyUser mit activeCompanyId, avatarImage und migriere Rechte
    async updateCompanyUserActiveCompanyAndAvatar(): Promise<void> {
        try {
            const usersRef = firestore().collection('users')
            const usersSnapshot = await usersRef.get()

            for (const doc of usersSnapshot.docs) {
                const userData = doc.data()
                const companyId = userData.companyId

                if (companyId) {
                    const prismaCompanyId =
                        this.idMappings.companies.get(companyId)
                    const companyUserId = this.idMappings.companyUsers.get(
                        doc.id
                    )

                    if (prismaCompanyId && companyUserId) {
                        // Aktualisiere activeCompanyId
                        await this.prisma.companyUser.update({
                            where: { id: companyUserId },
                            data: {
                                activeCompanyId: prismaCompanyId,
                                companies: {
                                    connect: {
                                        id: prismaCompanyId,
                                    },
                                },
                            },
                        })

                        // Aktualisiere Firebase Claims mit companyId
                        const userClaims = {
                            companyUserId: companyUserId,
                            companyId: prismaCompanyId,
                            bridgeUserId: this.idMappings.users.get(doc.id),
                        }

                        await this.setFirebaseClaims(doc.id, userClaims)

                        // Migriere das Avatar-Bild des CompanyUsers
                        if (
                            userData.avatarImage &&
                            !userData.avatarImage.includes(
                                'defaultProfileImage'
                            )
                        ) {
                            const destination = `companies/${prismaCompanyId}/users/${companyUserId}.jpeg`
                            const newAvatarUrl = await this.copyFileAndGetUrl(
                                userData.avatarImage,
                                destination
                            )

                            await this.prisma.companyUser.update({
                                where: { id: companyUserId },
                                data: { avatarImageUrl: newAvatarUrl },
                            })
                        }

                        // Migriere die Rechte des CompanyUsers
                        if (userData.rights) {
                            const {
                                createJobAd,
                                createUser,
                                editCompany,
                                superAdmin,
                            } = userData.rights

                            await this.prisma.userRights.create({
                                data: {
                                    superAdmin: superAdmin || false,
                                    createJobAd: createJobAd || false,
                                    createUser: createUser || false,
                                    editCompany: editCompany || false,
                                    company: {
                                        connect: { id: prismaCompanyId },
                                    },
                                    companyUser: {
                                        connect: { id: companyUserId },
                                    },
                                },
                            })
                        }
                    }
                }
            }

            console.log('CompanyUsers aktualisiert und Rechte migriert.')
        } catch (error) {
            console.error(`Fehler beim Aktualisieren der CompanyUsers:`, error)
            throw error
        }
    }

    /**
     * Migriert die JobActions für einen gegebenen Applicant.
     * @param firestoreApplicantId - Die Firestore ID des Applicants.
     * @param sqlApplicantId - Die SQL ID des Applicants.
     */
    async migrateJobActions(
        firestoreApplicantId: string,
        sqlApplicantId: string
    ): Promise<void> {
        try {
            const adsSeenRef = firestore()
                .collection('applicants')
                .doc(firestoreApplicantId)
                .collection('adsSeen')
            const adsSeenSnapshot = await adsSeenRef.get()

            for (const doc of adsSeenSnapshot.docs) {
                const adsSeenData = doc.data()
                const jobAdId = doc.id
                const sqlJobAdId = this.idMappings.jobAds.get(jobAdId)

                console.log('sqlJobAdId', sqlJobAdId)

                if (!sqlJobAdId) {
                    console.warn(
                        `JobAd ID mapping nicht gefunden für JobAd ID: ${jobAdId}`
                    )
                    continue
                }

                const { isLike, isBookmarked, isDislike, isMatch, companyId } =
                    adsSeenData

                // Bestimmen des ActionState basierend auf den Feldern
                let actionState: ActionState | null = null
                if (isDislike) {
                    actionState = ActionState.DISLIKED
                } else if (isMatch) {
                    actionState = ActionState.MATCHED
                } else if (isBookmarked) {
                    actionState = ActionState.BOOKMARKED
                } else if (isLike) {
                    actionState = ActionState.LIKED
                }

                if (actionState) {
                    // Erstellung des JobAction Eintrags
                    const jobAction = await this.prisma.jobAction.create({
                        data: {
                            jobAdvert: {
                                connect: { id: sqlJobAdId },
                            },
                            applicant: {
                                connect: { id: sqlApplicantId },
                            },
                            state: actionState,
                        },
                    })

                    // Erstelle einen initialen JobActionHistory Eintrag
                    await this.prisma.jobActionHistory.create({
                        data: {
                            jobAction: {
                                connect: { id: jobAction.id },
                            },
                            prevState: null,
                            newState: actionState,
                            type: 'INITIAL',
                        },
                    })

                    // Migriere die ChatRooms, falls vorhanden
                    if (isMatch) {
                        // Die Chat-Dokumente haben die Felder applicantId, companyId und jobAdId
                        const chatRef = firestore()
                            .collection('chats')
                            .where('applicant', '==', firestoreApplicantId)
                            .where('jobAdId', '==', jobAdId)
                            .limit(1)
                        const chatSnapshot = await chatRef.get()

                        if (!chatSnapshot.empty) {
                            const chatDoc = chatSnapshot.docs[0]
                            const chatData = chatDoc.data()

                            // Holen der SQL IDs für companyId und jobAdId
                            const sqlCompanyId = this.idMappings.companies.get(
                                chatData.companyId
                            )
                            console.log('sqlCompanyId', sqlCompanyId)
                            if (!sqlCompanyId) {
                                console.warn(
                                    `Company ID mapping nicht gefunden für Company ID: ${chatData.companyId}`
                                )
                                continue
                            }

                            const chatRoom = await this.prisma.chatRoom.create({
                                data: {
                                    status: 'active',
                                    jobAction: {
                                        connect: { id: jobAction.id },
                                    },
                                    createdAt: chatDoc.createTime.toDate(),
                                },
                            })

                            // Migriere die Nachrichten
                            const messagesSnapshot = await chatDoc.ref
                                .collection('messages')
                                .where('author', '!=', 'system')
                                .orderBy('timestamp')
                                .get()
                            for (const msgDoc of messagesSnapshot.docs) {
                                const msgData = msgDoc.data()
                                const authorId = msgData.author // ID des Authors (entweder applicantId oder companyUserId)

                                // Bestimme, ob der Author ein Applicant oder ein CompanyUser ist
                                let authorRelation = {}
                                if (
                                    this.idMappings.companyUsers.has(authorId)
                                ) {
                                    // Author ist ein CompanyUser
                                    authorRelation = {
                                        companyUser: {
                                            connect: {
                                                id: this.idMappings.companyUsers.get(
                                                    authorId
                                                ),
                                            },
                                        },
                                    }
                                } else if (
                                    this.idMappings.applicants.has(authorId)
                                ) {
                                    // Author ist ein Applicant
                                    authorRelation = {
                                        applicant: {
                                            connect: {
                                                id: this.idMappings.applicants.get(
                                                    authorId
                                                ),
                                            },
                                        },
                                    }
                                } else {
                                    console.warn(
                                        `Author ID mapping nicht gefunden für Author ID: ${authorId}`
                                    )
                                    continue // Überspringe diese Nachricht, da keine gültige Verbindung besteht
                                }

                                await this.prisma.message.create({
                                    data: {
                                        chatRoom: {
                                            connect: { id: chatRoom.id },
                                        },
                                        content: msgData.message,
                                        createdAt: msgData.timestamp.toDate(),
                                        authorName: msgData.authorName,
                                        isCompany: msgData.fromCompany,
                                        isApplicant: !msgData.fromCompany,
                                        ...authorRelation, // Verbindung zum Author herstellen
                                    },
                                })
                            }
                        }
                    }

                    // Migriere das Status-Feld aus der 'likes' Subcollection
                    if (isLike || isMatch) {
                        // Zugriff auf die 'likes' Subcollection unter dem entsprechenden jobAd
                        const likeDocRef = firestore()
                            .collection('companies')
                            .doc(companyId)
                            .collection('jobAds')
                            .doc(jobAdId)
                            .collection('likes')
                            .doc(firestoreApplicantId)
                        const likeDoc = await likeDocRef.get()

                        if (likeDoc.exists) {
                            const likeData = likeDoc.data()
                            const statusFromLike = likeData?.status

                            if (statusFromLike) {
                                // Aktualisiere die 'status' Spalte in der JobAction
                                await this.prisma.jobAction.update({
                                    where: { id: jobAction.id },
                                    data: { status: statusFromLike },
                                })
                            }
                        }
                    }
                }
            }

            console.log(
                `JobActions migriert für Applicant ID: ${firestoreApplicantId}`
            )
        } catch (error) {
            console.error(
                `Fehler beim Migrieren der JobActions für Applicant ID: ${firestoreApplicantId} -`,
                error
            )
            throw error
        }
    }

    /**
     * Migriert alle JobActions für alle Applicants.
     */
    // async migrateAllJobActions(): Promise<void> {
    //     try {
    //         const lastActiveDate = firestore.Timestamp.fromDate(
    //             new Date('2024-01-01')
    //         )
    //         const applicantsRef = firestore()
    //             .collection('applicants')
    //             .where('lastActive', '>=', lastActiveDate)
    //         const applicantsSnapshot = await applicantsRef.get()
    //
    //         const limit = pLimit(10) // Limit auf 10 gleichzeitige Promises
    //         const migrationPromises = applicantsSnapshot.docs.map((doc) =>
    //             limit(async () => {
    //                 const firestoreApplicantId = doc.id
    //                 const sqlApplicantId =
    //                     this.idMappings.applicants.get(firestoreApplicantId)
    //
    //                 if (!sqlApplicantId) {
    //                     console.warn(
    //                         `Applicant ID mapping nicht gefunden für Applicant ID: ${firestoreApplicantId}`
    //                     )
    //                     return
    //                 }
    //
    //                 await this.migrateJobActions(
    //                     firestoreApplicantId,
    //                     sqlApplicantId
    //                 )
    //             })
    //         )
    //
    //         await Promise.all(migrationPromises)
    //         console.log('Alle JobActions migriert.')
    //     } catch (error) {
    //         console.error(`Fehler beim Migrieren der JobActions:`, error)
    //         throw error
    //     }
    // }

    async migrateAllJobActions(): Promise<void> {
        try {
            const lastActiveDate = firestore.Timestamp.fromDate(
                new Date('2024-01-01')
            )
            const applicantsRef = firestore()
                .collection('applicants')
                .where('lastActive', '>=', lastActiveDate)
            const applicantsSnapshot = await applicantsRef.get()

            // Process applicants sequentially
            for (const doc of applicantsSnapshot.docs) {
                const firestoreApplicantId = doc.id
                const sqlApplicantId =
                    this.idMappings.applicants.get(firestoreApplicantId)

                if (!sqlApplicantId) {
                    console.warn(
                        `Applicant ID mapping nicht gefunden für Applicant ID: ${firestoreApplicantId}`
                    )
                    continue
                }

                await this.migrateJobActions(
                    firestoreApplicantId,
                    sqlApplicantId
                )
            }

            console.log('Alle JobActions migriert.')
        } catch (error) {
            console.error(`Fehler beim Migrieren der JobActions:`, error)
            throw error
        }
    }

    // Final step: Run all migrations
    async runMigration(): Promise<void> {
        try {
            console.log('Migration gestartet...')
            await this.migrateUsersAndCompanyUsers()
            await this.migrateApplicants()
            await this.migrateCompaniesAndJobAds()
            await this.updateCompanyUserActiveCompanyAndAvatar()
            await this.migrateAllJobActions() // Migriert alle JobActions
            console.log('Migration abgeschlossen!')
        } catch (error) {
            console.error('Fehler während der Migration:', error)
        } finally {
            await this.prisma.$disconnect()
        }
    }
}
