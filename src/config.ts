import * as process from 'process'

const configs = () => ({
    NODE_ENV: process.env.NODE_ENV || 'development',
    FIREBASE: {
        FIREBASE_PROJECT_ID:
            process.env.NODE_ENV === 'production'
                ? process.env.FIREBASE_PROJECT_ID
                : process.env.FIREBASE_DEV_PROJECT_ID,
        FIREBASE_CLIENT_EMAIL:
            process.env.NODE_ENV === 'production'
                ? process.env.FIREBASE_CLIENT_EMAIL
                : process.env.FIREBASE_DEV_CLIENT_EMAIL,
        FIREBASE_PRIVATE_KEY_ID:
            process.env.NODE_ENV === 'production'
                ? process.env.FIREBASE_PRIVATE_KEY_ID
                : process.env.FIREBASE_DEV_PRIVATE_KEY_ID,
        FIREBASE_PRIVATE_KEY:
            process.env.NODE_ENV === 'production'
                ? process.env.FIREBASE_PRIVATE_KEY
                : process.env.FIREBASE_DEV_PRIVATE_KEY,
    },
    PUSHER: {
        PUSHER_KEY: process.env.PUSHER_KEY,
        PUSHER_CLUSTER: process.env.PUSHER_CLUSTER,
        PUSHER_APP_ID: process.env.PUSHER_APP_ID,
        PUSHER_SECRET: process.env.PUSHER_SECRET,
    },
    GLOBAL: {
        PORT: process.env.PORT || 8900,
        BRIDGE_FRONT_HOST:
            process.env.NODE_ENV === 'production'
                ? process.env.BRIDGE_FRONT_HOST_PROD
                : process.env.BRIDGE_FRONT_HOST_DEV,
    },
    AGORA_ID: process.env.AGORA_ID,
    AGORA_CERTIFICATE: process.env.AGORA_CERTIFICATE,
    STRIPE_CONFIG: {
        apiKey:
            process.env.NODE_ENV === 'production'
                ? process.env.STRIPE_SECRET_KEY_LIVE
                : process.env.STRIPE_SECRET_KEY_TEST,
        webhookConfig: {
            requestBodyProperty: 'rawBody',
            stripeSecrets: {
                account:
                    process.env.NODE_ENV === 'development'
                        ? process.env.STRIPE_WEBHOOK_SECRET_TEST
                        : process.env.STRIPE_WEBHOOK_SECRET_LIVE,
            },
        },
    },
    STRIPE_TAX_RATE_ID:
        process.env.NODE_ENV === 'development'
            ? process.env.STRIPE_TEST_TAX_RATE_ID
            : process.env.STRIPE_LIVE_TAX_RATE_ID,
    STRIPE_PRODUCT_PRICE_ID:
        process.env.NODE_ENV === 'development'
            ? process.env.STRIPE_TEST_PRICE_ID
            : process.env.STRIPE_LIVE_PRICE_ID,
    STRIPE_PRODUCT_DISCOUNTED_PRICE_ID:
        process.env.NODE_ENV === 'development'
            ? process.env.STRIPE_TEST_DISCOUNTED_PRICE_ID
            : process.env.STRIPE_LIVE_DISCOUNTED_PRICE_ID,
    BRANCH_KEY:
        process.env.NODE_ENV === 'development'
            ? process.env.BRANCH_KEY_TEST
            : process.env.BRANCH_KEY_LIVE,
})
export default configs
