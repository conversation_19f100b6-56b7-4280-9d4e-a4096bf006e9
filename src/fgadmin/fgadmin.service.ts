import { Injectable } from '@nestjs/common'
import { CreateFgadminInput } from './dto/create-fgadmin.input'
import { PrismaService } from '../prisma.service'
import { admin } from '../auth/firebase-admin.module'
import { FairUserType } from '@prisma/client'

@Injectable()
export class FgadminService {
    constructor(private readonly prisma: PrismaService) {}

    async create(userId: string, createFgadminInput: CreateFgadminInput) {
        const fgAdminExists = await this.prisma.fGAdmin.findUnique({
            where: {
                userId: userId,
            },
        })
        if (fgAdminExists) {
            throw new Error('FG Admin already exists')
        }

        const bridgeUser = await this.prisma.user.findUnique({
            where: {
                id: userId,
            },
        })

        const newFGAdmin = await this.prisma.fGAdmin.create({
            data: {
                ...createFgadminInput,
                type: FairUserType.FGADMIN,
                user: {
                    connect: {
                        id: userId,
                    },
                },
            },
            include: {
                user: true,
            },
        })

        const userMetaData = {
            fgAdminId: newFGAdmin?.id,
            isFGAdmin: true,
            bridgeUserId: bridgeUser?.id,
        }

        await admin
            .auth()
            .setCustomUserClaims(<string>bridgeUser?.firebaseUid, {
                ...userMetaData,
            })

        return newFGAdmin
    }

    async verifyFGAdmin(email: string) {
        const fgAdmin = await this.prisma.fGAdmin.findUnique({
            where: {
                email: email,
            },
        })
        console.log('fgAdmin', fgAdmin)

        if (!fgAdmin) {
            throw new Error('FG Admin not found')
        }

        return fgAdmin
    }

    async findAll() {
        return this.prisma.fGAdmin.findMany({
            include: {
                user: true,
            },
        })
    }

    async findOne(id: string) {
        return this.prisma.fGAdmin.findUnique({
            where: {
                id: id,
            },
            include: {
                user: true,
            },
        })
    }

    async update(id: string, updateFGAdminInput: Partial<CreateFgadminInput>) {
        return this.prisma.fGAdmin.update({
            where: {
                id: id,
            },
            data: updateFGAdminInput,
            include: {
                user: true,
            },
        })
    }

    async remove(id: string) {
        return this.prisma.fGAdmin.delete({
            where: {
                id: id,
            },
        })
    }
}
