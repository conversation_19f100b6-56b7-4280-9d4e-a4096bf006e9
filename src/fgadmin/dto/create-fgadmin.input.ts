import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional, IsEmail } from 'class-validator'
import { FairUserType } from '@prisma/client'

@InputType()
export class CreateFgadminInput {
  @Field()
  @IsEmail()
  email: string

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  name?: string

  @IsString()
  @Field({ nullable: true })
  type: FairUserType

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  avatarImageUrl?: string
}
