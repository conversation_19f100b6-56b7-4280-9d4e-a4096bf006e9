import { Test, TestingModule } from '@nestjs/testing';
import { FgadminResolver } from './fgadmin.resolver';
import { FgadminService } from './fgadmin.service';

describe('FgadminResolver', () => {
  let resolver: FgadminResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FgadminResolver, FgadminService],
    }).compile();

    resolver = module.get<FgadminResolver>(FgadminResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
