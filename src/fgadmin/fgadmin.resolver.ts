import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql'
import { FgadminService } from './fgadmin.service'
import { Fgadmin } from './entities/fgadmin.entity'
import { CreateFgadminInput } from './dto/create-fgadmin.input'
import { UpdateFgadminInput } from './dto/update-fgadmin.input'
import { UseGuards } from '@nestjs/common'
import { SuperGuard } from '../guards/super-guard'

@Resolver(() => Fgadmin)
export class FgadminResolver {
    constructor(private readonly fgadminService: FgadminService) {}

    @Mutation(() => Fgadmin)
    createFgadmin(
        @Args('createFgadminInput') createFgadminInput: CreateFgadminInput,
        @Args('id') id: string
    ) {
        return this.fgadminService.create(id, createFgadminInput)
    }

    @Query(() => [Fgadmin], { name: 'allFgadmins' })
    findAll() {
        return this.fgadminService.findAll()
    }

    @Query(() => Fgadmin, { name: 'getFgadmin' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.fgadminService.findOne(id)
    }

    @Mutation(() => Fgadmin, { name: 'verifyFGAdmin' })
    verifyFGAdmin(@Args('email', { type: () => String }) email: string) {
        return this.fgadminService.verifyFGAdmin(email)
    }

    @Mutation(() => Fgadmin)
    updateFgadmin(
        @Args('updateFgadminInput') updateFgadminInput: UpdateFgadminInput
    ) {
        return this.fgadminService.update(
            updateFgadminInput.id,
            updateFgadminInput
        )
    }

    @Mutation(() => Fgadmin)
    removeFgadmin(@Args('id', { type: () => String }) id: string) {
        return this.fgadminService.remove(id)
    }
}
