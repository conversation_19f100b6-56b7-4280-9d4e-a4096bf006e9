import { Injectable, UnauthorizedException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { admin } from '../auth/firebase-admin.module'
import { LoginRequestDto } from '../auth/authTypes/input-dto'

@Injectable()
export class TokenService {
    constructor(private configService: ConfigService) {}

    extractToken(connectionParams: any): string | null {
        return connectionParams?.token || null
    }

    async verifyToken(loginRequestDto: LoginRequestDto) {
        try {
            const decodedToken = await admin
                .auth()
                .verifyIdToken(loginRequestDto.token)
            return { user: decodedToken }
        } catch (error) {
            throw new UnauthorizedException()
        }
    }
}
