import { ObjectType, Field } from '@nestjs/graphql'
import { JobAdvert } from '../../job-advert/entities/job-advert.entity'
import { User } from '../../user/entities/user.entity'
import { Company } from '../../company/entities/company.entity'
import { UserRight } from '../../user-right/entities/user-right.entity'

@ObjectType()
export class CompanyUser {
    @Field({ description: 'Company User ID' })
    id: string

    @Field({ nullable: true })
    email: string

    @Field({ nullable: true })
    name: string

    @Field({ nullable: true })
    avatarImageUrl: string

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date

    @Field({ nullable: true })
    activeCompanyId: string

    @Field(() => [Company])
    companies: Company[]

    @Field(() => User)
    user: User

    @Field()
    userId: string

    @Field(() => [JobAdvert])
    jobAdverts: JobAdvert[]

    @Field(() => [UserRight])
    userRights: UserRight[]
}
