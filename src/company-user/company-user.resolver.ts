import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { CompanyUserService } from './company-user.service'
import { CompanyUser } from './entities/company-user.entity'
import { CreateCompanyUserInput } from './dto/create-company-user.input'
import { UpdateCompanyUserInput } from './dto/update-company-user.input'
import { UseGuards } from '@nestjs/common'
import { CompanyGuard } from '../guards/company-guard'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { FirebaseCreateUserDto } from '../auth/authTypes/input-dto'
import { CreateUserRightInput } from '../user-right/dto/create-user-right.input'
import { SuperGuard } from '../guards/super-guard'

@Resolver(() => CompanyUser)
export class CompanyUserResolver {
    constructor(private readonly companyUserService: CompanyUserService) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => CompanyUser)
    createCompanyUser(
        @Context() context: any,
        @Args('createCompanyUserInput')
        createCompanyUserInput: CreateCompanyUserInput,
        @Args('userId', { type: () => String }) userId: string,
        @Args('companyId', { type: () => String, nullable: true })
        companyId: string
    ) {
        const bCompanyId = context?.req?.user?.companyId || companyId
        return this.companyUserService.create(
            createCompanyUserInput,
            userId,
            bCompanyId
        )
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => CompanyUser)
    async createFirebaseBridgeUser(
        @Context() context: any,
        @Args('firebaseUser') firebaseUserInput: FirebaseCreateUserDto,
        @Args('userRights', {
            type: () => CreateUserRightInput,
            nullable: true,
        })
        userRights: CreateUserRightInput,
        @Args('companyId', { type: () => String, nullable: true })
        companyId: string
    ) {
        const bCompanyId = context?.req?.user?.companyId || companyId
        return this.companyUserService.createBridgeUser(
            firebaseUserInput,
            bCompanyId,
            userRights
        )
    }

    @UseGuards(SuperGuard)
    @Query(() => [CompanyUser], { name: 'allCompanyUsers' })
    findAll() {
        return this.companyUserService.findAll()
    }

    @UseGuards(CompanyGuard)
    @Query(() => [CompanyUser], { name: 'findUsersByCompany' })
    findUsersByCompany(
        @Context() context: any,
        @Args('companyId', { type: () => String, nullable: true })
        companyId: string
    ) {
        const bCompanyId = companyId || context?.req?.user?.companyId
        return this.companyUserService.findByCompanyId(bCompanyId)
    }

    @UseGuards(CompanyGuard)
    @Query(() => CompanyUser, { name: 'getCompanyUser' })
    findOne(@Args('companyUserId', { type: () => String }) id: string) {
        return this.companyUserService.findOne(id)
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => CompanyUser, { name: 'verifyCompanyUser' })
    verifyCompanyUser(@Args('email', { type: () => String }) email: string) {
        return this.companyUserService.verifyCompanyUser(email)
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => CompanyUser)
    async updateCompanyUser(
        @Context() context: any,
        @Args('userRights', {
            type: () => CreateUserRightInput,
            nullable: true,
        })
        userRights: CreateUserRightInput,
        @Args('updateCompanyUserInput')
        updateCompanyUserInput: UpdateCompanyUserInput
    ) {
        const companyUserId = context?.req?.user?.companyUserId
        return await this.companyUserService.update(
            companyUserId,
            updateCompanyUserInput,
            userRights
        )
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => CompanyUser)
    removeCompanyUser(@Args('id', { type: () => String }) id: string) {
        return this.companyUserService.remove(id)
    }
}
