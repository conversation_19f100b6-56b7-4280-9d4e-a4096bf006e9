import { Module } from '@nestjs/common'
import { CompanyUserService } from './company-user.service'
import { CompanyUserResolver } from './company-user.resolver'
import { AuthModule } from '../auth/auth.module'
import { EmailServerModule } from '../email-server/email-server.module'

@Module({
    imports: [AuthModule, EmailServerModule],
    exports: [CompanyUserService],
    providers: [CompanyUserResolver, CompanyUserService],
})
export class CompanyUserModule {}
