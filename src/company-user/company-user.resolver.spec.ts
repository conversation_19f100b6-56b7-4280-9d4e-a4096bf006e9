import { Test, TestingModule } from '@nestjs/testing'
import { CompanyUserResolver } from './company-user.resolver'
import { CompanyUserService } from './company-user.service'

describe('CompanyUserResolver', () => {
    let resolver: CompanyUserResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [CompanyUserResolver, CompanyUserService],
        }).compile()

        resolver = module.get<CompanyUserResolver>(CompanyUserResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
