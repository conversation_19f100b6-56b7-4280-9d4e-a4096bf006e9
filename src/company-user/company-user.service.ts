import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateCompanyUserInput } from './dto/create-company-user.input'
import { UpdateCompanyUserInput } from './dto/update-company-user.input'
import { PrismaService } from '../prisma.service'
import { CompanyUser } from '@prisma/client'
import { FirebaseCreateUserDto } from '../auth/authTypes/input-dto'
import { AuthService } from '../auth/auth.service'
import { CreateUserRightInput } from '../user-right/dto/create-user-right.input'
import { admin } from '../auth/firebase-admin.module'
import { EmailServerService } from '../email-server/email-server.service'

@Injectable()
export class CompanyUserService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly authService: AuthService,
        private readonly emailService: EmailServerService
    ) {}

    async create(
        createCompanyUserInput: CreateCompanyUserInput,
        userId: string,
        companyId?: string
    ) {
        const companyUserExists = await this.prisma.companyUser.findUnique({
            where: {
                userId: userId,
            },
        })

        if (companyUserExists) {
            throw new NotFoundException('Company User already exists')
        }

        const data: any = {
            ...createCompanyUserInput,
            user: {
                connect: {
                    id: userId,
                },
            },
        }

        if (companyId) {
            data.companies = {
                connect: {
                    id: companyId,
                },
            }
        }

        return this.prisma.companyUser.create({
            data,
            include: {
                jobAdvert: true,
                user: true,
                companies: true,
                userRights: true,
            },
        })
    }

    async createBridgeUser(
        createBridgeUserInput: FirebaseCreateUserDto,
        companyId?: string,
        userRights?: CreateUserRightInput
    ) {
        const companyUserInfo = {
            email: createBridgeUserInput.email,
            name: createBridgeUserInput.displayName,
            activeCompanyId: companyId,
            avatarImageUrl: createBridgeUserInput.avatarImageUrl,
        }

        const firebaseUser = await this.authService.createNewUser(
            createBridgeUserInput
        )

        const newUser = await this.authService.registerViaFirebase({
            firebaseUid: firebaseUser.uid,
        })

        await this.authService.handleNewCompanyUserEmail(
            createBridgeUserInput.email,
            createBridgeUserInput.displayName
        )

        const data: any = {
            ...companyUserInfo,
            user: {
                connect: {
                    id: newUser?.user?.id,
                },
            },
        }

        if (companyId) {
            data.companies = {
                connect: {
                    id: companyId,
                },
            }
        }

        if (userRights) {
            data.userRights = {
                create: {
                    ...userRights,
                    company: {
                        connect: {
                            id: companyId,
                        },
                    },
                },
            }
        }

        const companyUser = await this.prisma.companyUser.create({
            data,
            include: {
                jobAdvert: true,
                user: true,
                companies: true,
                userRights: true,
            },
        })

        const userMetaData = {
            companyUserId: companyUser?.id,
            companyId: companyId,
            bridgeUserId: companyUser.user.id,
        }

        //setClaims
        await admin.auth().setCustomUserClaims(firebaseUser?.uid, {
            ...userMetaData,
        })

        return companyUser
    }

    findAll() {
        return this.prisma.companyUser.findMany({
            include: {
                jobAdvert: true,
                user: true,
                companies: true,
                userRights: true,
            },
        })
    }

    findOne(id: string) {
        return this.prisma.companyUser.findUnique({
            where: {
                id: id,
            },
            include: {
                jobAdvert: true,
                user: true,
                companies: true,
                userRights: true,
            },
        })
    }
    async verifyCompanyUser(email: string) {
        const companyUser = await this.prisma.companyUser.findUnique({
            where: {
                email: email,
            },
        })

        if (!companyUser) {
            throw new NotFoundException(
                `Company User with email ${email} does not exist`
            )
        }

        return companyUser
    }

    findByCompanyId(companyId: string) {
        return this.prisma.companyUser.findMany({
            where: {
                companies: {
                    some: {
                        id: companyId,
                    },
                },
            },
            include: {
                jobAdvert: true,
                user: true,
                companies: true,
                userRights: true,
            },
        })
    }

    async update(
        updaterCompanyUserId: string,
        updateCompanyUserInput: UpdateCompanyUserInput,
        userRights?: CreateUserRightInput
    ): Promise<CompanyUser> {
        let companyUser = null
        const oldCompanyUser = await this.prisma.companyUser.findUnique({
            where: {
                id: updateCompanyUserInput.id,
            },
            include: {
                userRights: true,
                user: true,
            },
        })

        if (oldCompanyUser) {
            if (userRights) {
                await this.handleUserRightsUpdate(
                    updaterCompanyUserId,
                    oldCompanyUser,
                    userRights
                )
            }
            // Update CompanyUser
            try {
                companyUser = await this.prisma.companyUser.update({
                    where: {
                        id: updateCompanyUserInput.id,
                    },
                    data: {
                        ...updateCompanyUserInput,
                    },
                    include: {
                        jobAdvert: true,
                        user: true,
                        companies: true,
                        userRights: true,
                    },
                })
            } catch (e) {
                throw new Error(e)
            }

            // Handle email updates
            if (updateCompanyUserInput?.email !== oldCompanyUser?.email) {
                await admin.auth().updateUser(oldCompanyUser.user.firebaseUid, {
                    email: updateCompanyUserInput?.email,
                    emailVerified: false,
                })

                const verificationEmail =
                    await this.authService.createEmailVerifyLink(
                        companyUser.email
                    )

                await this.emailService.sendWelcomeEmail({
                    email: companyUser.email,
                    name: companyUser.name,
                    verificationLink: verificationEmail,
                })
            }
        }

        return companyUser
    }

    async handleUserRightsUpdate(
        updaterCompanyUserId: string,
        oldCompanyUser: any,
        userRights: CreateUserRightInput
    ) {
        //Get Company User with updater Id
        const updaterCompanyUser = await this.prisma.companyUser.findUnique({
            where: {
                id: updaterCompanyUserId,
            },
            include: {
                userRights: true,
            },
        })

        if (!updaterCompanyUser?.userRights[0]?.superAdmin) return

        //Handle User Rights
        if (userRights) {
            if (oldCompanyUser.userRights.length) {
                await this.prisma.userRights.update({
                    where: { id: oldCompanyUser.userRights[0].id },
                    data: userRights,
                })
            } else {
                await this.prisma.userRights.create({
                    data: {
                        ...userRights,
                        company: {
                            connect: {
                                id: oldCompanyUser.activeCompanyId,
                            },
                        },
                        companyUser: {
                            connect: {
                                id: oldCompanyUser.id,
                            },
                        },
                    },
                })
            }
        }
    }

    remove(id: string) {
        const companyUserToDelete = this.prisma.companyUser.findUnique({
            where: { id: id },
        })
        if (!companyUserToDelete) {
            throw new NotFoundException(
                `Company User with ID ${id} does not exist`
            )
        }
        return this.prisma.companyUser.delete({
            where: { id: id },
        })
    }
}
