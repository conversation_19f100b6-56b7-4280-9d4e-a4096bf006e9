import { Type } from '@nestjs/common'
import { Field, Int, ObjectType } from '@nestjs/graphql'

export interface IPaginatedType<T> {
    items: T[]
    meta: PaginationMeta
}

@ObjectType()
export class PaginationMeta {
    @Field(() => Int)
    totalItems: number

    @Field(() => Int)
    itemCount: number

    @Field(() => Int)
    itemsPerPage: number

    @Field(() => Int)
    totalPages: number

    @Field(() => Int)
    currentPage: number
}

export function Paginated<T>(classRef: Type<T>): Type<IPaginatedType<T>> {
    @ObjectType({ isAbstract: true })
    abstract class PaginatedType implements IPaginatedType<T> {
        @Field(() => [classRef])
        items: T[]

        @Field(() => PaginationMeta)
        meta: PaginationMeta
    }

    return PaginatedType as Type<IPaginatedType<T>>
} 