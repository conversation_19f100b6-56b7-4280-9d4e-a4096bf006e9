import { InputType, Field, Int } from '@nestjs/graphql'
import { IsInt, IsOptional, Min, IsString } from 'class-validator'
import { Type } from 'class-transformer'

@InputType()
export class PaginationInput {
    @Field(() => Int, { nullable: true, defaultValue: 1 })
    @IsInt()
    @Min(1)
    @IsOptional()
    @Type(() => Number)
    page?: number = 1

    @Field(() => Int, { nullable: true, defaultValue: 10 })
    @IsInt()
    @Min(1)
    @IsOptional()
    @Type(() => Number)
    limit?: number = 10

    @Field(() => String, { nullable: true })
    @IsString()
    @IsOptional()
    search?: string

    @Field(() => String, { nullable: true })
    @IsString()
    @IsOptional()
    status?: string
} 