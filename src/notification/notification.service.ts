import { Injectable } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import * as admin from 'firebase-admin'
import { SendNotificationParams } from './type/notificationTypes'
import { EmailServerService } from '../email-server/email-server.service'
import { PusherPubSubService } from '../pub-sub/pusher-pub-sub.service'
import { messaging } from 'firebase-admin'
import MulticastMessage = messaging.MulticastMessage
import { AppointmentService } from '../appointment/appointment.service'

@Injectable()
export class NotificationService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly emailService: EmailServerService,
        private readonly pusherService: PusherPubSubService
    ) {}

    async createNotification(params: SendNotificationParams) {
        const {
            applicantId,
            companyUserId,
            createNotificationInput,
            createPushNotificationInput,
            createEmailNotificationInput,
        } = params

        const data: any = {
            ...createNotificationInput,
        }

        if (applicantId) {
            data.applicant = {
                connect: {
                    id: applicantId,
                },
            }
        }

        if (companyUserId) {
            data.companyUser = {
                connect: {
                    id: companyUserId,
                },
            }
        }

        // if (companyUserId === '' && applicantId === '') {
        //     throw new Error('Either applicantId or companyUserId is required')
        // }

        const notification = await this.prisma.notification.create({
            data,
            include: {
                applicant: true,
                companyUser: true,
                emailNotification: true,
                pushNotification: true,
            },
        })

        if (
            createPushNotificationInput &&
            Object.keys(createPushNotificationInput)?.length > 0
        ) {
            await this.prisma.pushNotification.create({
                data: {
                    ...createPushNotificationInput,
                    notification: {
                        connect: {
                            id: notification.id,
                        },
                    },
                },
            })
        }

        if (
            createEmailNotificationInput &&
            Object.keys(createEmailNotificationInput).length > 0
        ) {
            await this.prisma.emailNotification.create({
                data: {
                    ...createEmailNotificationInput,
                    notification: {
                        connect: {
                            id: notification.id,
                        },
                    },
                },
            })
        }

        const newNotification = await this.prisma.notification.findUnique({
            where: {
                id: notification.id,
            },
            include: {
                applicant: true,
                companyUser: true,
                emailNotification: true,
                pushNotification: true,
            },
        })

        if (companyUserId) {
            const channel = `liveNotifications.${companyUserId}`

            await this.pusherService.publish(
                `private-${channel}`,
                'notification',
                newNotification
            )
        } else if (createEmailNotificationInput?.emailSubject) {
            const channel = `liveNotifications.super`

            await this.pusherService.publish(
                `private-${channel}`,
                'notification',
                newNotification
            )
        }

        return newNotification
    }

    public async sendPushNotification(params: SendNotificationParams) {
        if (!params.applicantId) {
            return
        }

        const notificationCreated = await this.createNotification(params)

        if (!notificationCreated) {
            throw new Error('Error creating notification.')
        }

        const applicantDeviceToken = await this.prisma.deviceToken.findMany({
            where: {
                applicantId: params.applicantId,
            },
        })

        if (applicantDeviceToken.length === 0) {
            return notificationCreated
        }

        const devTokens = applicantDeviceToken.map((token) => token.token)

        const message: MulticastMessage = {
            notification: {
                title: params.createPushNotificationInput.title,
                body: params.createPushNotificationInput.body,
            },
            data: {
                action:
                    params.type == 'newAppointmentChat'
                        ? 'APPOINTMENTS'
                        : 'MATCHES',
                chatId: params?.chatId || '',
                appointmentId: params?.appointmentId || '',
            },
            android: {
                priority: 'high' as const,
            },
            tokens: devTokens,
        }

        //Send notification to the device
        const msgResponse = await admin
            .messaging()
            .sendEachForMulticast(message)

        //map response of admin.messaging and remove failed tokens ref:
        if (msgResponse.failureCount > 0) {
            const failedTokens = []
            msgResponse.responses.forEach((resp, idx) => {
                if (!resp.success) {
                    failedTokens.push(devTokens[idx])
                }
            })

            // remove failed tokens from the database
            await this.prisma.deviceToken.deleteMany({
                where: {
                    token: {
                        in: failedTokens,
                    },
                },
            })
        }
    }

    public async sendEmailNotification(params: SendNotificationParams) {
        const notificationCreated = await this.createNotification(params)

        const { jobAdId } = params

        if (!jobAdId) {
            throw new Error('jobAdvertId is required')
        }

        const responsibleUsersEmail =
            jobAdId &&
            (await this.prisma.jobAdvert.findUnique({
                where: {
                    id: jobAdId,
                },
                select: {
                    responsibleUsers: {
                        select: {
                            email: true,
                        },
                    },
                },
            }))

        if (!notificationCreated) {
            throw new Error('Error creating notification.')
        }
        console.log('see params::', params)

        if (params.createNotificationInput.type === 'newLike') {
            console.log('responsibleUsersEmail', responsibleUsersEmail)
            const emailPromises = responsibleUsersEmail.responsibleUsers.map(
                (user) =>
                    this.emailService.sendNewLikeEmail({
                        jobAdTitle: params.jobAdTitle,
                        jobAdId: params.jobAdId,
                        applicantName: params?.applicantName,
                        email: user.email,
                    })
            )
            await Promise.all(emailPromises)
        }
        if (params.createNotificationInput.type === 'newCompanyChat') {
            const emailPromises = responsibleUsersEmail.responsibleUsers.map(
                (user) =>
                    this.emailService.sendNewChatEmail({
                        jobAdTitle: params.jobAdTitle,
                        applicantName: params?.applicantName,
                        email: user.email,
                        message: 'Neue Nachricht erhalten!',
                        timeslot: {},
                    })
            )
            await Promise.all(emailPromises)
        }

        return notificationCreated
    }

    public async sendContactPersonEmailNotification(
        params: SendNotificationParams
    ) {
        //omit applicantId from params
        const cleanParams = { ...params, applicantId: undefined }

        const notificationCreated = await this.createNotification(cleanParams)

        const { appointmentId } = params

        if (!appointmentId) {
            throw new Error('appointmentId is required')
        }

        //get appointment
        const appointment = await this.prisma.appointment.findUnique({
            where: { id: appointmentId },
        })

        const companyContactPerson = await this.prisma.appointment.findUnique({
            where: {
                id: appointmentId,
            },
            select: {
                contactPersonTimeslot: {
                    select: {
                        companyFairContactPerson: {
                            select: {
                                contactPerson: {
                                    select: {
                                        email: true,
                                        companyId: true,
                                    },
                                },
                                companyFairParticipation: {
                                    select: {
                                        fair: {
                                            select: {
                                                contactPersonEmail: true,
                                            },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
            },
        })

        if (!notificationCreated) {
            throw new Error('Error creating notification.')
        }

        await this.emailService.sendNewChatEmail({
            jobAdTitle: params.jobAdTitle,
            applicantName: params?.applicantName,
            email: companyContactPerson.contactPersonTimeslot
                .companyFairContactPerson.contactPerson.email,
            message: 'Neue Nachricht erhalten!',
            timeslot: appointment,
        })

        return notificationCreated
    }

    findAll() {
        return this.prisma.notification.findMany({
            include: {
                applicant: true,
                companyUser: true,
            },
        })
    }

    findOne(id: string) {
        return this.prisma.notification.findUnique({
            where: { id },
            include: {
                applicant: true,
                companyUser: true,
            },
        })
    }

    async findByCompanyUser(companyUserId: string) {
        return this.prisma.notification.findMany({
            where: {
                companyUserId,
            },
            include: {
                companyUser: true,
                emailNotification: true,
            },
        })
    }

    async markAllAsRead(companyUserId?: string, applicantId?: string) {
        let result = null
        if (companyUserId) {
            result = await this.prisma.notification.updateMany({
                where: {
                    companyUserId,
                    isNew: true,
                },
                data: {
                    isNew: false,
                },
            })
        }

        if (applicantId) {
            result = await this.prisma.notification.updateMany({
                where: {
                    applicantId,
                    isNew: true,
                },
                data: {
                    isNew: false,
                },
            })
        }

        if (result?.count > 0) {
            return 'Notifications marked as read successfully.'
        } else if (result?.count === 0) {
            return 'No new notifications found to mark as read.'
        }
    }

    async markAsRead(id: string) {
        const result = await this.prisma.notification.update({
            where: {
                id,
            },
            data: {
                isNew: false,
            },
        })

        if (result) {
            return result
        } else {
            throw new Error('Notification not found.')
        }
    }

    async deleteNotification(id: string) {
        let deletedNotification = null
        const notification = await this.prisma.notification.findUnique({
            where: {
                id,
            },
        })

        if (!notification) {
            throw new Error('Notification not found.')
        } else {
            deletedNotification = await this.prisma.notification.delete({
                where: {
                    id,
                },
            })
        }

        if (deletedNotification) {
            return 'Notification deleted successfully'
        } else {
            throw new Error('Notification not found.')
        }
    }

    findByApplicant(applicantId: string) {
        return this.prisma.notification.findMany({
            where: {
                applicantId,
            },
            include: {
                applicant: true,
                pushNotification: true,
            },
        })
    }
}
