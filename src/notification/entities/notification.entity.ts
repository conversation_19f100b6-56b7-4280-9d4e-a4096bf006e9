import { ObjectType, Field } from '@nestjs/graphql'
import { CompanyUser } from '../../company-user/entities/company-user.entity'
import { Applicant } from '../../applicant/entities/applicant.entity'

@ObjectType()
export class PushNotification {
    @Field()
    id: string

    @Field({ nullable: true })
    title: string

    @Field({ nullable: true })
    body: string

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date
}

@ObjectType()
export class EmailNotification {
    @Field()
    id: string

    @Field({ nullable: true })
    emailSubject: string

    @Field({ nullable: true })
    emailBody: string

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date
}

@ObjectType()
export class Notification {
    @Field()
    id: string

    @Field({ nullable: true })
    isNew: boolean

    @Field(() => PushNotification, { nullable: true })
    pushNotification: PushNotification

    @Field(() => EmailNotification, { nullable: true })
    emailNotification: EmailNotification

    @Field(() => CompanyUser, { nullable: true })
    companyUser: CompanyUser

    @Field(() => Applicant, { nullable: true })
    applicant: Applicant

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date
}
