import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional, IsBoolean } from 'class-validator'

@InputType()
export class CreatePushNotificationInput {
    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    title: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    body: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    actionUrl: string
}

@InputType()
export class CreateEmailNotificationInput {
    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    emailSubject: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    emailBody: string
}

@InputType()
export class CreateNotificationInput {
    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isNew: boolean

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    type: string
}
