import { Args, Context, Mutation, Query, Resolver } from '@nestjs/graphql'
import { NotificationService } from './notification.service'
import { Notification } from './entities/notification.entity'
import {
    CreateEmailNotificationInput,
    CreateNotificationInput,
    CreatePushNotificationInput,
} from './dto/create-notification.input'
import { UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { SendNotificationParams } from './type/notificationTypes'

@Resolver(() => Notification)
export class NotificationResolver {
    constructor(private readonly notificationService: NotificationService) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Notification)
    async createNotification(
        @Args('createNotificationInput')
        createNotificationInput: CreateNotificationInput,
        @Args('createEmailNotificationInput', { nullable: true })
        createEmailNotificationInput: CreateEmailNotificationInput,
        @Args('createPushNotificationInput', { nullable: true })
        createPushNotificationInput: CreatePushNotificationInput,
        @Args('companyUserId', { type: () => String, nullable: true })
        companyUserId: string,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string,
        @Context() context: any
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId

        const params: SendNotificationParams = {
            applicantId: bApplicantId,
            companyUserId,
            createNotificationInput,
            createPushNotificationInput,
            createEmailNotificationInput,
        }

        return await this.notificationService.createNotification(params)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Notification)
    async sendPushNotification(
        @Context() context: any,
        @Args('createNotificationInput')
        createNotificationInput: CreateNotificationInput,
        @Args('createEmailNotificationInput', { nullable: true })
        createEmailNotificationInput: CreateEmailNotificationInput,
        @Args('createPushNotificationInput', { nullable: true })
        createPushNotificationInput: CreatePushNotificationInput,
        @Args('companyUserId', { type: () => String, nullable: true })
        companyUserId: string,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        const params: SendNotificationParams = {
            applicantId: bApplicantId,
            companyUserId,
            createNotificationInput,
            createPushNotificationInput,
            createEmailNotificationInput,
        }

        return await this.notificationService.sendPushNotification(params)
    }

    @Mutation(() => Notification)
    async sendEmailNotification(
        @Args('createNotificationInput')
        createNotificationInput: CreateNotificationInput,
        @Args('createEmailNotificationInput', { nullable: true })
        createEmailNotificationInput: CreateEmailNotificationInput,
        @Args('companyUserId', { type: () => String, nullable: true })
        companyUserId: string
    ) {
        const params: SendNotificationParams = {
            companyUserId,
            createNotificationInput,
            createEmailNotificationInput,
        }

        return await this.notificationService.sendEmailNotification(params)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [Notification], { name: 'allNotifications' })
    findAll() {
        return this.notificationService.findAll()
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => Notification, { name: 'findNotification' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.notificationService.findOne(id)
    }

    @Query(() => [Notification], { name: 'companyUserNotifications' })
    companyUserNotifications(
        @Args('companyUserId', { type: () => String }) companyUserId: string
    ) {
        return this.notificationService.findByCompanyUser(companyUserId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => String, { name: 'markAllAsRead' })
    markAllAsRead(
        @Args('companyUserId', { type: () => String, nullable: true })
        companyUserId: string,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string,
        @Context() context: any
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        const bCompanyUserId =
            context?.req?.user?.companyUserId || companyUserId

        return this.notificationService.markAllAsRead(
            bCompanyUserId,
            bApplicantId
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Notification, { name: 'markAsRead' })
    markAsRead(
        @Args('notificationId', { type: () => String }) notificationId: string
    ) {
        return this.notificationService.markAsRead(notificationId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => [Notification], { name: 'applicantNotifications' })
    applicantNotifications(
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string,
        @Context() context: any
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        return this.notificationService.findByApplicant(bApplicantId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => String, { name: 'deleteNotification' })
    deleteNotification(
        @Args('notificationId', { type: () => String }) notificationId: string
    ) {
        return this.notificationService.deleteNotification(notificationId)
    }
}
