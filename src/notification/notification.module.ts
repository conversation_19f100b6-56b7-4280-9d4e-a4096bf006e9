import { Module } from '@nestjs/common'
import { NotificationService } from './notification.service'
import { NotificationResolver } from './notification.resolver'
import { PubSub } from 'graphql-subscriptions'
import { EmailServerModule } from '../email-server/email-server.module'
import { AuthModule } from '../auth/auth.module'
import { PusherPubSubModule } from '../pub-sub/pusher-pub-sub.module'

@Module({
    imports: [AuthModule, EmailServerModule, PusherPubSubModule],
    exports: [NotificationService],
    providers: [PubSub, NotificationResolver, NotificationService],
})
export class NotificationModule {}
