import {
    CreateEmailNotificationInput,
    CreateNotificationInput,
    CreatePushNotificationInput,
} from '../dto/create-notification.input'

export interface SendNotificationParams {
    type?: string
    jobAdId?: string
    jobAdTitle?: string
    applicantId?: string
    appointmentId?: string
    chatId?: string
    applicantName?: string
    companyUserId?: string
    companyFairContactPersonId?: string
    createNotificationInput: CreateNotificationInput
    createPushNotificationInput?: CreatePushNotificationInput
    createEmailNotificationInput?: CreateEmailNotificationInput
}
