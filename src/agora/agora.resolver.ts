import { Resolver, Query, Args } from '@nestjs/graphql'
import { AgoraService } from './agora.service'
import { Agora } from './entities/agora.entity'
import { CreateAgoraInput } from './dto/create-agora.input'
import { UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@UseGuards(GraphqlAuthGuard)
@Resolver(() => Agora)
export class AgoraResolver {
    constructor(private readonly agoraService: AgoraService) {}

    @Query(() => Agora)
    generateAgoraToken(
        @Args('createAgoraInput') createAgoraInput: CreateAgoraInput
    ) {
        return this.agoraService.generateAgoraToken(createAgoraInput)
    }
}
