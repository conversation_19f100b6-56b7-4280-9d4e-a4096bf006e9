import { Injectable } from '@nestjs/common'
import { CreateAgoraInput } from './dto/create-agora.input'
import { PrismaService } from '../prisma.service'
import { RtcTokenBuilder, RtcRole } from 'agora-access-token'
import { Agora } from './entities/agora.entity'
import { ConfigService } from '@nestjs/config'
import { generateRandomString } from '../utils'

@Injectable()
export class AgoraService {
    constructor(
        private readonly prisma: PrismaService,
        private configService: ConfigService
    ) {}

    async generateAgoraToken(
        createAgoraInput: CreateAgoraInput
    ): Promise<Agora> {
        const AGORA_ID = this.configService.get('AGORA_ID')
        const AGORA_CERTIFICATE = this.configService.get('AGORA_CERTIFICATE')

        //check if jobActionId is valid
        if (!createAgoraInput.jobActionId) {
            throw new Error('JobAction Id is required')
        }

        const jobAction = await this.prisma.jobAction.findUnique({
            where: {
                id: createAgoraInput.jobActionId,
            },
        })

        if (!jobAction) {
            throw new Error('JobAction ID is invalid')
        }

        const uid = generateRandomString()
        const timestamp = Math.floor(Date.now() / 1000) + 6000
        const token = RtcTokenBuilder.buildTokenWithAccount(
            AGORA_ID,
            AGORA_CERTIFICATE,
            createAgoraInput.jobActionId,
            uid,
            RtcRole.PUBLISHER,
            timestamp
        )
        return { token, uid }
    }
}
