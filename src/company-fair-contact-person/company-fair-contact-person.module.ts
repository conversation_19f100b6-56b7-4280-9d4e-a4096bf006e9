import { Module } from '@nestjs/common'
import { CompanyFairContactPersonService } from './company-fair-contact-person.service'
import { CompanyFairContactPersonResolver } from './company-fair-contact-person.resolver'
import { ContactPersonTimeslotModule } from '../contact-person-timeslot/contact-person-timeslot.module'

@Module({
    imports: [ContactPersonTimeslotModule],
    providers: [
        CompanyFairContactPersonResolver,
        CompanyFairContactPersonService,
    ],
    exports: [CompanyFairContactPersonService],
})
export class CompanyFairContactPersonModule {}
