import { ObjectType, Field } from '@nestjs/graphql'
import { CompanyFairParticipation } from '../../company-fair-participation/entities/company-fair-participation.entity'
import { ContactPerson } from '../../contact-person/entities/contact-person.entity'
import { ContactPersonTimeslot } from '../../contact-person-timeslot/entities/contact-person-timeslot.entity'

@ObjectType()
export class CompanyFairContactPerson {
  @Field()
  id: string

  @Field(() => CompanyFairParticipation)
  companyFairParticipation: CompanyFairParticipation

  @Field()
  companyFairParticipationId: string

  @Field(() => ContactPerson)
  contactPerson: ContactPerson

  @Field()
  contactPersonId: string

  @Field(() => [ContactPersonTimeslot])
  ContactPersonTimeslot: ContactPersonTimeslot[]
}