import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateCompanyFairContactPersonInput } from './dto/create-company-fair-contact-person.input'
import { UpdateCompanyFairContactPersonInput } from './dto/update-company-fair-contact-person.input'
import { ContactPersonTimeslotService } from '../contact-person-timeslot/contact-person-timeslot.service'

@Injectable()
export class CompanyFairContactPersonService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly contactPersonTimeslotService: ContactPersonTimeslotService
    ) {}

    async create(
        createCompanyFairContactPersonInput: CreateCompanyFairContactPersonInput
    ) {
        const { companyFairParticipationId, contactPersonId } =
            createCompanyFairContactPersonInput

        const participation =
            await this.prisma.companyFairParticipation.findUnique({
                where: { id: companyFairParticipationId },
            })

        if (!participation) {
            throw new NotFoundException('Company Fair Participation not found')
        }

        const contactPerson = await this.prisma.contactPerson.findUnique({
            where: { id: contactPersonId },
        })

        if (!contactPerson) {
            throw new NotFoundException('Contact Person not found')
        }

        const existing = await this.prisma.companyFairContactPerson.findFirst({
            where: {
                companyFairParticipationId,
                contactPersonId,
            },
        })

        if (existing) {
            throw new Error(
                'Contact person already assigned to this fair participation'
            )
        }

        const companyFairContactPerson =
            await this.prisma.companyFairContactPerson.create({
                data: {
                    companyFairParticipation: {
                        connect: { id: companyFairParticipationId },
                    },
                    contactPerson: {
                        connect: { id: contactPersonId },
                    },
                },
                include: {
                    companyFairParticipation: {
                        include: {
                            company: true,
                            fair: true,
                        },
                    },
                    contactPerson: true,
                    ContactPersonTimeslot: true,
                },
            })

        const fairId = participation?.fairId

        const timeslots = await this.prisma.timeslot.findMany({
            where: {
                fairId,
            },
        })

        await Promise.all(
            timeslots.map(async (timeslot) => {
                await this.contactPersonTimeslotService.create({
                    companyFairContactPersonId: companyFairContactPerson.id,
                    startTime: timeslot.startTime,
                    endTime: timeslot.endTime,
                })
            })
        )

        return companyFairContactPerson
    }

    async findAll() {
        return this.prisma.companyFairContactPerson.findMany({
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                contactPerson: true,
                ContactPersonTimeslot: true,
            },
        })
    }

    async findOne(id: string) {
        const contactPerson =
            await this.prisma.companyFairContactPerson.findUnique({
                where: { id },
                include: {
                    companyFairParticipation: {
                        include: {
                            company: true,
                            fair: true,
                        },
                    },
                    contactPerson: true,
                    ContactPersonTimeslot: true,
                },
            })

        if (!contactPerson) {
            throw new NotFoundException('Company fair contact person not found')
        }

        return contactPerson
    }

    async update(
        id: string,
        updateCompanyFairContactPersonInput: UpdateCompanyFairContactPersonInput
    ) {
        const { contactPersonId } = updateCompanyFairContactPersonInput

        await this.findOne(id)

        if (contactPersonId) {
            const contactPerson = await this.prisma.contactPerson.findUnique({
                where: { id: contactPersonId },
            })

            if (!contactPerson) {
                throw new NotFoundException('Contact Person not found')
            }
        }

        return this.prisma.companyFairContactPerson.update({
            where: { id },
            data: {
                contactPerson: contactPersonId
                    ? {
                          connect: { id: contactPersonId },
                      }
                    : undefined,
            },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                contactPerson: true,
                ContactPersonTimeslot: true,
            },
        })
    }

    async remove(id: string) {
        const person = await this.findOne(id)

        if (!person) {
            throw new NotFoundException('Company fair contact person not found')
        }

        await this.prisma.contactPersonTimeslot.deleteMany({
            where: {
                companyFairContactPersonId: id,
            },
        })

        return this.prisma.companyFairContactPerson.delete({
            where: { id },
        })
    }

    async findByParticipation(participationId: string) {
        return this.prisma.companyFairContactPerson.findMany({
            where: { companyFairParticipationId: participationId },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                contactPerson: true,
                ContactPersonTimeslot: true,
            },
        })
    }

    async findByContactPerson(contactPersonId: string) {
        return this.prisma.companyFairContactPerson.findMany({
            where: { contactPersonId },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                contactPerson: true,
                ContactPersonTimeslot: true,
            },
        })
    }

    async findByFair(fairId: string) {
        return this.prisma.companyFairContactPerson.findMany({
            where: {
                companyFairParticipation: {
                    fairId,
                },
            },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                contactPerson: true,
                ContactPersonTimeslot: true,
            },
        })
    }

    async findByCompany(companyId: string) {
        return this.prisma.companyFairContactPerson.findMany({
            where: {
                companyFairParticipation: {
                    companyId,
                },
            },
            include: {
                companyFairParticipation: {
                    include: {
                        company: true,
                        fair: true,
                    },
                },
                contactPerson: true,
                ContactPersonTimeslot: true,
            },
        })
    }
}
