import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql'
import { CompanyFairContactPersonService } from './company-fair-contact-person.service'
import { CompanyFairContactPerson } from './entities/company-fair-contact-person.entity'
import { CreateCompanyFairContactPersonInput } from './dto/create-company-fair-contact-person.input'
import { UpdateCompanyFairContactPersonInput } from './dto/update-company-fair-contact-person.input'

@Resolver(() => CompanyFairContactPerson)
export class CompanyFairContactPersonResolver {
    constructor(
        private readonly companyFairContactPersonService: CompanyFairContactPersonService
    ) {}

    @Mutation(() => CompanyFairContactPerson)
    createCompanyFairContactPerson(
        @Args('createCompanyFairContactPersonInput')
        createCompanyFairContactPersonInput: CreateCompanyFairContactPersonInput
    ) {
        return this.companyFairContactPersonService.create(
            createCompanyFairContactPersonInput
        )
    }

    @Query(() => [CompanyFairContactPerson], {
        name: 'getAllCompanyFairContactPersons',
    })
    findAll() {
        return this.companyFairContactPersonService.findAll()
    }

    @Query(() => CompanyFairContactPerson, {
        name: 'getCompanyFairContactPersonById',
    })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.companyFairContactPersonService.findOne(id)
    }

    @Mutation(() => CompanyFairContactPerson)
    updateCompanyFairContactPerson(
        @Args('updateCompanyFairContactPersonInput')
        updateCompanyFairContactPersonInput: UpdateCompanyFairContactPersonInput
    ) {
        return this.companyFairContactPersonService.update(
            updateCompanyFairContactPersonInput.id,
            updateCompanyFairContactPersonInput
        )
    }

    @Mutation(() => CompanyFairContactPerson)
    removeCompanyFairContactPerson(
        @Args('id', { type: () => String }) id: string
    ) {
        return this.companyFairContactPersonService.remove(id)
    }

    @Query(() => [CompanyFairContactPerson], {
        name: 'findCompanyFairContactPersonsByContactPersonId',
    })
    findByContactPersonId(
        @Args('contactPersonId', { type: () => String }) contactPersonId: string
    ) {
        return this.companyFairContactPersonService.findByContactPerson(
            contactPersonId
        )
    }

    @Query(() => [CompanyFairContactPerson], {
        name: 'findFairContactPersonByParticipation',
    })
    findByParticipation(
        @Args('participationId', { type: () => String }) participationId: string
    ) {
        return this.companyFairContactPersonService.findByParticipation(
            participationId
        )
    }

    @Query(() => [CompanyFairContactPerson], {
        name: 'findFairContactPersonByFair',
    })
    findByFair(@Args('fairId', { type: () => String }) fairId: string) {
        return this.companyFairContactPersonService.findByFair(fairId)
    }

    @Query(() => [CompanyFairContactPerson], {
        name: 'findFairContactPersonByCompany',
    })
    findByCompany(
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        return this.companyFairContactPersonService.findByCompany(companyId)
    }
}
