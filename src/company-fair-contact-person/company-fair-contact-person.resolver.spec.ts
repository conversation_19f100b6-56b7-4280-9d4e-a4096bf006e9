import { Test, TestingModule } from '@nestjs/testing';
import { CompanyFairContactPersonResolver } from './company-fair-contact-person.resolver';
import { CompanyFairContactPersonService } from './company-fair-contact-person.service';

describe('CompanyFairContactPersonResolver', () => {
  let resolver: CompanyFairContactPersonResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CompanyFairContactPersonResolver, CompanyFairContactPersonService],
    }).compile();

    resolver = module.get<CompanyFairContactPersonResolver>(CompanyFairContactPersonResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
