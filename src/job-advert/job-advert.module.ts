import { Module } from '@nestjs/common'
import { JobAdvertService } from './job-advert.service'
import { JobAdvertResolver } from './job-advert.resolver'
import { AuthModule } from '../auth/auth.module'
import { EmailServerModule } from '../email-server/email-server.module'
import { PusherPubSubModule } from '../pub-sub/pusher-pub-sub.module'

@Module({
    imports: [AuthModule, EmailServerModule, PusherPubSubModule],
    exports: [JobAdvertService],
    providers: [JobAdvertResolver, JobAdvertService],
})
export class JobAdvertModule {}
