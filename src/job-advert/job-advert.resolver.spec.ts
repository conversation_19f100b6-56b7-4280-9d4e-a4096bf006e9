import { Test, TestingModule } from '@nestjs/testing'
import { JobAdvertResolver } from './job-advert.resolver'
import { JobAdvertService } from './job-advert.service'

describe('JobAdvertResolver', () => {
    let resolver: JobAdvertResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [JobAdvertResolver, JobAdvertService],
        }).compile()

        resolver = module.get<JobAdvertResolver>(JobAdvertResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
