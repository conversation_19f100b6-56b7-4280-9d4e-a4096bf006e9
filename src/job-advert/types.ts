import { ActionState, JobAdvertType } from '@prisma/client'

export type JobAdsFilterOptions = {
    type?: JobAdvertType[]
    district?: string
    companyId?: string
    longitude?: number
    radius?: number
    latitude?: number
    categories?: number[]
}

export type JobActionsFilterOptions = {
    applicantId?: string
    jobAdvertId?: string
    state?: ActionState
    status?: string
    deletedFromCompany?: boolean
    deletedFromApplicant?: boolean
    companyIsNew?: boolean
    applicantIsNew?: boolean
}
