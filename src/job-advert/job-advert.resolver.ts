import { Args, Context, Mutation, Query, Resolver } from '@nestjs/graphql'
import { JobAdvertService } from './job-advert.service'
import { CreateJobAdvertInput } from './dto/create-job-advert.input'
import { UpdateJobAdvertInput } from './dto/update-job-advert.input'
import { JobAdvertFilterOptionsInput } from './dto/job-advert-filter-options-input'
import { JobAdvert } from './entities/job-advert.entity'
import { JobAdvertStats } from './entities/job-advert-stats'
import { UseGuards } from '@nestjs/common'
import { CompanyGuard } from '../guards/company-guard'
import { SuperGuard } from '../guards/super-guard'
import { ApplicantGuard } from '../guards/applicant-guard'
import { PaginationInput } from '../common/dto/pagination.input'
import { PaginatedJobAdvertsResponse } from './dto/paginated-job-adverts.response'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver(() => JobAdvert)
export class JobAdvertResolver {
    constructor(private readonly jobAdvertService: JobAdvertService) {}

    @UseGuards(CompanyGuard)
    @Mutation(() => JobAdvert, { name: 'createJobAd' })
    async createJobAdvert(
        @Args('createJobAdvertInput')
        createJobAdvertInput: CreateJobAdvertInput,
        @Args({ name: 'categoryIds', type: () => [String] })
        categoryIds: string[],
        @Args('companyId', { type: () => String }) companyId: string,
        @Args({
            name: 'responsibleUsersIds',
            type: () => [String],
            nullable: true,
        })
        responsibleUsersIds?: string[]
    ) {
        const filteredResponsibleUsersIds = responsibleUsersIds?.filter(
            (id) => id !== null
        )

        return await this.jobAdvertService.create(
            createJobAdvertInput,
            categoryIds,
            companyId,
            filteredResponsibleUsersIds
        )
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => JobAdvert, { name: 'updateJobAdvert' })
    async updateJobAdvert(
        @Args('updateJobAdvertInput')
        updateJobAdvertInput: UpdateJobAdvertInput,
        @Args('id', { type: () => String }) id: string,
        @Args({
            name: 'categoryIdsToDisconnect',
            type: () => [String],
            nullable: true,
        })
        categoryIdsToDisconnect: string[],
        @Args({
            name: 'categoryIdsToConnect',
            type: () => [String],
            nullable: true,
        })
        categoryIdsToConnect: string[],
        @Args({
            name: 'responsibleUsersIdsToConnect',
            type: () => [String],
            nullable: true,
        })
        responsibleUsersIdsToConnect: string[],
        @Args({
            name: 'responsibleUsersIdsToDisconnect',
            type: () => [String],
            nullable: true,
        })
        responsibleUsersIdsToDisconnect: string[],
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        return await this.jobAdvertService.update(
            id,
            categoryIdsToDisconnect,
            categoryIdsToConnect,
            responsibleUsersIdsToConnect,
            responsibleUsersIdsToDisconnect,
            companyId,
            updateJobAdvertInput
        )
    }

    @UseGuards(SuperGuard)
    @Query(() => [JobAdvert], { name: 'allJobAds' })
    findAll() {
        return this.jobAdvertService.findAll()
    }

    @UseGuards(SuperGuard)
    @Query(() => PaginatedJobAdvertsResponse, { name: 'paginatedJobAds' })
    findPaginated(
        @Args('paginationInput', { nullable: true })
        paginationInput?: PaginationInput
    ) {
        return this.jobAdvertService.findPaginated(
            paginationInput || { page: 1, limit: 10, status: 'Pending' }
        )
    }

    //TODO: Remove this temporary query after mobile app is updated
    @UseGuards(GraphqlAuthGuard)
    @Query(() => [JobAdvert], { name: 'jobAdsByCompanyId' })
    async findJobAdByCompanyId(
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        return this.jobAdvertService.findCompanyJobAds(companyId)
    }

    @UseGuards(CompanyGuard)
    @Query(() => [JobAdvert], { name: 'findJobAdsByCompanyIdForCompany' })
    async findJobAdsByCompanyIdForCompany(
        @Context() context: any,
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        const companyUserId = context?.req?.user?.companyUserId
        const isSuper = context?.req?.user?.isSuperUser
        const verifiedCompanyId = context?.req?.user?.companyId === companyId
        if (!verifiedCompanyId && !isSuper) {
            throw new Error('UnAuthorized:: You are not authorized')
        }

        return await this.jobAdvertService.findByCompany(
            companyId,
            companyUserId
        )
    }

    @UseGuards(CompanyGuard)
    @Query(() => [JobAdvert], { name: 'findJobAdsByCompanyIdForApplicant' })
    findJobAdsByCompanyIdForApplicant(
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        return this.jobAdvertService.findCompanyJobAds(companyId)
    }

    @UseGuards(ApplicantGuard)
    @Query(() => [JobAdvert], { name: 'jobAdsByFilter' })
    findJobAdsByFilter(
        @Context() context: any,
        @Args('includeDisliked', { type: () => Boolean, nullable: true })
        includeDisliked: boolean,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string,
        @Args('filterOptions', { nullable: true })
        filterOptions: JobAdvertFilterOptionsInput
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId

        return this.jobAdvertService.filteredJobAdverts(
            bApplicantId,
            includeDisliked,
            filterOptions
        )
    }

    @UseGuards(SuperGuard)
    @Mutation(() => JobAdvert, { name: 'approveJobAdvert' })
    async approveJobAdvert(
        @Context() context: any,
        @Args('jobAdId', { type: () => String })
        jobAdId: string,
        @Args('superUserId', { type: () => String, nullable: true })
        superUserId: string
    ) {
        const bSuperUser = superUserId || context?.req?.user?.superUserId

        return await this.jobAdvertService.approveJobAd(jobAdId, bSuperUser)
    }

    @UseGuards(SuperGuard)
    @Mutation(() => JobAdvert, { name: 'blockJobAdvert' })
    async blockJobAdvert(
        @Context() context: any,
        @Args('jobAdId', { type: () => String })
        jobAdId: string,
        @Args('declineReason', { type: () => String })
        declineReason: string,
        @Args('superUserId', { type: () => String, nullable: true })
        superUserId: string
    ) {
        const bSuperUser = superUserId || context?.req?.user?.superUserId

        return await this.jobAdvertService.blockJobAd(
            jobAdId,
            bSuperUser,
            declineReason
        )
    }

    @UseGuards(SuperGuard)
    @Query(() => [JobAdvert], { name: 'jobAdsByLocation' })
    findJobAdsByLocation(
        @Args('latitude', { type: () => Number, nullable: true }) lat: number,
        @Args('longitude', { type: () => Number, nullable: true }) lon: number,
        @Args('radius', { type: () => Number, nullable: true }) radius: number
    ) {
        return this.jobAdvertService.findNearbyJobAdverts(lat, lon, radius)
    }

    @UseGuards(ApplicantGuard)
    @Query(() => [JobAdvert], { name: 'jobAdsUnSeen' })
    findJobAdsUnseen(
        @Args('applicantId', { type: () => String }) applicantId: string
    ) {
        return this.jobAdvertService.jobAdvertsUnseen(applicantId)
    }

    @UseGuards(ApplicantGuard)
    @Query(() => [JobAdvert], { name: 'jobAdsSeen' })
    findJobAdsSeen(
        @Args('applicantId', { type: () => String }) applicantId: string
    ) {
        return this.jobAdvertService.jobAdvertsSeen(applicantId)
    }

    @UseGuards(ApplicantGuard)
    @Query(() => [JobAdvert], { name: 'jobAdsBookmarked' })
    findJobAdsBookmarked(
        @Args('applicantId', { type: () => String }) applicantId: string
    ) {
        return this.jobAdvertService.jobAdvertsBookmarked(applicantId)
    }

    @UseGuards(ApplicantGuard)
    @Query(() => [JobAdvert], { name: 'jobAdsNotBookmarked' })
    findJobAdsNotBookmarked(
        @Args('applicantId', { type: () => String }) applicantId: string
    ) {
        return this.jobAdvertService.jobAdvertsNotBookmarked(applicantId)
    }

    @UseGuards(ApplicantGuard)
    @Query(() => JobAdvert, { name: 'jobAdById' })
    findOneForApplicant(@Args('id', { type: () => String }) id: string) {
        return this.jobAdvertService.findOneForApplicant(id)
    }

    @UseGuards(CompanyGuard)
    @Query(() => JobAdvert, { name: 'jobAdByIdForCompany', nullable: true })
    findOneJobForCompany(
        @Args('id', { type: () => String }) id: string,
        @Context() context: any
    ) {
        const bSuperUser =
            context?.req?.user?.superUserId || context?.req?.user?.fgAdminId
        const companyUserId = context?.req?.user?.companyUserId
        return this.jobAdvertService.findOne(id, bSuperUser, companyUserId)
    }

    @UseGuards(CompanyGuard)
    @Query(() => JobAdvertStats, { name: 'jobAdStatsByCompanyId' })
    getJobAdvertStatsByCompany(@Args('id', { type: () => String }) id: string) {
        return this.jobAdvertService.jobAdvertStatsByCompany(id)
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => JobAdvert, { name: 'pauseJobAdvert' })
    pauseJobAdvert(@Args('id', { type: () => String }) id: string) {
        return this.jobAdvertService.pauseResumeJob(id, true)
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => JobAdvert, { name: 'resumeJobAdvert' })
    resumeJobAdvert(@Args('id', { type: () => String }) id: string) {
        return this.jobAdvertService.pauseResumeJob(id, false)
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => JobAdvert, { name: 'removeJobAdvert' })
    removeJobAdvert(@Args('id', { type: () => String }) id: string) {
        return this.jobAdvertService.remove(id)
    }
}
