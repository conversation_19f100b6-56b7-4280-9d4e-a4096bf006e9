import { Field, ObjectType, Int } from '@nestjs/graphql'

@ObjectType('JobAdvertStats')
export class JobAdvertStats {
    @Field(() => Int, { nullable: false, defaultValue: 0 })
    likes!: number

    @Field(() => Int, { nullable: false, defaultValue: 0 })
    impressions!: number

    @Field(() => Int, { nullable: false, defaultValue: 0 })
    bookmarks!: number

    @Field(() => Int, { nullable: false, defaultValue: 0 })
    matches!: number
}
