import { ObjectType, Field } from '@nestjs/graphql'
import { CompanyUser } from '../../company-user/entities/company-user.entity'
import { Company } from '../../company/entities/company.entity'
import { JobCategory } from '../../job-category/entities/job-category.entity'
import { JobAdvertStats } from './job-advert-stats'
import { JobAction } from '../../job-actions/entities/job-action.entity'
import { JobAdvertType } from '@prisma/client'
import { StripeSubscription } from '../../subscription/entities/subscription.entity'

@ObjectType()
export class JobAdvert {
    @Field()
    id: string

    @Field()
    title: string

    @Field({ nullable: true })
    address: string

    @Field({ nullable: true })
    approved: boolean

    @Field({ nullable: true })
    paused: boolean

    @Field(() => [JobCategory], { nullable: true })
    categories: JobCategory[]

    @Field({ nullable: true })
    city: string

    @Field({ nullable: true })
    workHours: number

    @Field({ nullable: true })
    latitude: number

    @Field({ nullable: true })
    longitude: number

    @Field({ nullable: true })
    isDeclined: boolean

    @Field({ nullable: true })
    isDeleted: boolean

    @Field({ nullable: true })
    isDraft: boolean

    @Field({ nullable: true })
    description: string

    @Field({ nullable: true })
    dynamicLink: string

    @Field({ nullable: true })
    declineReason: string

    @Field({ nullable: true })
    detailDescription: string

    @Field({ nullable: true })
    district: string

    @Field({ nullable: true })
    educationDuration: number

    @Field({ nullable: true })
    headerImageUrl: string

    @Field({ nullable: true })
    holidayDays: number

    @Field(() => [CompanyUser], { nullable: true })
    responsibleUsers: CompanyUser[]

    @Field(() => [Number], { nullable: true })
    gehalt?: number[]

    @Field({ nullable: true })
    companyUserId: string

    @Field({ nullable: true })
    startDate: Date

    @Field({ nullable: true })
    status: string

    @Field({ nullable: true })
    type: JobAdvertType

    @Field({ nullable: true })
    createdAt: Date

    @Field({ nullable: true })
    activeFromDate: Date

    @Field({ nullable: true })
    updatedAt: Date

    @Field(() => Company)
    company: Company

    @Field({ nullable: true })
    companyId: string

    @Field(() => [JobAction], { nullable: true })
    jobAction: JobAction[]

    @Field(() => [StripeSubscription], { nullable: true })
    subscriptions: StripeSubscription[]

    @Field({ nullable: true })
    _count?: JobAdvertStats
}
