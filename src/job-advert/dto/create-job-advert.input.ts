import { InputType, Field } from '@nestjs/graphql'
import {
    IsString,
    IsN<PERSON>ber,
    IsBoolean,
    IsDate,
    IsOptional,
    IsArray,
} from 'class-validator'
import { JobAdvertType } from '@prisma/client'

@InputType()
export class CreateJobAdvertInput {
    @IsString()
    @Field()
    title: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    address?: string

    @Field(() => [Number])
    @IsOptional()
    @IsArray()
    gehalt?: number[]

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    workHours?: number

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    approved?: boolean

    @IsString()
    @Field()
    city: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    companyName?: string

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    latitude?: number

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    longitude?: number

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isDeclined?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isDeleted?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isDraft?: boolean

    @IsString()
    @Field()
    description: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    declineReason?: string

    @IsString()
    @Field()
    detailDescription: string

    @IsString()
    @Field()
    district: string

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    educationDuration?: number

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    headerImageUrl?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    imageUrl?: string

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    holidayDays?: number

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    impressions?: number

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    companyUserId?: string

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    jobCategoryId?: string

    @IsDate()
    @Field()
    startDate: Date

    @IsDate()
    @IsOptional()
    @Field({ nullable: true })
    endDate?: Date

    @IsDate()
    @IsOptional()
    @Field({ nullable: true })
    activeFromDate?: Date

    @IsString()
    @Field()
    type: JobAdvertType
}
