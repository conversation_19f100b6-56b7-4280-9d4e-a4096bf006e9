import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional, IsNumber, IsArray } from 'class-validator'
import { JobAdvertType } from '@prisma/client'

@InputType()
export class JobAdvertFilterOptionsInput {
    @IsArray()
    @Field(() => [JobAdvertType], { nullable: true })
    @IsOptional()
    type?: JobAdvertType[]

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    categoryId?: string

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    longitude?: number

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    latitude?: number
}
