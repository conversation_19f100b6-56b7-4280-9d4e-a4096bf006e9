import { Module } from '@nestjs/common'
import { JobActionsService } from './job-actions.service'
import { JobActionsResolver } from './job-actions.resolver'
import { AuthModule } from '../auth/auth.module'
import { NotificationModule } from '../notification/notification.module'
import { ChatRoomModule } from '../chat-room/chat-room.module'
import { JobActionHistoryModule } from '../job-action-history/job-action-history.module'
import { PrismaModule } from '../prisma.module'
import { PusherPubSubModule } from '../pub-sub/pusher-pub-sub.module'
import { JobAdvertModule } from '../job-advert/job-advert.module'
import { JobActionValidatorService } from './job-action-validator/job-action-validator.service'
import { JobActionNotifierService } from './job-action-notifier/job-action-notifier.service'
import { JobActionPublisherService } from './job-action-publish/job-action-publisher.service'

@Module({
    imports: [
        AuthModule,
        NotificationModule,
        ChatRoomModule,
        JobActionHistoryModule,
        JobAdvertModule,
        PrismaModule,
        PusherPubSubModule,
    ],
    exports: [JobActionsService],
    providers: [
        JobActionsResolver,
        JobActionsService,
        JobActionPublisherService,
        JobActionValidatorService,
        JobActionNotifierService,
    ],
})
export class JobActionsModule {}
