import { ObjectType, Field } from '@nestjs/graphql'
import { JobAdvert } from '../../job-advert/entities/job-advert.entity'
import { Applicant } from '../../applicant/entities/applicant.entity'
import { ActionState } from '@prisma/client'

@ObjectType()
export class JobAction {
    @Field()
    id: string

    @Field(() => JobAdvert)
    jobAdvert: JobAdvert

    @Field(() => Applicant)
    applicant: Applicant

    @Field({ nullable: true })
    status: string

    @Field({ nullable: true })
    chatRoomId: string

    @Field(() => ActionState)
    state: ActionState

    @Field({ nullable: true })
    deletedFromCompany: boolean

    @Field({ nullable: true })
    declineReason: string

    @Field({ nullable: true })
    deletedFromApplicant: boolean

    @Field({ nullable: true })
    companyIsNew: boolean

    @Field({ nullable: true })
    applicantIsNew: boolean
}
