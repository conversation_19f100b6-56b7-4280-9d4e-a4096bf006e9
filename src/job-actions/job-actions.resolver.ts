import { Args, Context, Mutation, Query, Resolver } from '@nestjs/graphql'
import { JobActionsService } from './job-actions.service'
import { JobAction } from './entities/job-action.entity'
import { CreateJobActionInput } from './dto/create-job-action.input'
import { UpdateJobActionInput } from './dto/update-job-action.input'
import { CreateUpdateJobActionInput } from './dto/create-update-job-action.input'
import { JobAdvertStats } from '../job-advert/entities/job-advert-stats'
import { UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { CompanyGuard } from '../guards/company-guard'
import { ApplicantGuard } from '../guards/applicant-guard'
import { SuperGuard } from '../guards/super-guard'

@Resolver(() => JobAction)
export class JobActionsResolver {
    constructor(private readonly jobActionsService: JobActionsService) {}

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => JobAction)
    async createJobAction(
        @Context() context: any,
        @Args('createJobActionInput')
        createJobActionInput: CreateJobActionInput,
        @Args('jobAdvertId', { type: () => String }) jobAdvertId: string,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        const createAction = await this.jobActionsService.create(
            createJobActionInput,
            jobAdvertId,
            bApplicantId
        )

        return createAction
    }

    @UseGuards(SuperGuard)
    @Query(() => [JobAction], { name: 'allJobActions' })
    findAll() {
        return this.jobActionsService.findAll()
    }

    @UseGuards(CompanyGuard)
    @Query(() => JobAction, { name: 'jobActionById' })
    findOne(
        @Args('id', { type: () => String }) id: string,
        @Context() context: any
    ) {
        const companyUserId = context?.req?.user?.companyUserId
        return this.jobActionsService.findOne(id, companyUserId)
    }

    @UseGuards(CompanyGuard)
    @Query(() => [JobAction], { name: 'jobLikesByAdvertId' })
    findAdvertLikes(
        @Context() context: any,
        @Args('advertId', { type: () => String }) advertId: string
    ) {
        const companyUserId = context?.req?.user?.companyUserId
        const isSuper =
            context?.req?.user?.isSuperUser || context?.req?.user?.isFGAdmin
        return this.jobActionsService.findLikesByJobAdvert(
            advertId,
            companyUserId,
            isSuper
        )
    }

    @UseGuards(ApplicantGuard)
    @Query(() => [JobAction], { name: 'jobActionsByApplicantId' })
    findApplicantLikes(
        @Context() context: any,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        return this.jobActionsService.findJobActionsByApplicant(bApplicantId)
    }

    @UseGuards(CompanyGuard)
    @Query(() => JobAdvertStats, { name: 'jobAdStatsById' })
    getSingleJobAdvertStats(
        @Context() context: any,
        @Args('id', { type: () => String }) id: string
    ) {
        const companyUserId = context?.req?.user?.companyUserId
        return this.jobActionsService.getJobAdvertStats(
            id,
            companyUserId,
            context?.req?.user?.isSuperUser
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => JobAction)
    async updateJobAction(
        @Context() context: any,
        @Args('updateJobActionInput')
        updateJobActionInput: UpdateJobActionInput,
        @Args('currentState', {
            type: () => String,
            nullable: true,
        })
        currentState: JobAction['state']
    ) {
        const companyUserId = context?.req?.user?.companyUserId || null
        const applicantId = context?.req?.user?.applicantId || null
        const isSuper =
            context?.req?.user?.isSuperUser || context?.req?.user?.isFGAdmin

        return await this.jobActionsService.update(
            updateJobActionInput,
            companyUserId,
            currentState,
            applicantId,
            isSuper
        )
    }

    @UseGuards(CompanyGuard)
    @Mutation(() => JobAction)
    async declineApplicant(
        @Args('jobActionId') jobActionId: string,
        @Context() context: any,
        @Args('declineReason', { nullable: true }) declineReason?: string
    ) {
        const companyUserId = context?.req?.user?.companyUserId
        return await this.jobActionsService.declineMatch(
            jobActionId,
            companyUserId,
            declineReason
        )
    }
    @UseGuards(ApplicantGuard)
    @Mutation(() => JobAction)
    async declineJobAction(@Args('jobActionId') jobActionId: string) {
        return await this.jobActionsService.declineJobAction(jobActionId)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => JobAction, { name: 'setJobAction' })
    async createOrUpdateJobAction(
        @Context() context: any,
        @Args('createOrUpdateJobActionInput')
        createUpdateJobActionInput: CreateUpdateJobActionInput
    ) {
        console.log({ createUpdateJobActionInput })
        const bApplicantId =
            context?.req?.user?.applicantId ||
            createUpdateJobActionInput.applicantId
        const companyUserId = context?.req?.user?.companyUserId || null

        return await this.jobActionsService.createOrUpdate(
            bApplicantId,
            companyUserId,
            createUpdateJobActionInput,
            context?.req?.user?.isSuperUser
        )
    }
}
