import {
    Injectable,
    NotFoundException,
    UnauthorizedException,
} from '@nestjs/common'
import { CreateJobActionInput } from './dto/create-job-action.input'
import { UpdateJobActionInput } from './dto/update-job-action.input'
import { PrismaService } from '../prisma.service'
import { CreateUpdateJobActionInput } from './dto/create-update-job-action.input'
import { ActionState, JobAction } from '@prisma/client'
import { ChatRoomService } from '../chat-room/chat-room.service'
import { JobActionHistoryService } from '../job-action-history/job-action-history.service'
import { JobAdvertService } from '../job-advert/job-advert.service'
import { RedisPubSub } from 'graphql-redis-subscriptions'
import { JobActionNotifierService } from './job-action-notifier/job-action-notifier.service'
import { JobActionPublisherService } from './job-action-publish/job-action-publisher.service'
import { JobActionValidatorService } from './job-action-validator/job-action-validator.service'

@Injectable()
export class JobActionsService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly chatRoomService: ChatRoomService,
        private readonly jobActionHistoryService: JobActionHistoryService,
        private readonly jobAdvertService: JobAdvertService,
        private readonly jobActionPublisher: JobActionPublisherService,
        private readonly jobActionNotifierService: JobActionNotifierService,
        private readonly jobActionValidatorService: JobActionValidatorService
    ) {}

    private prepareJobActionData(
        input: CreateJobActionInput,
        jobAdvertId: string,
        applicantId: string
    ) {
        return {
            ...input,
            status: input.status || input.state,
            jobAdvert: { connect: { id: jobAdvertId } },
            applicant: { connect: { id: applicantId } },
        }
    }

    private getJobActionIncludes() {
        return {
            applicant: true,
            jobAdvert: {
                include: {
                    company: true,
                    jobAction: true,
                },
            },
        }
    }

    async create(
        createJobActionInput: CreateJobActionInput,
        jobAdvertId: string,
        applicantId: string
    ) {
        await this.jobActionValidatorService.validateBeforeCreateJobAction(
            createJobActionInput,
            applicantId,
            jobAdvertId
        )

        const jobAction = await this.prisma.jobAction.create({
            data: this.prepareJobActionData(
                createJobActionInput,
                jobAdvertId,
                applicantId
            ),
            include: this.getJobActionIncludes(),
        })

        if (!jobAction) throw new NotFoundException('JobAction not created')

        await this.jobActionNotifierService.notifyOnCreate(
            createJobActionInput.state,
            jobAction,
            applicantId,
            jobAdvertId
        )

        await this.jobActionHistoryService.create(jobAction.id, {
            prevState: jobAction.state,
            newState: jobAction.state,
            type: 'create',
        })

        await this.jobActionPublisher.publishOnCreate(
            jobAction,
            applicantId,
            jobAdvertId,
            jobAction.state
        )

        return jobAction
    }

    findAll() {
        return this.prisma.jobAction.findMany({
            include: {
                applicant: true,
                jobAdvert: true,
            },
        })
    }

    async findOne(id: string, companyUserId?: string) {
        try {
            const jobAction = await this.prisma.jobAction.findUnique({
                where: {
                    id: id,
                },
                include: { applicant: true, jobAdvert: true },
            })

            if (
                await this.canAccessJobAction(
                    companyUserId,
                    jobAction.jobAdvertId
                )
            ) {
                return jobAction
            } else {
                throw new UnauthorizedException(
                    'You are not authorized to access job action'
                )
            }
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    async findLikesByJobAdvert(
        jobAdvertId: string,
        companyUserId: string,
        isSuper?: boolean
    ) {
        const likes = await this.prisma.jobAction.findMany({
            where: {
                jobAdvertId: jobAdvertId,
                deletedFromCompany: false,
                state: {
                    in: [ActionState.LIKED, ActionState.MATCHED],
                },
            },
            include: {
                applicant: true,
                jobAdvert: true,
            },
        })

        if (
            (await this.canAccessJobAction(companyUserId, jobAdvertId)) ||
            isSuper
        ) {
            return likes
        } else {
            throw new UnauthorizedException(
                'You are not authorized to access job action'
            )
        }
    }

    getCompanyIdFromJobAdvert(jobAdvertId: string) {
        return this.prisma.jobAdvert.findUnique({
            where: {
                id: jobAdvertId,
            },
            select: {
                companyId: true,
            },
        })
    }

    async getJobAdvertStats(
        jobAdvertId: string,
        companyUserId?: string,
        isSuperUser?: boolean
    ) {
        if (!jobAdvertId) throw new NotFoundException('JobAdvert not found')

        const likes = await this.prisma.jobAction.groupBy({
            by: ['state'],
            where: {
                state: ActionState.LIKED,
                jobAdvertId: jobAdvertId,
                deletedFromCompany: false,
            },
            _count: {
                state: true,
            },
        })

        const bookmarks = await this.prisma.jobAction.groupBy({
            by: ['state'],
            where: {
                state: ActionState.BOOKMARKED,
                jobAdvertId: jobAdvertId,
                deletedFromCompany: false,
            },
            _count: {
                state: true,
            },
        })

        const impressions = await this.prisma.jobAction.groupBy({
            by: ['jobAdvertId'],
            where: {
                jobAdvertId: jobAdvertId,
            },
            _count: {
                jobAdvertId: true,
            },
        })

        const matched = await this.prisma.jobAction.groupBy({
            by: ['state'],
            where: {
                state: ActionState.MATCHED,
                jobAdvertId: jobAdvertId,
                deletedFromCompany: false,
                deletedFromApplicant: false,
            },
            _count: {
                state: true,
            },
        })

        const likeStats = {
            likes: likes[0]?._count.state || 0,
            bookmarks: bookmarks[0]?._count.state || 0,
            impressions: impressions[0]?._count.jobAdvertId || 0,
            matches: matched[0]?._count.state || 0,
        }

        if (
            await this.canAccessJobAction(
                companyUserId,
                jobAdvertId,
                isSuperUser
            )
        ) {
            return likeStats
        } else {
            throw new UnauthorizedException(
                'You are not authorized to access JobAdvert Stats'
            )
        }
    }

    findJobActionsByApplicant(applicantId: string) {
        return this.prisma.jobAction.findMany({
            where: {
                applicantId: applicantId,
                deletedFromApplicant: false,
                state: {
                    in: [
                        ActionState.BOOKMARKED,
                        ActionState.DELETED,
                        ActionState.LIKED,
                        ActionState.MATCHED,
                    ],
                },
            },
            include: {
                chatRoom: true,
                applicant: true,
                jobAdvert: {
                    include: {
                        company: true,
                    },
                },
            },
        })
    }

    assertNotDeleted(state: ActionState) {
        if (state === ActionState.DELETED) {
            throw new NotFoundException('Already deleted')
        }
    }

    assertCanMatch(
        state: ActionState,
        companyUserId?: string,
        isSuperUser?: boolean
    ) {
        console.log('assertCanMatch', isSuperUser)
        if (isSuperUser) return
        if (state === ActionState.MATCHED && !companyUserId) {
            throw new UnauthorizedException('Not authorized to match job')
        }
    }

    async update(
        updateJobActionInput: UpdateJobActionInput,
        companyUserId: string,
        currentState: JobAction['state'] = undefined,
        applicantId?: string,
        isSuperUser?: boolean
    ) {
        let updatedJobAction = null
        const jobAction = await this.prisma.jobAction.findFirst({
            where: { id: updateJobActionInput.id },
            include: {
                applicant: true,
                jobAdvert: {
                    include: {
                        company: true,
                    },
                },
            },
        })

        if (applicantId && jobAction?.applicantId !== applicantId) {
            throw new UnauthorizedException(
                'You are not authorized to access Job action'
            )
        }

        if (!applicantId && companyUserId) {
            const canAccess = await this.canAccessJobAction(
                companyUserId,
                jobAction?.jobAdvertId,
                isSuperUser
            )
            if (companyUserId && !canAccess)
                throw new UnauthorizedException(
                    'You are not authorized to access Job action'
                )
        }

        if (typeof updateJobActionInput?.state === undefined) {
            updatedJobAction = await this.updateNoState(updateJobActionInput)
        } else {
            updatedJobAction = await this.updateWithState(
                updateJobActionInput,
                companyUserId,
                currentState,
                isSuperUser
            )
        }

        const { companyId } = await this.getCompanyIdFromJobAdvert(
            jobAction.jobAdvert.id
        )

        await this.jobActionPublisher.publishToClient(
            updatedJobAction,
            updatedJobAction.state,
            applicantId,
            jobAction.jobAdvert.id,
            companyId
        )

        return updatedJobAction
    }

    async declineMatch(
        jobActionId: string,
        companyUserId: string,
        declineReason: string
    ) {
        const jobAction = await this.prisma.jobAction.findUnique({
            where: {
                id: jobActionId,
            },
        })

        if (!jobAction) throw new NotFoundException('JobAction not found')

        const updatedAction = await this.update(
            {
                id: jobActionId,
                deletedFromCompany: true,
                declineReason: declineReason,
                status: ActionState.DELETED,
                state: jobAction.deletedFromApplicant
                    ? ActionState.DELETED
                    : jobAction.state,
            },
            companyUserId,
            jobAction.state,
            jobAction.applicantId,
            false
        )

        if (!updatedAction)
            throw new NotFoundException('JobAction not declined.')

        return updatedAction
    }

    async declineJobAction(jobActionId: string) {
        const jobAction = await this.prisma.jobAction.findUnique({
            where: {
                id: jobActionId,
            },
        })

        if (!jobAction) throw new NotFoundException('JobAction not found')

        const updatedAction = await this.update(
            {
                id: jobActionId,
                deletedFromApplicant: true,
                status: ActionState.DELETED,
                state: jobAction.deletedFromCompany
                    ? ActionState.DELETED
                    : jobAction.state,
            },
            '',
            jobAction.state,
            jobAction.applicantId,
            false
        )

        if (!updatedAction)
            throw new NotFoundException('JobAction not declined.')

        return updatedAction
    }

    async updateNoState(updateJobActionInput: UpdateJobActionInput) {
        const jobAction = await this.prisma.jobAction.findFirst({
            where: { id: updateJobActionInput.id },
        })

        if (!jobAction) throw new NotFoundException('JobAction not found')

        const updatedAction = await this.prisma.jobAction.update({
            where: {
                id: updateJobActionInput.id,
            },
            data: {
                ...updateJobActionInput,
            },
            include: {
                applicant: true,
                jobAdvert: {
                    include: {
                        company: true,
                        jobAction: true,
                    },
                },
            },
        })

        if (!updatedAction) throw new NotFoundException('JobAction not updated')

        return updatedAction
    }

    async updateWithState(
        updateJobActionInput: UpdateJobActionInput,
        companyUserId: string,
        currentState: JobAction['state'] = undefined,
        isSuperUser?: boolean
    ) {
        let jobAction = null
        let chatRoom = null

        if (!currentState) {
            try {
                jobAction = await this.prisma.jobAction.findFirst({
                    where: { id: updateJobActionInput.id },
                    include: {
                        applicant: true,
                        jobAdvert: {
                            include: {
                                company: true,
                                jobAction: true,
                            },
                        },
                    },
                })
                currentState = jobAction?.state
            } catch (error) {
                throw new NotFoundException(
                    `JobAction with id: ${updateJobActionInput.id} not found`
                )
            }
        }

        this.assertNotDeleted(currentState)
        this.assertCanMatch(updateJobActionInput.state, companyUserId)

        const processUpdate = await this.processActionUpdate({
            newState: updateJobActionInput.state || currentState,
            currentState,
            isCompany: !!companyUserId,
        })
        //Create Chat room if action is matched
        if (
            processUpdate.canUpdate &&
            updateJobActionInput.state === ActionState.MATCHED
        ) {
            //Check if chat room already exists
            const chatRoomExists = await this.prisma.chatRoom.findFirst({
                where: {
                    jobAction: {
                        id: updateJobActionInput.id,
                    },
                },
            })
            if (!chatRoomExists) {
                chatRoom = await this.chatRoomService.create(
                    updateJobActionInput?.id,
                    {
                        status: 'Active',
                    }
                )
            }
        }

        const { applicantIsNew } = processUpdate

        const updatedAction =
            processUpdate?.canUpdate &&
            (await this.prisma.jobAction.update({
                where: {
                    id: updateJobActionInput.id || jobAction?.id,
                },
                data: {
                    ...updateJobActionInput,
                    status:
                        updateJobActionInput?.status ||
                        updateJobActionInput?.state,
                    companyIsNew: updateJobActionInput.companyIsNew,
                    applicantIsNew:
                        typeof updateJobActionInput.applicantIsNew !==
                        'undefined'
                            ? updateJobActionInput.applicantIsNew
                            : applicantIsNew,
                },
                include: {
                    applicant: true,
                    chatRoom: true,
                    jobAdvert: {
                        include: {
                            company: true,
                            jobAction: true,
                        },
                    },
                },
            }))

        if (!updatedAction || !processUpdate?.canUpdate)
            throw new NotFoundException(
                `JobAction not updated: ${processUpdate.message}`
            )

        //Notify on update start
        await this.jobActionNotifierService.notifyOnUpdate(
            updateJobActionInput.state,
            updatedAction
        )

        //Create JobActionHistory
        if (currentState !== updatedAction.state) {
            await this.jobActionHistoryService.create(updatedAction.id, {
                prevState: currentState,
                newState: updatedAction.state,
                type: 'update',
            })
        }

        await this.jobAdvertService.publishJobAdvertEvent(
            updatedAction.jobAdvert
        )

        return { ...updatedAction, chatRoomId: chatRoom?.id }
    }

    async createOrUpdate(
        bApplicantId: string,
        companyUserId: string,
        data: CreateUpdateJobActionInput,
        isSuperUser?: boolean
    ) {
        const { id, jobAdvertId, applicantId, ...createInput } = data
        const applicantIdToUse = bApplicantId || applicantId

        let updateJobAction = null

        const actionExists = await this.prisma.jobAction.findFirst({
            where: {
                AND: {
                    applicantId: applicantIdToUse,
                    jobAdvertId: jobAdvertId,
                },
            },
        })

        try {
            if (actionExists === null || id === null) {
                return await this.create(
                    createInput,
                    jobAdvertId,
                    applicantIdToUse
                )
            }

            const actionId = id || actionExists?.id

            const jobAction = await this.prisma.jobAction.findUnique({
                where: { id: actionId },
            })

            //Check if user can access job action
            if (applicantIdToUse !== jobAction.applicantId) {
                throw new UnauthorizedException(
                    'You are not authorized to access job action'
                )
            }

            const currentState = jobAction?.state

            if (!jobAction?.id) {
                updateJobAction = await this.create(
                    createInput,
                    jobAdvertId,
                    applicantIdToUse
                )
            } else {
                updateJobAction = await this.update(
                    { id: actionId, ...createInput },
                    companyUserId,
                    currentState,
                    applicantIdToUse,
                    isSuperUser
                )
            }

            return updateJobAction
        } catch (e) {
            throw new NotFoundException(e.message)
        }
    }

    async processActionUpdate({
        newState,
        currentState,
        isCompany = false,
    }: {
        newState: JobAction['state']
        currentState: JobAction['state']
        isCompany?: boolean
    }) {
        switch (newState) {
            case ActionState.BOOKMARKED:
                const canBookmark = !(
                    currentState === ActionState.LIKED ||
                    currentState === ActionState.MATCHED
                )
                return {
                    canUpdate: canBookmark,
                    message: `Cannot bookmark a ${currentState?.toLowerCase()} job`,
                }

            case ActionState.LIKED:
                const canLike = currentState !== ActionState.MATCHED
                return {
                    canUpdate: canLike,
                    message: canLike
                        ? 'Can like job'
                        : `Cannot like a ${currentState?.toLowerCase()} job`,
                }

            case ActionState.DISLIKED:
                const canDislike = currentState !== ActionState.MATCHED
                let deleteFromApplicant = false
                if (currentState === ActionState.LIKED)
                    deleteFromApplicant = true

                return {
                    deleteFromApplicant,
                    canUpdate: canDislike,
                    message: canDislike
                        ? 'Can dislike job'
                        : 'Cannot dislike a matched job',
                }

            case ActionState.DELETED:
                return {
                    deletedFromApplicant: !isCompany,
                    deletedFromCompany: isCompany,
                    canUpdate: true,
                    message: 'Can delete job',
                }

            case ActionState.MATCHED:
                return {
                    applicantIsNew: true,
                    canUpdate: true,
                    message: 'Can match job',
                }

            default:
                return {
                    canUpdate: false,
                    message: 'Invalid Job Action',
                }
        }
    }

    async canAccessJobAction(
        companyUserId: string,
        jobAdvertId: string,
        isSuperUser?: boolean
    ) {
        //Check if user is a super admin from request context user

        const jobAds = await this.prisma.jobAdvert.findMany({
            where: {
                id: jobAdvertId,
                isDeleted: false,
                responsibleUsers: {
                    some: {
                        id: companyUserId,
                    },
                },
            },
        })

        const companyUser = await this.prisma.companyUser.findFirst({
            where: {
                id: companyUserId,
            },
            include: {
                userRights: true,
            },
        })

        const isAdmin = companyUser?.userRights[0]?.superAdmin || isSuperUser

        const isResponsibleUser = jobAds.length > 0

        return isAdmin || isResponsibleUser
    }
}
