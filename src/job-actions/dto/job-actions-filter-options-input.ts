import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional, IsEnum, IsBoolean } from 'class-validator'
import { ActionState } from '@prisma/client'

@InputType()
export class JobActionsFilterOptionsInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string

    @IsEnum(ActionState)
    @Field({ nullable: true })
    @IsOptional()
    state?: ActionState

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    deletedFromCompany?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    deletedFromApplicant?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    companyIsNew?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    applicantIsNew?: boolean
}
