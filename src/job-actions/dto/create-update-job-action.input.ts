import { InputType, Field, registerEnumType } from '@nestjs/graphql'
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator'
import { ActionState } from '@prisma/client'

registerEnumType(ActionState, {
    name: 'ActionState',
})

@InputType()
export class CreateUpdateJobActionInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    id?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    jobAdvertId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    applicantId?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    declineReason?: string

    @IsEnum(ActionState)
    @IsOptional()
    @Field(() => ActionState, { nullable: true })
    state: ActionState

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    deletedFromCompany?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    deletedFromApplicant?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    companyIsNew?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    applicantIsNew?: boolean
}
