import { InputType, Field, registerEnumType } from '@nestjs/graphql'
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator'
import { ActionState } from '@prisma/client'

registerEnumType(ActionState, {
    name: 'ActionState',
})

@InputType()
export class CreateJobActionInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    status?: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    declineReason?: string

    @IsEnum(ActionState)
    @Field(() => ActionState)
    state: ActionState

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    deletedFromCompany?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    deletedFromApplicant?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    companyIsNew?: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    applicantIsNew?: boolean
}
