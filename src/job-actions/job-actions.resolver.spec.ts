import { Test, TestingModule } from '@nestjs/testing'
import { JobActionsResolver } from './job-actions.resolver'
import { JobActionsService } from './job-actions.service'

describe('JobActionsResolver', () => {
    let resolver: JobActionsResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [JobActionsResolver, JobActionsService],
        }).compile()

        resolver = module.get<JobActionsResolver>(JobActionsResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
