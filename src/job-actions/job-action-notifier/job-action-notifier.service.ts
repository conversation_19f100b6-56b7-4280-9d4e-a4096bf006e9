import { Injectable, NotFoundException } from '@nestjs/common'
import { ActionState } from '@prisma/client'
import {
    CreateEmailNotificationInput,
    CreateNotificationInput,
} from '../../notification/dto/create-notification.input'
import { SendNotificationParams } from '../../notification/type/notificationTypes'
import { PrismaService } from '../../prisma.service'
import { NotificationService } from '../../notification/notification.service'

@Injectable()
export class JobActionNotifierService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly notificationService: NotificationService
    ) {}

    async notifyOnCreate(
        state: ActionState,
        jobAction: any,
        applicantId: string,
        jobAdvertId: string
    ) {
        const createNotificationInput = {
            isNew: true,
            type: 'newLike',
        }
        const createEmailNotificationInput = {
            emailBody: `Es gibt eine neue Bewerbung zu Ihrer Stellenanzeige: ${jobAction.jobAdvert?.title}`,
            emailSubject: `Neue Bewerbung zu ${jobAction.jobAdvert?.title}`,
        }

        const type = state

        if (state === ActionState.LIKED || state === ActionState.MATCHED) {
            await this.notifyJobActionUpdate(
                createNotificationInput,
                createEmailNotificationInput,
                jobAdvertId,
                applicantId,
                jobAction.deletedFromCompany || jobAction.deletedFromApplicant,
                type
            )
        }
    }

    async notifyOnUpdate(state: ActionState, updatedAction: any) {
        if (state === ActionState.LIKED || state === ActionState.MATCHED) {
            const type = state

            const createNotificationInput = {
                isNew: true,
                type,
            }

            const createEmailNotificationInput = {
                emailBody: `Es gibt eine neue Bewerbung zu Ihrer Stellenanzeige: ${updatedAction.jobAdvert?.title}`,
                emailSubject: `Neue Bewerbung zu ${updatedAction.jobAdvert?.title}`,
            }

            const jobAdvertId = updatedAction.jobAdvert?.id
            const applicantId = updatedAction?.applicant?.id
            const deletedFromSide =
                updatedAction?.deletedFromCompany ||
                updatedAction?.deletedFromApplicant

            // call notifyJobActionUpdate
            await this.notifyJobActionUpdate(
                createNotificationInput,
                createEmailNotificationInput,
                jobAdvertId,
                applicantId,
                deletedFromSide,
                type
            )
        }
    }

    async notifyJobActionUpdate(
        createNotificationInput: CreateNotificationInput,
        createEmailNotificationInput: CreateEmailNotificationInput,
        jobAdvertId: string,
        applicantId?: string,
        deletedFromSide?: boolean | undefined,
        type?: ActionState
    ) {
        const jobAdvert = await this.prisma.jobAdvert.findUnique({
            where: {
                id: jobAdvertId,
            },
            include: { company: true },
        })

        const applicant = await this.prisma.applicant.findUnique({
            where: {
                id: applicantId,
            },
        })

        if (!jobAdvert) throw new NotFoundException('JobAdvert not found')

        const companyUserId = jobAdvert.companyUserId

        const params: SendNotificationParams = {
            jobAdId: jobAdvert.id,
            jobAdTitle: jobAdvert.title,
            applicantId: applicantId,
            applicantName: applicant?.firstName + ' ' + applicant?.lastName,
            type: type,
            companyUserId,
            createNotificationInput,
            createEmailNotificationInput,
        }

        console.log('We got into notifyJobActionUpdate params', {
            type: type,
            deletedFromSide: deletedFromSide,
        })

        if (type === ActionState.MATCHED) {
            //Message if job is matched
            const newMatchMsg = {
                title: `Match:  ${jobAdvert.title}`,
                body: `Du hast ein neues Match mit ${jobAdvert.company.name} - ${jobAdvert.title}`,
                actionUrl: `applicant/job-advert/${jobAdvert.id}`,
            }

            const matchDeletedMsg = {
                title: ` Du wurdest leider abgelehnt`,
                body: `${jobAdvert.company.name} hat deine Bewerbung nicht angenommen.`,
                actionUrl: `applicant/job-advert/${jobAdvert.id}`,
            }

            params.createPushNotificationInput = deletedFromSide
                ? matchDeletedMsg
                : newMatchMsg
            params.companyUserId = ''
            await this.notificationService.sendPushNotification(params)
        } else if (type === ActionState.LIKED) {
            if (deletedFromSide) return
            params.applicantId = ''
            console.log('We gonna send email now::')
            await this.notificationService.sendEmailNotification(params)
        }
    }
}
