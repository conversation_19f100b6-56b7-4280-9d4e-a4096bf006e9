import { Injectable } from '@nestjs/common'
import { ActionState } from '@prisma/client'
import { PusherPubSubService } from '../../pub-sub/pusher-pub-sub.service'
import { JobAdvertService } from '../../job-advert/job-advert.service'

@Injectable()
export class JobActionPublisherService {
    constructor(
        private readonly pusherService: PusherPubSubService,
        private readonly jobAdvertService: JobAdvertService
    ) {}

    async publishToClient(
        jobAction: any,
        state: ActionState,
        applicantId: string,
        jobAdvertId: string,
        companyId: string
    ) {
        if (
            state === ActionState.LIKED ||
            state === ActionState.MATCHED ||
            state === ActionState.DELETED
        ) {
            // await this.publishToLiveCompanies(companyId, jobAction)

            await this.publishToLiveJobAdverts(jobAdvertId, jobAction)
        }

        if (jobAction?.deletedFromApplicant && state !== ActionState.DELETED) {
            await this.publishToLiveJobAdverts(jobAdvertId, jobAction)
        }

        await this.publishToLiveApplicants(
            applicantId || jobAction.applicantId,
            jobAction,
            state
        )
    }

    async publishOnCreate(
        jobAction: any,
        applicantId: string,
        jobAdvertId: string,
        state: ActionState
    ): Promise<void> {
        await this.publishToLiveApplicants(
            applicantId,
            jobAction,
            jobAction.state
        )

        if (state === ActionState.LIKED || state == ActionState.BOOKMARKED) {
            await this.jobAdvertService.publishJobAdvertEvent(
                jobAction.jobAdvert
            )
            await this.publishToLiveJobAdverts(jobAdvertId, jobAction)
        }
    }

    async publishToLiveJobAdverts(jobAdvertId: string, jobAction: any) {
        const jobActionData = {
            id: jobAction?.id,
            status: jobAction?.status,
            state: jobAction?.state,
            deletedFromCompany: jobAction?.deletedFromCompany,
            deletedFromApplicant: jobAction?.deletedFromApplicant,
            applicantId: jobAction?.applicant?.id,
            name:
                jobAction?.applicant?.firstName +
                ' ' +
                jobAction?.applicant?.lastName,
            birthday: jobAction?.applicant?.birthDate,
            graduation: jobAction?.applicant?.graduation,
            city: jobAction?.applicant?.city,
            image: jobAction?.applicant?.profileImageUrl,
            applicant: jobAction?.applicant,
            title: jobAction?.jobAdvert?.title,
            approved: jobAction?.jobAdvert?.approved,
            jobAction: { id: jobAction?.id, state: jobAction?.state },
        }
        const channel = `liveJobAdvertActions.${jobAdvertId}`

        await this.pusherService.publish(
            `private-${channel}`,
            'jobActionUpdated',
            jobActionData
        )
    }

    // async publishToLiveCompanies(companyId: string, jobAction: any) {
    //     await this.pubSub
    //         .publish(`liveCompanyJobActions.${companyId}`, {
    //             jobAction,
    //             state: jobAction?.state,
    //         })
    //         .catch((err) => {
    //             console.log('pubSub error', err)
    //         })
    // }


    async publishToLiveApplicants(
        applicantId: string,
        jobAction: any,
        state: string
    ) {
        const slimJobAction = {
            id: jobAction.id,
            title: jobAction?.jobAdvert.title,
            logoImageUrl: jobAction?.jobAdvert?.company.logoImageUrl,
            name: jobAction?.jobAdvert.company.name,
            deletedFromCompany: jobAction.deletedFromCompany,
            deletedFromApplicant: jobAction.deletedFromApplicant,
            applicantIsNew: jobAction.applicantIsNew,
            jobAdvertId: jobAction?.jobAdvert?.id,
            state: jobAction.state,
            declineReason: jobAction.declineReason,
            chatRoomId: jobAction.chatRoomId || jobAction?.chatRoom?.id,
        }

        try {
            const channel = `liveApplicantJobActions.${applicantId}`

            await this.pusherService.publish(
                `private-${channel}`,
                'jobActionUpdated',
                slimJobAction
            )
        } catch (err) {
            console.log('pubSub error', err)
        } finally {
            console.log('pubSub finally')
        }
    }
}
