import { Test, TestingModule } from '@nestjs/testing'
import { JobActionPublisherService } from './job-action-publisher.service'

describe('JobActionPublishService', () => {
    let service: JobActionPublisherService

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [JobActionPublisherService],
        }).compile()

        service = module.get<JobActionPublisherService>(
            JobActionPublisherService
        )
    })

    it('should be defined', () => {
        expect(service).toBeDefined()
    })
})
