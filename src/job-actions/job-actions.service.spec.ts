import { Test, TestingModule } from '@nestjs/testing'
import { JobActionsService } from './job-actions.service'

describe('JobActionsService', () => {
    let service: JobActionsService

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [JobActionsService],
        }).compile()

        service = module.get<JobActionsService>(JobActionsService)
    })

    it('should be defined', () => {
        expect(service).toBeDefined()
    })
})
