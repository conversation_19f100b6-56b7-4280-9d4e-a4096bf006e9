import {
    Injectable,
    NotFoundException,
    UnauthorizedException,
} from '@nestjs/common'
import { NotificationService } from '../../notification/notification.service'
import { ChatRoomService } from '../../chat-room/chat-room.service'
import { CreateJobActionInput } from '../dto/create-job-action.input'
import { PrismaService } from '../../prisma.service'
import { ActionState } from '@prisma/client'
import { UpdateJobActionInput } from '../dto/update-job-action.input'

@Injectable()
export class JobActionValidatorService {
    constructor(
        private readonly notificationService: NotificationService,
        private readonly chatRoomService: ChatRoomService,
        private readonly prisma: PrismaService
    ) {}

    private assertNotDeleted(state: ActionState) {
        if (state === ActionState.DELETED) {
            throw new NotFoundException('Already deleted')
        }
    }

    private assertCanMatch(state: ActionState, companyUserId?: string) {
        if (state === ActionState.MATCHED && !companyUserId) {
            throw new UnauthorizedException('Not authorized to match job')
        }
    }

    async validateBeforeCreateJobAction(
        createJobActionInput: CreateJobActionInput,
        applicantId: string,
        jobAdvertId: string
    ) {
        if (!createJobActionInput.status) {
            createJobActionInput.status = createJobActionInput.state
        }
        //Check if applicant already exists
        const existingApplicant = await this.prisma.applicant.findUnique({
            where: {
                id: applicantId,
            },
        })

        if (!existingApplicant) {
            throw new NotFoundException('Applicant does not exist.')
        }

        //check if job advert isDeleted is false and approved is true
        const jobAdvert = await this.prisma.jobAdvert.findUnique({
            where: {
                id: jobAdvertId,
            },
            include: {
                jobAction: true,
            },
        })

        if (
            !jobAdvert ||
            jobAdvert.isDeleted ||
            !jobAdvert.approved ||
            jobAdvert.isDeclined
        ) {
            throw new NotFoundException('JobAdvert not found or not approved.')
        }
    }

    //     async validateBeforeUpdateJobAction(updateJobActionInput: UpdateJobActionInput, currentState: ActionState,companyUserId: string, processUpdate: any) {
    //         //Check if job action already exists
    //         if (!currentState) {
    //             try {
    //                 const jobAction = await this.prisma.jobAction.findFirst({
    //                     where: { id: updateJobActionInput.id },
    //                     include: {
    //                         applicant: true,
    //                         jobAdvert: {
    //                             include: {
    //                                 company: true,
    //                                 jobAction: true,
    //                             },
    //                         },
    //                     },
    //                 })
    //                 currentState = jobAction?.state
    //             } catch (error) {
    //                 throw new NotFoundException(
    //                     `JobAction with id: ${updateJobActionInput.id} not found`
    //                 )
    //             }
    //         }
    //
    //         this.assertNotDeleted(currentState)
    //         this.assertCanMatch(updateJobActionInput.state, companyUserId)
    //
    //         const processUpdate = await this.processActionUpdate({
    //             newState: updateJobActionInput.state || currentState,
    //             currentState,
    //             isCompany: !!companyUserId,
    //         })
    //         //Create Chat room if action is matched
    //         if (
    //             processUpdate.canUpdate &&
    //             updateJobActionInput.state === ActionState.MATCHED
    //         ) {
    //             //Check if chat room already exists
    //             const chatRoomExists = await this.prisma.chatRoom.findFirst({
    //                 where: {
    //                     jobAction: {
    //                         id: updateJobActionInput.id,
    //                     },
    //                 },
    //             })
    //             if (!chatRoomExists) {
    //                 chatRoom = await this.chatRoomService.create(
    //                     updateJobActionInput?.id,
    //                     companyUserId,
    //                     {
    //                         status: 'Active',
    //                     }
    //                 )
    //             }
    //         }
    //
    //         const { applicantIsNew } = processUpdate
    // }
}
