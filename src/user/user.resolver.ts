import { Args, Context, Mutation, Query, Resolver } from '@nestjs/graphql'
import { AuthService } from '../auth/auth.service'
import { LoginRequestDto, RegisterDto } from '../auth/authTypes/input-dto'
import {
    ClaimsResponse,
    LoginResponse,
    RegisterResponse,
} from '../auth/authTypes/response-types'
import { UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@Resolver()
export class UserResolver {
    constructor(private readonly authService: AuthService) {}
    @Query(() => String)
    about(): string {
        return 'Hi am the new bridge api2'
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => LoginResponse)
    async login(
        @Args('loginInput') loginRequestDto: LoginRequestDto,
        @Context() context: any
    ) {
        const token = context?.req?.user?.token
        return this.authService.login(token ? { token } : loginRequestDto)
    }

    @Mutation(() => RegisterResponse)
    async register(
        @Context() context: any,
        @Args('registerInput', { nullable: true }) registerDto: RegisterDto
    ) {
        const firebaseUid = registerDto.firebaseUid || context?.req?.user?.uid
        return this.authService.registerViaFirebase({ firebaseUid })
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => RegisterResponse)
    async registerApplicant(@Context() context: any) {
        const firebaseUid = context?.req?.user?.uid
        return this.authService.registerApplicant({ firebaseUid })
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => ClaimsResponse)
    async setCustomUserClaims(@Context() context: any) {
        const firebaseUid = context?.req?.user?.uid
        return this.authService.setUserClaims({ firebaseUid })
    }
}
