import { InputType, Field } from '@nestjs/graphql'
import { IsE<PERSON>, IsOptional, IsString } from 'class-validator'

@InputType()
export class WelcomeEmailInput {
    @IsString()
    @Field()
    email: string

    @IsString()
    @Field()
    name: string

    @IsString()
    @Field({ nullable: true })
    verificationLink: string
}

@InputType()
export class ResetPasswordInput {
    @IsString()
    @IsEmail()
    @Field()
    email: string

    @IsString()
    @Field()
    resetLink: string
}

@InputType()
export class NewCompanyUserInput {
    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    name: string

    @IsString()
    @IsEmail()
    @Field()
    email: string

    @IsString()
    @Field()
    resetLink: string
}
