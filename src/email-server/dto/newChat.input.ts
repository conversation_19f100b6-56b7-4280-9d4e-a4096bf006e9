import { InputType, Field } from '@nestjs/graphql'
import { IsEmail, IsObject, IsOptional, IsString } from 'class-validator'

@InputType()
export class NewChatInput {
    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    jobAdTitle: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    applicantName: string

    @IsEmail()
    @Field()
    email: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    message: string

    @IsObject()
    @IsOptional()
    @Field({ nullable: true })
    timeslot: any
}
