import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional } from 'class-validator'

@InputType()
export class NewAppointmentInput {
    @IsString()
    @Field()
    applicantName: string

    @IsString()
    @Field()
    companyName: string

    @IsString()
    @Field()
    fairName: string

    @IsString()
    @Field()
    contactPersonName: string

    @IsString()
    @Field()
    appointmentDate: string

    @IsString()
    @Field()
    appointmentStatus: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    appointmentId?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    applicantId?: string
}
