import { Resolver, Query, Args } from '@nestjs/graphql'
import { EmailServerService } from './email-server.service'
import { EmailServer } from './entities/email-server.entity'
import {
    ResetPasswordInput,
    WelcomeEmailInput,
} from './dto/welcome-email.input'
import { NewLikeInput } from './dto/newLike.input'

@Resolver(() => EmailServer)
export class EmailServerResolver {
    constructor(private readonly emailServerService: EmailServerService) {}

    @Query(() => String, { name: 'sendRestPasswordEmail' })
    sendResetPasswordEmail(
        @Args('data', { type: () => ResetPasswordInput })
        data: ResetPasswordInput
    ) {
        return this.emailServerService.sendResetPasswordEmail(data)
    }
    @Query(() => String, { name: 'sendNewLikeEmail' })
    sendNewLikeEmail(
        @Args('data', { type: () => NewLikeInput })
        data: NewLikeInput
    ) {
        return this.emailServerService.sendNewLikeEmail(data)
    }
    @Query(() => String, { name: 'sendWelcomeEmail' })
    sendWelcomeEmail(
        @Args('data', { type: () => WelcomeEmailInput })
        data: WelcomeEmailInput
    ) {
        return this.emailServerService.sendWelcomeEmail(data)
    }
}
