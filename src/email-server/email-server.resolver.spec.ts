import { Test, TestingModule } from '@nestjs/testing'
import { EmailServerResolver } from './email-server.resolver'
import { EmailServerService } from './email-server.service'

describe('EmailServerResolver', () => {
    let resolver: EmailServerResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [EmailServerResolver, EmailServerService],
        }).compile()

        resolver = module.get<EmailServerResolver>(EmailServerResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
