<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    <title>Neuer Termin erstellt</title>
    <style type="text/css">
        body, table, td {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        }
        .ExternalClass { width: 100%; }
        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
            line-height: 150%;
        }
        a { text-decoration: none; }
        * { color: inherit; }
        a[x-apple-data-detectors], u+#body a, #MessageViewBody a {
            color: inherit;
            text-decoration: none;
            font-size: inherit;
            font-family: inherit;
            font-weight: inherit;
            line-height: inherit;
        }
        img { -ms-interpolation-mode: bicubic; }
        table:not([class^=s-]) {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            border-spacing: 0px;
            border-collapse: collapse;
        }
        table:not([class^=s-]) td {
            border-spacing: 0px;
            border-collapse: collapse;
        }

        /* Custom styles for better appearance */
        .main-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e2e8f0;
        }

        .header-section {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            border-radius: 12px 12px 0 0;
            padding: 30px 40px 20px 40px;
        }

        .content-section {
            padding: 30px 40px;
        }

        .appointment-details {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }

        .detail-row {
            display: table;
            width: 100%;
            margin-bottom: 12px;
            border-bottom: 1px solid #e2e8f0;
            padding-bottom: 12px;
        }

        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .detail-label {
            display: table-cell;
            width: 140px;
            font-weight: 600;
            color: #4a5568;
            vertical-align: top;
            padding-right: 15px;
        }

        .detail-value {
            display: table-cell;
            color: #1a202c;
            font-weight: 500;
            vertical-align: top;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-confirmed {
            background: #d1fae5;
            color: #065f46;
        }

        .status-rejected {
            background: #fee2e2;
            color: #991b1b;
        }

        .cta-button {
            background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);
            color: white !important;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(227, 38, 82, 0.4);
        }

        .calendar-icon {
            background: #e32652;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .info-highlight {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 4px solid #0ea5e9;
            border-radius: 0 8px 8px 0;
            padding: 16px 20px;
            margin: 20px 0;
        }

        @media screen and (max-width: 600px) {
            .header-section, .content-section {
                padding: 20px !important;
            }
            .appointment-details {
                margin: 15px 0;
                padding: 20px;
            }
            .detail-row {
                display: block;
                margin-bottom: 15px;
            }
            .detail-label {
                display: block;
                width: 100%;
                margin-bottom: 5px;
            }
            .detail-value {
                display: block;
                width: 100%;
            }
            .cta-button {
                padding: 14px 24px;
                font-size: 15px;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f1f5f9; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
<table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f1f5f9;">
    <tr>
        <td align="center" style="padding: 40px 20px;">

            <!-- Main Email Container -->
            <table class="main-card" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto;">

                <!-- Header Section -->
                <tr>
                    <td class="header-section" align="center">
                        <div class="calendar-icon">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19ZM7 10H12V15H7V10Z" fill="white"/>
                            </svg>
                        </div>
                        <h1 style="color: white; font-size: 28px; font-weight: 700; margin: 0; line-height: 1.3;">
                            Neuer Termin vereinbart! 📅
                        </h1>
                        <p style="color: rgba(255, 255, 255, 0.9); font-size: 16px; margin: 10px 0 0 0; line-height: 1.5;">
                            Ein wichtiger Termin wurde für Sie erstellt
                        </p>
                    </td>
                </tr>

                <!-- Content Section -->
                <tr>
                    <td class="content-section">

                        <!-- Intro Message -->
                        <div class="info-highlight">
                            <p style="color: #0c4a6e; font-size: 16px; margin: 0; line-height: 1.5;">
                                <strong>Großartig!</strong> Ein neuer Termin wurde erfolgreich in Ihrem System erstellt.
                                Alle relevanten Details finden Sie unten aufgelistet.
                            </p>
                        </div>

                        <!-- Appointment Details -->
                        <div class="appointment-details">
                            <h3 style="color: #1a202c; font-size: 18px; font-weight: 600; margin: 0 0 20px 0; display: flex; align-items: center;">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" style="margin-right: 8px;">
                                    <path d="M14 2H6C4.9 2 4.01 2.9 4.01 4L4 20C4 21.1 4.89 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z" fill="#e32652"/>
                                </svg>
                                Termindetails
                            </h3>

                            <div class="detail-row">
                                <span class="detail-label">👤 Bewerber:</span>
                                <span class="detail-value">{{applicantName}}</span>
                            </div>

                            <div class="detail-row">
                                <span class="detail-label">🏢 Unternehmen:</span>
                                <span class="detail-value">{{companyName}}</span>
                            </div>

                            <div class="detail-row">
                                <span class="detail-label">🎪 Messe:</span>
                                <span class="detail-value">{{fairName}}</span>
                            </div>

                            <div class="detail-row">
                                <span class="detail-label">👥 Kontaktperson:</span>
                                <span class="detail-value">{{contactPersonName}}</span>
                            </div>

                            <div class="detail-row">
                                <span class="detail-label">📅 Termin:</span>
                                <span class="detail-value">{{appointmentDate}}</span>
                            </div>

                            <div class="detail-row">
                                <span class="detail-label">📊 Status:</span>
                                <span class="detail-value">
                      <span class="status-badge status-pending">{{appointmentStatus}}</span>
                    </span>
                            </div>
                        </div>

                        <!-- Next Steps -->

                        <!-- Call to Action -->
                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 35px 0 25px 0;">
                            <tr>
                                <td align="center">
                                    <a href="{{appointmentLink}}" class="cta-button" style="background: linear-gradient(135deg, #e32652 0%, #c41e3a 100%); color: white !important; padding: 16px 32px; border-radius: 8px; font-weight: 600; font-size: 16px; text-decoration: none; display: inline-block; box-shadow: 0 4px 12px rgba(227, 38, 82, 0.3);">
                                        🔍 Termin im Dashboard ansehen
                                    </a>
                                </td>
                            </tr>
                        </table>

                        <!-- Additional Information -->
                        <div style="background: #fffbeb; border: 1px solid #fbbf24; border-radius: 8px; padding: 16px; margin: 25px 0;">
                            <p style="color: #92400e; font-size: 14px; margin: 0; line-height: 1.5;">
                                <strong>⚠️ Wichtiger Hinweis:</strong> Diese E-Mail wurde automatisch generiert.
                                Bei Änderungen oder Fragen wenden Sie sich bitte direkt über die Bridge-App an den Bewerber.
                            </p>
                        </div>

                    </td>
                </tr>
            </table>

            <!-- Footer -->
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin-top: 40px;">
                <tr>
                    <td align="center">
                        <img src="https://firebasestorage.googleapis.com/v0/b/bridge-public-v2/o/dist%2Fimages%2Fbridge-logo-fae1080e.png?alt=media&token=a0a04c7d-33b4-44bb-b97a-fd1a96a493e3"
                             alt="Bridge Logo"
                             width="120"
                             style="display: block; margin-bottom: 20px; opacity: 0.8;">
                    </td>
                </tr>
                <tr>
                    <td align="center">
                        <p style="color: #718096; font-size: 14px; line-height: 1.5; margin: 0;">
                            Diese E-Mail wurde automatisch von der Bridge App gesendet.<br><br>
                            <strong>Berufe4you UG</strong><br>
                            Logistikpark 7<br>
                            95448 Bayreuth<br><br>
                            <a href="mailto:<EMAIL>" style="color: #e32652; text-decoration: none;"><EMAIL></a>
                        </p>
                    </td>
                </tr>
            </table>

        </td>
    </tr>
</table>
</body>
</html>