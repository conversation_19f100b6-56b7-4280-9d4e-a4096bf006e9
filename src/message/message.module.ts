import { Modu<PERSON> } from '@nestjs/common'
import { MessageService } from './message.service'
import { MessageResolver } from './message.resolver'
import { AuthModule } from '../auth/auth.module'
import { NotificationModule } from '../notification/notification.module'
import { ChatRoomModule } from '../chat-room/chat-room.module'
import { PusherPubSubModule } from '../pub-sub/pusher-pub-sub.module'
import { JobActionsModule } from '../job-actions/job-actions.module'
import { AppointmentModule } from '../appointment/appointment.module'
import { AppointmentPublisherService } from '../appointment/appointment-publisher/appointment-publisher.service'

@Module({
    imports: [
        AuthModule,
        NotificationModule,
        ChatRoomModule,
        PusherPubSubModule,
        JobActionsModule,
        AppointmentModule,
    ],
    exports: [MessageService],
    providers: [MessageResolver, MessageService],
})
export class MessageModule {}
