import { InputType, Field } from '@nestjs/graphql'
import { DeletedBy } from '@prisma/client'
import {
    IsBoolean,
    IsDate,
    IsEnum,
    IsOptional,
    IsString,
} from 'class-validator'

@InputType()
export class CreateMessageInput {
    @IsString()
    @Field()
    content: string

    @IsString()
    @IsOptional()
    @Field()
    authorName: string

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isCompany: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true, defaultValue: false })
    isApplicant: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isSent: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isDelivered: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isSeen: boolean

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    isDeleted: boolean

    @IsDate()
    @IsOptional()
    @Field({ nullable: true })
    deletedAt: Date

    @IsEnum(DeletedBy)
    @IsOptional()
    @Field({ nullable: true })
    deletedBy: DeletedBy

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    deletedById: string
}
