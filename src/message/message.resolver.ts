import { Args, Context, Mutation, Query, Resolver } from '@nestjs/graphql'
import { MessageService } from './message.service'
import { CreateMessageInput } from './dto/create-message.input'
import { UpdateMessageInput } from './dto/update-message.input'
import { MarkMessagesAsSeenInput } from './dto/mark-messages-as-seen.input'
import { UnauthorizedException, UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { SuperGuard } from '../guards/super-guard'
import { Message } from './entities/message.entity'
import { MarkMessagesAsSeenResponse } from './entities/mark-messages-as-seen-response.entity'
import { ChatRoomService } from '../chat-room/chat-room.service'

@Resolver(() => Message)
export class MessageResolver {
    constructor(
        private readonly messageService: MessageService,
        private readonly chatRoomService: ChatRoomService
    ) { }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Message)
    async createMessage(
        @Context() context: any,
        @Args('chatRoomId', { type: () => String }) chatRoomId: string,
        @Args('createMessageInput') createMessageInput: CreateMessageInput,
        @Args('companyUserId', { type: () => String, nullable: true })
        companyUserId: string,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string,
        @Args('companyFairContactPersonId', {
            type: () => String,
            nullable: true,
        })
        companyFairContactPersonId?: string
    ) {
        const isSuper =
            context?.req?.user?.isSuperUser || context?.req?.user?.isFGAdmin
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        const bCompanyUserId =
            context?.req?.user?.companyUserId || companyUserId

        const hasAccess = await this.chatRoomService.userHasAccessToChatRoom(
            isSuper,
            chatRoomId,
            bApplicantId,
            bCompanyUserId
        )

        if (!hasAccess) {
            return new UnauthorizedException(
                'User does not have access to this chat room'
            )
        }

        const contactPerson =
            await this.chatRoomService.findContactPersonInChatRoom(chatRoomId)
        const companyFCPersonId =
            contactPerson.appointment?.contactPersonTimeslot
                ?.companyFairContactPersonId || companyFairContactPersonId

        return await this.messageService.create(
            chatRoomId,
            createMessageInput,
            bCompanyUserId,
            bApplicantId,
            companyFCPersonId
        )
    }

    @UseGuards(SuperGuard)
    @Query(() => [Message], { name: 'allChatMessages' })
    findAll() {
        return this.messageService.findAll()
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => Message, { name: 'chatMessageById' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.messageService.findOne(id)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Message)
    updateMessage(
        @Args('id', { type: () => String }) id: string,
        @Args('chatRoomId', { type: () => String }) chatRoomId: string,
        @Args('companyUserId', { type: () => String }) companyUserId: string,
        @Args('applicantId', { type: () => String }) applicantId: string,
        @Args('updateMessageInput') updateMessageInput: UpdateMessageInput
    ) {
        return this.messageService.update(
            id,
            chatRoomId,
            companyUserId,
            applicantId,
            updateMessageInput
        )
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => MarkMessagesAsSeenResponse)
    async markMessagesAsSeen(
        @Context() context: any,
        @Args('markMessagesAsSeenInput') markMessagesAsSeenInput: MarkMessagesAsSeenInput
    ) {
        const { chatRoomId, applicantId } = markMessagesAsSeenInput

        const isSuper = context?.req?.user?.isSuperUser || context?.req?.user?.isFGAdmin
        const companyUserId = context?.req?.user?.companyUserId

        // Check if user has access to the chat room
        const hasAccess = await this.chatRoomService.userHasAccessToChatRoom(
            isSuper,
            chatRoomId,
            applicantId,
            companyUserId
        )

        if (!hasAccess) {
            throw new UnauthorizedException(
                'User does not have access to this chat room'
            )
        }

        return this.messageService.markMessagesAsSeen(chatRoomId, applicantId)
    }
}
