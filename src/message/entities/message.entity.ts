import { ObjectType, Field } from '@nestjs/graphql'
import { DeletedBy } from '@prisma/client'

@ObjectType()
export class Message {
    @Field()
    id: string

    @Field()
    content: string

    @Field({ nullable: true })
    authorName: string

    @Field({ nullable: true })
    isCompany: boolean

    @Field({ nullable: true })
    isApplicant: boolean

    @Field({ nullable: true })
    isSent: boolean

    @Field({ nullable: true })
    isSeen: boolean

    @Field({ nullable: true })
    isDelivered: boolean

    @Field({ nullable: true })
    isDeleted: boolean

    @Field({ nullable: true })
    deletedAt?: Date

    @Field({ nullable: true })
    deletedBy: DeletedBy

    @Field({ nullable: true })
    deletedById: string

    @Field({ defaultValue: new Date() })
    createdAt: Date
}
