import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreatePartnerLinkInput } from './dto/create-partner-link.input'
import { UpdatePartnerLinkInput } from './dto/update-partner-link.input'
import { PartnerLink } from './entities/partner-link.entity'

@Injectable()
export class PartnerLinkService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        createPartnerLinkInput: CreatePartnerLinkInput
    ): Promise<PartnerLink> {
        return this.prisma.partnerLink.create({
            data: createPartnerLinkInput,
        })
    }

    findAll() {
        return this.prisma.partnerLink.findMany({
            include: {
                companyFairParticipations: true,
            },
        })
    }

    async update(
        id: string,
        updatePartnerLinkInput: UpdatePartnerLinkInput
    ): Promise<PartnerLink> {
        try {
            return this.prisma.partnerLink.update({
                where: { id },
                data: updatePartnerLinkInput,
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    async remove(id: string): Promise<PartnerLink> {
        try {
            return this.prisma.partnerLink.delete({
                where: { id },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }
}
