import { ObjectType, Field } from '@nestjs/graphql'
import { CompanyFairParticipation } from '../../company-fair-participation/entities/company-fair-participation.entity'

@ObjectType()
export class PartnerLink {
    @Field()
    id: string

    @Field()
    name: string

    @Field()
    url: string

    @Field(() => [CompanyFairParticipation], { nullable: true })
    companyFairParticipations?: CompanyFairParticipation[]

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date
}
