import { Test, TestingModule } from '@nestjs/testing'
import { PartnerLinkResolver } from './partner-link.resolver'
import { PartnerLinkService } from './partner-link.service'

describe('PartnerLinkResolver', () => {
    let resolver: PartnerLinkResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [PartnerLinkResolver, PartnerLinkService],
        }).compile()

        resolver = module.get<PartnerLinkResolver>(PartnerLinkResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
