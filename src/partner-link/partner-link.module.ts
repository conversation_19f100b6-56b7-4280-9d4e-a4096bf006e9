import { Module } from '@nestjs/common'
import { PartnerLinkService } from './partner-link.service'
import { PartnerLinkResolver } from './partner-link.resolver'
import { PrismaModule } from '../prisma.module'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [PrismaModule, AuthModule],
    providers: [PartnerLinkResolver, PartnerLinkService],
    exports: [PartnerLinkService],
})
export class PartnerLinkModule {}
