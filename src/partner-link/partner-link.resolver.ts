import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { PartnerLinkService } from './partner-link.service'
import { PartnerLink } from './entities/partner-link.entity'
import { CreatePartnerLinkInput } from './dto/create-partner-link.input'
import { UpdatePartnerLinkInput } from './dto/update-partner-link.input'
import { SuperGuard } from '../guards/super-guard'
import { UseGuards } from '@nestjs/common'

@Resolver(() => PartnerLink)
export class PartnerLinkResolver {
    constructor(private readonly partnerLinkService: PartnerLinkService) {}

    @UseGuards(SuperGuard)
    @Mutation(() => PartnerLink)
    createPartnerLink(
        @Args('createPartnerLinkInput')
        createPartnerLinkInput: CreatePartnerLinkInput
    ) {
        return this.partnerLinkService.create(createPartnerLinkInput)
    }

    @Query(() => [PartnerLink], { name: 'partnerLinks' })
    findAll() {
        return this.partnerLinkService.findAll()
    }

    @UseGuards(SuperGuard)
    @Mutation(() => PartnerLink)
    updatePartnerLink(
        @Args('updatePartnerLinkInput')
        updatePartnerLinkInput: UpdatePartnerLinkInput
    ) {
        return this.partnerLinkService.update(
            updatePartnerLinkInput.id,
            updatePartnerLinkInput
        )
    }

    @UseGuards(SuperGuard)
    @Mutation(() => PartnerLink)
    removePartnerLink(@Args('id', { type: () => String }) id: string) {
        return this.partnerLinkService.remove(id)
    }
}
