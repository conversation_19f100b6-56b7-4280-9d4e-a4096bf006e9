import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { DeviceTokenService } from './device-token.service'
import { DeviceToken } from './entities/device-token.entity'
import { CreateDeviceTokenInput } from './dto/create-device-token.input'
import { UpdateDeviceTokenInput } from './dto/update-device-token.input'
import { UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { ApplicantGuard } from '../guards/applicant-guard'
import { SuperGuard } from '../guards/super-guard'

@UseGuards(GraphqlAuthGuard)
@Resolver(() => DeviceToken)
export class DeviceTokenResolver {
    constructor(private readonly deviceTokenService: DeviceTokenService) {}

    @UseGuards(ApplicantGuard)
    @Mutation(() => DeviceToken)
    createDeviceToken(
        @Args('createDeviceTokenInput')
        createDeviceTokenInput: CreateDeviceTokenInput,
        @Args('applicantId', { type: () => String }) applicantId: string
    ) {
        return this.deviceTokenService.create(
            createDeviceTokenInput,
            applicantId
        )
    }

    @UseGuards(ApplicantGuard)
    @Mutation(() => DeviceToken)
    setDeviceToken(
        @Args('setDeviceTokenInput')
        createDeviceTokenInput: CreateDeviceTokenInput,
        @Context() context: any,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId

        return this.deviceTokenService.createOrUpdate(
            createDeviceTokenInput,
            bApplicantId
        )
    }

    @UseGuards(SuperGuard)
    @Query(() => [DeviceToken], { name: 'allDeviceToken' })
    findAll() {
        return this.deviceTokenService.findAll()
    }

    @UseGuards(ApplicantGuard)
    @Query(() => DeviceToken, { name: 'deviceTokenById' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.deviceTokenService.findOne(id)
    }

    @UseGuards(ApplicantGuard)
    @Mutation(() => DeviceToken)
    updateDeviceToken(
        @Args('updateDeviceTokenInput')
        updateDeviceTokenInput: UpdateDeviceTokenInput
    ) {
        return this.deviceTokenService.update(updateDeviceTokenInput)
    }

    @UseGuards(ApplicantGuard)
    @Mutation(() => DeviceToken, { name: 'removeDeviceTokensByApplicantId' })
    removeAllDeviceToken(
        @Args('applicantId', { nullable: true }) applicantId: string
    ) {
        return this.deviceTokenService.removeByApplicantId(applicantId)
    }

    @UseGuards(ApplicantGuard)
    @Mutation(() => DeviceToken)
    removeDeviceToken(@Args('token', { nullable: true }) token: string) {
        return this.deviceTokenService.remove(token)
    }
}
