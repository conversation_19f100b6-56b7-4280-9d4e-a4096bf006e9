import { Test, TestingModule } from '@nestjs/testing'
import { DeviceTokenResolver } from './device-token.resolver'
import { DeviceTokenService } from './device-token.service'

describe('DeviceTokenResolver', () => {
    let resolver: DeviceTokenResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [DeviceTokenResolver, DeviceTokenService],
        }).compile()

        resolver = module.get<DeviceTokenResolver>(DeviceTokenResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
