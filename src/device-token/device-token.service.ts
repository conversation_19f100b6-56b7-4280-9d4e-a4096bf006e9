import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateDeviceTokenInput } from './dto/create-device-token.input'
import { UpdateDeviceTokenInput } from './dto/update-device-token.input'
import { PrismaService } from '../prisma.service'

@Injectable()
export class DeviceTokenService {
    constructor(private readonly prisma: PrismaService) {}
    async create(
        createDeviceTokenInput: CreateDeviceTokenInput,
        applicantId: string
    ) {
        const checkExistingToken = await this.prisma.deviceToken.findUnique({
            where: {
                token: createDeviceTokenInput.token,
            },
        })

        if (checkExistingToken) return checkExistingToken

        return this.prisma.deviceToken.create({
            data: {
                ...createDeviceTokenInput,
                applicant: {
                    connect: {
                        id: applicantId,
                    },
                },
            },
            include: {
                applicant: true,
            },
        })
    }
    async createOrUpdate(
        createDeviceTokenInput: CreateDeviceTokenInput,
        applicantId?: string
    ) {
        const tokenExists = await this.prisma.deviceToken.findUnique({
            where: {
                token: createDeviceTokenInput.token,
            },
        })

        if (tokenExists) {
            return this.update({ ...createDeviceTokenInput })
        } else {
            return this.create(createDeviceTokenInput, applicantId)
        }
    }

    async findAll() {
        return this.prisma.deviceToken.findMany({
            include: {
                applicant: true,
            },
        })
    }

    findOne(id: string) {
        try {
            return this.prisma.deviceToken.findUnique({
                where: {
                    id: id,
                },
                include: { applicant: true },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    async update(
        updateDeviceTokenInput: UpdateDeviceTokenInput,
        id?: string | null | undefined
    ) {
        const whereClause = id
            ? { id: id }
            : { token: updateDeviceTokenInput.token }

        const updateResult = await this.prisma.deviceToken.update({
            where: whereClause,
            data: updateDeviceTokenInput,
        })

        return updateResult
    }

    async remove(token?: string) {
        const tokenToDelete = await this.prisma.deviceToken.findFirst({
            where: {
                token: token,
            },
        })

        if (!tokenToDelete) {
            throw new NotFoundException(`Device Token does not exist`)
        }
        return this.prisma.deviceToken.delete({
            where: { id: tokenToDelete?.id },
            include: {
                applicant: true,
            },
        })
    }

    async removeByApplicantId(applicantId?: string) {
        return this.prisma.deviceToken.deleteMany({
            where: { applicantId: applicantId },
        })
    }
}
