import { Injectable } from '@nestjs/common'
import { PrismaService } from '../prisma.service'

@Injectable()
export class SubscriptionConfigService {
    constructor(private readonly prisma: PrismaService) {}

    async createDefaultConfig(companyId: string, createdById?: string) {
        try {
            const defaultPlan = await this.prisma.pricingPlan.findFirst({
                where: {
                    isPopular: true,
                    isActive: true,
                },
            })

            return await this.prisma.companySubscriptionConfig.create({
                data: {
                    companyId,
                    subscriptionType: 'COMPANY_UNLIMITED',
                    createdBy: createdById,
                    ...(defaultPlan && {
                        availablePricingPlans: {
                            connect: {
                                id: defaultPlan.id,
                            },
                        },
                    }),
                },
                include: {
                    availablePricingPlans: true,
                },
            })
        } catch (error) {
            console.error('Error creating default subscription config:', error)
            throw error
        }
    }
}
