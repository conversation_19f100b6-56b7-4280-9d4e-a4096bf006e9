import { Test, TestingModule } from '@nestjs/testing';
import { FairResolver } from './fair.resolver';
import { FairService } from './fair.service';

describe('FairResolver', () => {
  let resolver: FairResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FairResolver, FairService],
    }).compile();

    resolver = module.get<FairResolver>(FairResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
