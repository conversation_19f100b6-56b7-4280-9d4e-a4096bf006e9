import { Injectable } from '@nestjs/common'

interface TimeRange {
    start: string
    end: string
}

interface FairDay {
    day: string
    startTime: string
    endTime: string
}

interface Timeslot {
    startTime: Date
    endTime: Date
    fairId: string
}

@Injectable()
export class TimeslotGeneratorService {
    private readonly TIMESLOT_DURATION_MS = 15 * 60 * 1000 //default 15 minutes
    private readonly BREAKS: TimeRange[] = [
        { start: '12:00', end: '13:00' }, // Lunch break
    ]

    public generateTimeslots(fairId: string, fairDays: FairDay[]): Timeslot[] {
        const allTimeslots: Timeslot[] = []

        fairDays.forEach((fairDay) => {
            const dayTimeslots = this.generateTimeslotsForDay(fairId, fairDay)
            allTimeslots.push(...dayTimeslots)
        })

        return allTimeslots
    }

    private generateTimeslotsForDay(
        fairId: string,
        fairDay: FairDay
    ): Timeslot[] {
        const timeslots: Timeslot[] = []

        const baseDate = new Date(fairDay.day)
        baseDate.setHours(0, 0, 0, 0)

        const dayStart = this.parseTimeToDate(baseDate, fairDay.startTime)
        const dayEnd = this.parseTimeToDate(baseDate, fairDay.endTime)

        const dayBreaks = this.BREAKS.map((breakTime) => ({
            start: this.parseTimeToDate(baseDate, breakTime.start),
            end: this.parseTimeToDate(baseDate, breakTime.end),
        }))

        let currentTime = dayStart.getTime()

        while (currentTime < dayEnd.getTime()) {
            const slotStart = new Date(currentTime)
            const slotEnd = new Date(currentTime + this.TIMESLOT_DURATION_MS)

            if (!this.isInBreak(slotStart, dayBreaks)) {
                timeslots.push({
                    startTime: slotStart,
                    endTime: slotEnd,
                    fairId: fairId,
                })
            }

            currentTime += this.TIMESLOT_DURATION_MS
        }

        return timeslots
    }

    private isInBreak(
        time: Date,
        breaks: { start: Date; end: Date }[]
    ): boolean {
        return breaks.some(
            (breakPeriod) => time >= breakPeriod.start && time < breakPeriod.end
        )
    }

    private parseTimeToDate(baseDate: Date, time: string | Date): Date {
        if (time instanceof Date) {
            return time
        }
        const [hours, minutes] = time.split(':').map(Number)
        const date = new Date(baseDate)
        date.setHours(hours, minutes, 0, 0)
        return date
    }
}
