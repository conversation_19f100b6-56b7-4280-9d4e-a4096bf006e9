import { Field, ObjectType, Int } from '@nestjs/graphql'

@ObjectType('FairStats')
export class FairStats {
    @Field(() => Int, { nullable: false, defaultValue: 0 })
    fairs!: number

    @Field(() => Int, { nullable: false, defaultValue: 0 })
    companies!: number

    @Field(() => Int, { nullable: false, defaultValue: 0 })
    appointments!: number

    @Field(() => Int, { nullable: false, defaultValue: 0 })
    chatRooms!: number
}
