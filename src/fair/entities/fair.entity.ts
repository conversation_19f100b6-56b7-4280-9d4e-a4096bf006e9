import { ObjectType, Field, registerEnumType } from '@nestjs/graphql'
import { CompanyFairParticipation } from '../../company-fair-participation/entities/company-fair-participation.entity'
import { Timeslot } from '../../timeslot/entities/timeslot.entity'
import { FairStatus } from '@prisma/client'
import { FairDay } from '../../fair-day/entities/fair-day.entity'

registerEnumType(FairStatus, {
    name: 'FairStatus',
})

@ObjectType()
export class Fair {
    @Field()
    id: string

    @Field()
    name: string

    @Field()
    startDate: Date

    @Field()
    endDate: Date

    @Field()
    registrationStartDate: Date

    @Field()
    registrationEndDate: Date

    @Field()
    city: string

    @Field()
    location: string

    @Field({ nullable: true })
    locationName: string

    @Field(() => FairStatus, { nullable: true })
    status: FairStatus

    @Field({ nullable: true })
    description: string

    @Field({ nullable: true })
    logoImageUrl: string

    @Field({ nullable: true })
    contactPersonName: string

    @Field({ nullable: true })
    contactPersonEmail: string

    @Field({ nullable: true })
    publisherLogoImageUrl: string

    @Field({ nullable: true })
    publisherName: string

    @Field(() => [CompanyFairParticipation])
    companyFairParticipations: CompanyFairParticipation[]

    @Field(() => [Timeslot])
    standardTimeslots: Timeslot[]

    @Field(() => [FairDay], { nullable: true })
    fairDays: FairDay[]
}
