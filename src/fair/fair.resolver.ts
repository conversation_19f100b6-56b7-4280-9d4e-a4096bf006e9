import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { FairService } from './fair.service'
import { Fair } from './entities/fair.entity'
import { FairStats } from './entities/fair-stats.entity'
import { CreateFairInput } from './dto/create-fair.input'
import { UpdateFairInput } from './dto/update-fair.input'
import { UseGuards } from '@nestjs/common'
import { SuperGuard } from '../guards/super-guard'
import { CreateFairDayInput } from '../fair-day/dto/create-fair-day.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@UseGuards(GraphqlAuthGuard)
@Resolver(() => Fair)
export class FairResolver {
    constructor(private readonly fairService: FairService) {}

    @Mutation(() => Fair)
    createFair(
        @Args('createFairInput') createFairInput: CreateFairInput,
        @Args('fairDays', { type: () => [CreateFairDayInput] })
        fairDays: CreateFairDayInput[]
    ) {
        return this.fairService.create(createFairInput, fairDays)
    }

    @Mutation(() => Fair)
    cloneFair(
        @Args('id', { type: () => String }) id: string,
        @Args('cloneFairInput') cloneFairInput: CreateFairInput
    ) {
        return this.fairService.cloneFair(id, cloneFairInput)
    }

    @Query(() => [Fair], { name: 'allFairs' })
    findAll(@Context() context: any) {
        const applicantId = context?.req?.user?.applicantId
        const isApplicant = !!applicantId
        return this.fairService.findAll(isApplicant)
    }

    @Query(() => [Fair], { name: 'findFairsByCompanyId' })
    findByCompanyId(
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        return this.fairService.findByCompanyId(companyId)
    }

    @Query(() => Fair, { name: 'getFair' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.fairService.findOne(id)
    }

    @Mutation(() => Fair)
    updateFair(
        @Args('id', { type: () => String }) id: string,
        @Args('updateFairInput') updateFairInput: UpdateFairInput
    ) {
        return this.fairService.update(id, updateFairInput)
    }

    @Mutation(() => Fair)
    removeFair(@Args('id', { type: () => String }) id: string) {
        return this.fairService.remove(id)
    }

    @UseGuards(SuperGuard)
    @Query(() => FairStats, { name: 'fairStats' })
    getFairStats() {
        return this.fairService.getFairStats()
    }
}
