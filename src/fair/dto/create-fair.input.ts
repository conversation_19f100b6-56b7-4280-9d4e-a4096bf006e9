import { InputType, Field } from '@nestjs/graphql'
import {
  IsString,
  IsDate,
  IsOptional,
  IsEmail,
} from 'class-validator'
import { FairStatus } from '@prisma/client'
import { Type } from 'class-transformer'

@InputType()
export class CreateFairInput {
  @IsString()
  @Field()
  name: string

  @Type(() => Date)
  @IsDate()
  @Field()
  startDate: Date

  @Type(() => Date)
  @IsDate()
  @Field()
  endDate: Date

  @Type(() => Date)
  @IsDate()
  @Field()
  registrationStartDate: Date

  @Type(() => Date)
  @IsDate()
  @Field()
  registrationEndDate: Date

  @IsString()
  @Field()
  city: string

  @IsString()
  @Field()
  location: string

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  locationName?: string

  @Field({ nullable: true })
  status: FairStatus

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  description?: string

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  logoImageUrl?: string

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  contactPersonName?: string

  @IsEmail()
  @IsOptional()
  @Field({ nullable: true })
  contactPersonEmail?: string

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  publisherLogoImageUrl?: string

  @IsString()
  @IsOptional()
  @Field({ nullable: true })
  publisherName?: string
}