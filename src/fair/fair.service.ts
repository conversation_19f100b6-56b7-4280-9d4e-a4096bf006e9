import {
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from '@nestjs/common'
import { CreateFairInput } from './dto/create-fair.input'
import { UpdateFairInput } from './dto/update-fair.input'
import { PrismaService } from '../prisma.service'
import { Fair, FairStatus } from '@prisma/client'
import { CreateFairDayInput } from '../fair-day/dto/create-fair-day.input'
import { FairDayService } from '../fair-day/fair-day.service'
import { TimeslotGeneratorService } from './timeslot-generator/timeslot-generator.service'

@Injectable()
export class FairService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly fairDayService: FairDayService,
        private readonly timeslotGeneratorService: TimeslotGeneratorService
    ) {}

    private generateStandardTimeslots(fairId: string, fairDays: any) {
        return this.timeslotGeneratorService.generateTimeslots(fairId, fairDays)
    }

    async create(
        createFairInput: CreateFairInput,
        fairDays?: CreateFairDayInput[]
    ) {
        try {
            const newFair = await this.prisma.fair.create({
                data: {
                    ...createFairInput,
                    status: FairStatus.INACTIVE,
                },
            })

            if (!newFair) throw new NotFoundException('Fair not created')

            //map fairDays and create them
            if (fairDays) {
                fairDays.map(async (fairDay: CreateFairDayInput) => {
                    await this.fairDayService.create({
                        fairId: newFair.id,
                        day: new Date(fairDay.day),
                        startTime: new Date(fairDay.startTime),
                        endTime: new Date(fairDay.endTime),
                    })
                })
            }

            // Generate standard timeslots
            const standardTimeslots = this.generateStandardTimeslots(
                newFair.id,
                fairDays
            )

            // Save the timeslots in the database
            await this.prisma.timeslot.createMany({
                data: standardTimeslots,
            })

            return newFair
        } catch (error) {
            console.error('Error creating fair:', error)
            throw error
        }
    }

    async findAll(isApplicant: boolean) {
        try {
            const currentDate = new Date()

            return await this.prisma.fair.findMany({
                where: isApplicant
                    ? {
                          status: FairStatus.ACTIVE,
                      }
                    : undefined,
                orderBy: { createdAt: 'desc' },
            })
        } catch (error) {
            throw new InternalServerErrorException('Failed to fetch fairs')
        }
    }
    async findOne(id: string) {
        try {
            const fair = await this.prisma.fair.findUnique({
                where: { id },
                include: {
                    companyFairParticipations: {
                        include: {
                            categories: true,
                            company: true,
                            companyFairJobs: {
                                include: {
                                    fairJob: true,
                                },
                            },
                            companyFairContactPersons: {
                                include: {
                                    ContactPersonTimeslot: {
                                        include: {
                                            Appointment: {
                                                include: {
                                                    applicant: true,
                                                },
                                            },
                                        },
                                    },
                                    contactPerson: true,
                                },
                            },
                        },
                    },
                    standardTimeslots: true,
                },
            })

            if (!fair)
                throw new NotFoundException(`Fair with ID ${id} not found`)

            return fair
        } catch (error) {
            console.error('Error fetching fair:', error)
            throw error
        }
    }

    async update(id: string, updateFairInput: UpdateFairInput) {
        try {
            const fairExists = await this.prisma.fair.findUnique({
                where: { id },
            })

            if (!fairExists) {
                throw new NotFoundException(`Fair with ID ${id} not found`)
            }

            return await this.prisma.fair.update({
                where: { id },
                data: updateFairInput,
                include: {
                    companyFairParticipations: {
                        include: {
                            company: true,
                            companyFairJobs: true,
                            companyFairContactPersons: true,
                        },
                    },
                    standardTimeslots: true,
                },
            })
        } catch (error) {
            console.error('Error updating fair:', error)
            throw error
        }
    }

    async remove(id: string) {
        try {
            const fairToDelete = await this.prisma.fair.findUnique({
                where: { id },
            })

            if (!fairToDelete) {
                throw new NotFoundException(`Fair with ID ${id} not found`)
            }

            const deletedFair = await this.deleteFairAndRelatedData(id)
            const allOrphanedDataRemoved = await this.verifyNoOrphanedData(id)
            const deletedStats = await this.getDeletionStats(id)

            if (!allOrphanedDataRemoved) {
                throw new NotFoundException('Orphaned data found')
            }

            return deletedFair
        } catch (error) {
            console.error('Error removing fair:', error)
            throw error
        }
    }

    async verifyNoOrphanedData(fairId: string): Promise<boolean> {
        const orphanedData = await this.prisma.$transaction([
            this.prisma.timeslot.findMany({
                where: { fairId },
            }),
            this.prisma.companyFairParticipation.findMany({
                where: { fairId },
            }),
        ])

        return orphanedData.every((data) => data.length === 0)
    }

    async findByCompanyId(companyId: string) {
        return this.prisma.fair.findMany({
            where: {
                companyFairParticipations: {
                    some: {
                        companyId,
                    },
                },
            },
            include: {
                companyFairParticipations: {
                    include: {
                        categories: true,
                        company: true,
                        companyFairJobs: {
                            include: {
                                fairJob: true,
                            },
                        },
                        companyFairContactPersons: {
                            include: {
                                ContactPersonTimeslot: {
                                    include: {
                                        Appointment: {
                                            include: {
                                                applicant: true,
                                            },
                                        },
                                    },
                                },
                                contactPerson: true,
                            },
                        },
                    },
                },
                standardTimeslots: true,
            },
        })
    }

    async deleteFairAndRelatedData(fairId: string) {
        try {
            return await this.prisma.$transaction(
                async (prisma) => {
                    await prisma.appointment.deleteMany({
                        where: {
                            contactPersonTimeslot: {
                                companyFairContactPerson: {
                                    companyFairParticipation: {
                                        fairId,
                                    },
                                },
                            },
                        },
                    })

                    await prisma.contactPersonTimeslot.deleteMany({
                        where: {
                            companyFairContactPerson: {
                                companyFairParticipation: {
                                    fairId,
                                },
                            },
                        },
                    })

                    // Delete all company fair contact persons
                    await prisma.companyFairContactPerson.deleteMany({
                        where: {
                            companyFairParticipation: {
                                fairId,
                            },
                        },
                    })

                    // Delete all company fair jobs
                    await prisma.companyFairJob.deleteMany({
                        where: {
                            companyFairParticipation: {
                                fairId,
                            },
                        },
                    })

                    // Delete all company fair participations
                    await prisma.companyFairParticipation.deleteMany({
                        where: { fairId },
                    })

                    // Delete all standard timeslots
                    await prisma.timeslot.deleteMany({
                        where: { fairId },
                    })

                    // Finally delete the fair
                    return prisma.fair.delete({
                        where: { id: fairId },
                    })
                },
                {
                    timeout: 10000,
                }
            )
        } catch (error) {
            console.error('Deletion error:', error)
            throw new Error(`Failed to delete fair: ${error.message}`)
        }
    }

    async getDeletionStats(fairId: string) {
        const stats = await this.prisma.$transaction([
            this.prisma.appointment.count({
                where: {
                    contactPersonTimeslot: {
                        companyFairContactPerson: {
                            companyFairParticipation: {
                                fairId,
                            },
                        },
                    },
                },
            }),
            this.prisma.timeslot.count({
                where: { fairId },
            }),
            this.prisma.companyFairParticipation.count({
                where: { fairId },
            }),
            this.prisma.companyFairJob.count({
                where: {
                    companyFairParticipation: {
                        fairId,
                    },
                },
            }),
        ])

        return {
            appointments: stats[0],
            timeslots: stats[1],
            participations: stats[2],
            fairJobs: stats[3],
        }
    }

    async getFairStats() {
        try {
            const [
                fairsCount,
                companiesCount,
                appointmentsCount,
                chatRoomsCount,
            ] = await Promise.all([
                this.prisma.fair.count(),
                this.prisma.company.count(),
                this.prisma.appointment.count(),
                this.prisma.chatRoom.count(),
            ])

            return {
                fairs: fairsCount,
                companies: companiesCount,
                appointments: appointmentsCount,
                chatRooms: chatRoomsCount,
            }
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    // async cloneFair(id: string, newFairData: CreateFairInput): Promise<Fair> {
    //     const existingFair: any = await this.prisma.fair.findUnique({
    //         where: { id },
    //         include: {
    //             companyFairParticipations: {
    //                 include: {
    //                     categories: true,
    //                     companyFairJobs: {
    //                         include: {
    //                             fairJob: true,
    //                         },
    //                     },
    //                     companyFairContactPersons: {
    //                         include: {
    //                             contactPerson: true,
    //                             ContactPersonTimeslot: true,
    //                         },
    //                     },
    //                 },
    //             },
    //             fairDays: true,
    //             standardTimeslots: true,
    //         },
    //     })
    //
    //     if (!existingFair) {
    //         throw new NotFoundException('Fair not found')
    //     }
    //
    //     let newFair: Fair
    //     const newFairDays = existingFair.fairDays
    //     try {
    //         newFair = await this.create(newFairData, newFairDays)
    //     } catch (error) {
    //         console.error('Error creating new fair:', error)
    //         throw new InternalServerErrorException('Failed to create new fair')
    //     }
    //
    //     try {
    //         await this.prisma.$transaction(
    //             async (prisma) => {
    //                 if (existingFair?.standardTimeslots?.length > 0) {
    //                     const timeslotsData =
    //                         existingFair.standardTimeslots.map((timeslot) => ({
    //                             fairId: newFair.id,
    //                             startTime: new Date(timeslot.startTime),
    //                             endTime: new Date(timeslot.endTime),
    //                         }))
    //
    //                     await prisma.timeslot.createMany({
    //                         data: timeslotsData,
    //                     })
    //                 }
    //
    //                 for (const participation of existingFair.companyFairParticipations) {
    //                     const newParticipation =
    //                         await this.companyFairParticipationService.create({
    //                             companyId: participation.companyId,
    //                             fairId: newFair.id,
    //                             categoryIds: participation.categories.map(
    //                                 (category) => category.id
    //                             ),
    //                         })
    //
    //                     if (participation.companyFairJobs?.length > 0) {
    //                         for (const job of participation.companyFairJobs) {
    //                             await this.companyFairJobService.create({
    //                                 companyFairParticipationId:
    //                                     newParticipation.id,
    //                                 description: job.description,
    //                                 fairJobId: job.fairJobId,
    //                             })
    //                         }
    //                     }
    //
    //                     if (
    //                         participation.companyFairContactPersons?.length > 0
    //                     ) {
    //                         for (const contactPerson of participation.companyFairContactPersons) {
    //                             const newContactPerson =
    //                                 await this.companyFairContactPersonService.create(
    //                                     {
    //                                         companyFairParticipationId:
    //                                             newParticipation.id,
    //                                         contactPersonId:
    //                                             contactPerson.contactPersonId,
    //                                     }
    //                                 )
    //
    //                             if (
    //                                 contactPerson.ContactPersonTimeslot
    //                                     ?.length > 0
    //                             ) {
    //                                 for (const timeslot of contactPerson.ContactPersonTimeslot) {
    //                                     await this.contactPersonTimeslotService.create(
    //                                         {
    //                                             companyFairContactPersonId:
    //                                                 newContactPerson.id,
    //                                             startTime: timeslot.startTime,
    //                                             endTime: timeslot.endTime,
    //                                             available: timeslot.available,
    //                                         }
    //                                     )
    //                                 }
    //                             }
    //                         }
    //                     }
    //                 }
    //             },
    //             {
    //                 timeout: 30000,
    //             }
    //         )
    //     } catch (error) {
    //         console.error('Error during transaction:', error)
    //         throw new Error(
    //             `Failed to clone fair and related data: ${error.message}`
    //         )
    //     }
    //
    //     return newFair
    // }

    async cloneFair(id: string, newFairData: CreateFairInput): Promise<Fair> {
        // Retrieve the existing fair with all related data
        const existingFair: any = await this.prisma.fair.findUnique({
            where: { id },
            include: {
                companyFairParticipations: {
                    include: {
                        categories: true,
                        partnerLinks: true,
                        companyFairJobs: true,
                        companyFairContactPersons: {
                            include: {
                                ContactPersonTimeslot: true,
                            },
                        },
                    },
                },
                fairDays: true,
                standardTimeslots: true,
            },
        })

        if (!existingFair) {
            throw new NotFoundException('Fair not found')
        }

        // Create the new fair
        let newFair: Fair
        const newFairDays = existingFair.fairDays
        try {
            newFair = await this.create(newFairData, newFairDays)
        } catch (error) {
            console.error('Error creating new fair:', error)
            throw new InternalServerErrorException('Failed to create new fair')
        }

        // Transaction to clone related data
        try {
            await this.prisma.$transaction(
                async (prisma) => {
                    if (existingFair.standardTimeslots.length > 0) {
                        const timeslotsData =
                            existingFair.standardTimeslots.map((timeslot) => ({
                                fairId: newFair.id,
                                startTime: new Date(timeslot.startTime),
                                endTime: new Date(timeslot.endTime),
                            }))
                        await prisma.timeslot.createMany({
                            data: timeslotsData,
                        })
                    }

                    // Clone company participations with nested data
                    for (const participation of existingFair.companyFairParticipations) {
                        await prisma.companyFairParticipation.create({
                            data: {
                                companyId: participation.companyId,
                                fairId: newFair.id,
                                categories: {
                                    connect: participation.categories.map(
                                        (category) => ({ id: category.id })
                                    ),
                                },
                                partnerLinks: {
                                    connect: participation.partnerLinks.map(
                                        (partnerLink) => ({
                                            id: partnerLink.id,
                                        })
                                    ),
                                },
                                companyFairJobs: {
                                    create: participation.companyFairJobs.map(
                                        (job) => ({
                                            description: job.description,
                                            fairJobId: job.fairJobId,
                                        })
                                    ),
                                },
                                companyFairContactPersons: {
                                    create: participation.companyFairContactPersons.map(
                                        (cp) => ({
                                            contactPersonId: cp.contactPersonId,
                                            ContactPersonTimeslot: {
                                                create: cp.ContactPersonTimeslot.map(
                                                    (ts) => ({
                                                        startTime: ts.startTime,
                                                        endTime: ts.endTime,
                                                        available: ts.available,
                                                    })
                                                ),
                                            },
                                        })
                                    ),
                                },
                            },
                        })
                    }
                },
                {
                    timeout: 30000,
                }
            )
        } catch (error) {
            console.error('Error during transaction:', error)
            throw new Error(
                `Failed to clone fair and related data: ${error.message}`
            )
        }

        return newFair
    }
}
