import { Module } from '@nestjs/common'
import { FairService } from './fair.service'
import { FairResolver } from './fair.resolver'
import { AuthModule } from '../auth/auth.module'
import { EmailServerModule } from '../email-server/email-server.module'
import { CompanyFairJobModule } from '../company-fair-job/company-fair-job.module'
import { CompanyFairParticipationModule } from '../company-fair-participation/company-fair-participation.module'
import { TimeslotModule } from '../timeslot/timeslot.module'
import { CompanyFairContactPersonModule } from '../company-fair-contact-person/company-fair-contact-person.module'
import { ContactPersonTimeslotModule } from '../contact-person-timeslot/contact-person-timeslot.module'
import { FairDayModule } from '../fair-day/fair-day.module'
import { TimeslotGeneratorService } from './timeslot-generator/timeslot-generator.service';

@Module({
    imports: [
        AuthModule,
        CompanyFairJobModule,
        CompanyFairParticipationModule,
        TimeslotModule,
        CompanyFairContactPersonModule,
        ContactPersonTimeslotModule,
        FairDayModule,
    ],
    providers: [FairResolver, FairService, TimeslotGeneratorService],
})
export class FairModule {}
