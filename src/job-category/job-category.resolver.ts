import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { JobCategoryService } from './job-category.service'
import { JobCategory } from './entities/job-category.entity'
import { CreateJobCategoryInput } from './dto/create-job-category.input'
import { UpdateJobCategoryInput } from './dto/update-job-category.input'
import { SuperGuard } from '../guards/super-guard'
import { UseGuards } from '@nestjs/common'

@Resolver(() => JobCategory)
export class JobCategoryResolver {
    constructor(private readonly jobCategoryService: JobCategoryService) {}

    @UseGuards(SuperGuard)
    @Mutation(() => JobCategory)
    createJobCategory(
        @Args('createJobCategoryInput')
        createJobCategoryInput: CreateJobCategoryInput
    ) {
        return this.jobCategoryService.create(createJobCategoryInput)
    }

    @Query(() => [JobCategory], { name: 'allJobCategories' })
    findAll() {
        return this.jobCategoryService.findAll()
    }

    @Query(() => JobCategory, { name: 'jobCategoryById' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.jobCategoryService.findOne(id)
    }

    @UseGuards(SuperGuard)
    @Mutation(() => JobCategory)
    updateJobCategory(
        @Args('updateJobCategoryInput')
        updateJobCategoryInput: UpdateJobCategoryInput
    ) {
        return this.jobCategoryService.update(updateJobCategoryInput)
    }

    @UseGuards(SuperGuard)
    @Mutation(() => JobCategory)
    removeJobCategory(@Args('id', { type: () => String }) id: string) {
        return this.jobCategoryService.remove(id)
    }
}
