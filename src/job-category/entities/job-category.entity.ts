import { ObjectType, Field } from '@nestjs/graphql'
import { JobAdvert } from '../../job-advert/entities/job-advert.entity'
import { JobAdvertCount } from './job-advert-count'
import { JobsFilter } from '../../jobs-filter/entities/jobs-filter.entity'
import { CreateJobAdvertInput } from '../../job-advert/dto/create-job-advert.input'

@ObjectType()
export class JobCategory {
    @Field()
    id: string

    @Field()
    name: string

    @Field()
    imageUrl: string

    @Field(() => [JobAdvert])
    jobAdverts?: CreateJobAdvertInput[]

    @Field(() => [JobsFilter])
    jobsFilter?: JobsFilter[]

    @Field()
    _count?: JobAdvertCount
}
