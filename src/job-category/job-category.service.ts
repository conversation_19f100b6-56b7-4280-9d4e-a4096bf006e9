import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateJobCategoryInput } from './dto/create-job-category.input'
import { UpdateJobCategoryInput } from './dto/update-job-category.input'
import { JobCategory } from './entities/job-category.entity'
import { PrismaService } from '../prisma.service'

@Injectable()
export class JobCategoryService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        createJobCategoryInput: CreateJobCategoryInput
    ): Promise<JobCategory> {
        return this.prisma.jobCategory.create({
            data: createJobCategoryInput,
        })
    }

    findAll() {
        return this.prisma.jobCategory.findMany({
            include: {
                jobAdverts: true,
                _count: {
                    select: { jobAdverts: true },
                },
            },
        })
    }
    async findOne(id: string): Promise<JobCategory> {
        try {
            return this.prisma.jobCategory.findUnique({
                where: {
                    id: id,
                },
                include: { jobAdverts: true },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    async update(
        updateJobCategoryInput: UpdateJobCategoryInput
    ): Promise<JobCategory> {
        return this.prisma.jobCategory.update({
            where: {
                id: updateJobCategoryInput.id,
            },
            data: updateJobCategoryInput,
        })
    }

    remove(id: string) {
        const jobCategoryToDelete = this.prisma.jobCategory.findUnique({
            where: { id: id },
        })

        if (!jobCategoryToDelete) {
            throw new NotFoundException(
                `JobCategory with ID ${id} does not exist`
            )
        }
        return this.prisma.jobCategory.delete({
            where: { id: id },
        })
    }
}
