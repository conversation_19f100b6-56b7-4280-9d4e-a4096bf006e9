import { Module } from '@nestjs/common'
import { SchedulerService } from './scheduler.service'
import { SchedulerResolver } from './scheduler.resolver'
import { FirebaseActionsModule } from '../firebase-actions/firebase-actions.module'
import { AuthModule } from '../auth/auth.module'
import { ApplicantModule } from '../applicant/applicant.module'
import { EmailServerModule } from '../email-server/email-server.module'

@Module({
    imports: [
        FirebaseActionsModule,
        AuthModule,
        ApplicantModule,
        EmailServerModule,
    ],
    exports: [SchedulerService],
    providers: [SchedulerResolver, SchedulerService],
})
export class SchedulerModule {}
