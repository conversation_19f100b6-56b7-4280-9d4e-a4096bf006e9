import { Injectable } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { FirebaseActionsService } from '../firebase-actions/firebase-actions.service'
import { Cron, CronExpression } from '@nestjs/schedule'

@Injectable()
export class SchedulerService {
    constructor(
        private readonly firebaseService: FirebaseActionsService,
        private readonly prisma: PrismaService
    ) {}

    @Cron(CronExpression.EVERY_DAY_AT_1AM)
    async deleteOldImages() {
        console.log('Running scheduled image deletion...')

        try {
            const imagesToDelete = await this.prisma.staleImage.findMany({
                where: {
                    createdAt: {
                        lt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // Older than 3 days
                    },
                },
                select: { url: true, id: true },
            })

            for (const image of imagesToDelete) {
                await this.firebaseService.deleteImage(image.url)
                await this.prisma.staleImage.delete({
                    where: { id: image.id },
                })
            }
        } catch (error) {
            console.error('Error in scheduled image deletion:', error)
        }
    }
}
