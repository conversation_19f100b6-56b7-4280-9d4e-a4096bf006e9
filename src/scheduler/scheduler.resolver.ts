import { Resolver, Mutation } from '@nestjs/graphql'
import { SchedulerService } from './scheduler.service'
import { UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

@UseGuards(GraphqlAuthGuard)
@Resolver()
export class SchedulerResolver {
    constructor(private readonly schedulerService: SchedulerService) {}

    @Mutation(() => Boolean)
    async triggerImageDeletion(): Promise<boolean> {
        try {
            await this.schedulerService.deleteOldImages()
            return true
        } catch (error) {
            console.error('Error triggering image deletion:', error)
            return false
        }
    }
}
