import { Injectable } from '@nestjs/common'
import { PusherPubSubService } from '../../pub-sub/pusher-pub-sub.service'

@Injectable()
export class AppointmentPublisherService {
    constructor(private readonly pusherService: PusherPubSubService) {}

    public readonly eventNames = {
        appointmentCreated: 'appointmentCreated',
        appointmentUpdated: 'appointmentUpdated',
        appointmentDeleted: 'appointmentDeleted',
    }

    private formatSlimAppointment(appointment: any) {
        return {
            id: appointment?.id,
            status: appointment?.status,
            applicantId: appointment?.applicant?.id,
            name:
                appointment?.applicant?.firstName +
                ' ' +
                appointment?.applicant?.lastName,
            birthday: appointment?.applicant?.birthDate,
            image: appointment?.applicant?.profileImageUrl,
            logoImageUrl:
                appointment?.contactPersonTimeslot?.companyFairContactPerson
                    ?.companyFairParticipation?.company?.logoImageUrl,
            reservationDate: appointment?.reservationDate,
            companyIsNew: appointment?.companyIsNew,
            fairName:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.name,
            fairAddress:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.location,
            fairCity:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.city,
        }
    }
    private formatSlimAppointmentForApplicant(appointment: any) {
        return {
            id: appointment?.id,
            status: appointment?.status,
            start: appointment?.contactPersonTimeslot?.startTime,
            end: appointment?.contactPersonTimeslot?.endTime,
            applicantIsNew: appointment?.applicantIsNew,
            reservationDate: appointment?.reservationDate,
            chatRoomId: appointment?.chatRoomId || appointment?.chatRoom?.id,
            contactPersonName:
                appointment?.contactPersonTimeslot?.companyFairContactPerson
                    ?.contactPerson?.name,
            logoImageUrl:
                appointment?.contactPersonTimeslot?.companyFairContactPerson
                    ?.companyFairParticipation?.company?.logoImageUrl,
            companyName:
                appointment?.contactPersonTimeslot.companyFairContactPerson
                    ?.companyFairParticipation?.company?.name,
            companyIsNew: appointment?.companyIsNew,
            fairName:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.name,
            fairAddress:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.location,
            fairCity:
                appointment.contactPersonTimeslot.companyFairContactPerson
                    .companyFairParticipation.fair.city,
            rejectReason: appointment?.rejectReason,
        }
    }

    /**
     * Publishes appointment updates to relevant parties based on appointment status
     * @param appointment - The appointment object containing status and other details
     * @param applicantId - ID of the applicant
     * @param contactPersonId - ID of the contact person
     * @param isApplicant - Whether the update is initiated by an applicant (unused in current implementation)
     */
    async publishOnUpdate(
        appointment: any,
        applicantId: string,
        contactPersonId: string,
        isApplicant = false
    ): Promise<void> {
        const { status } = appointment
        const eventName = this.eventNames.appointmentUpdated

        // Define notification targets based on appointment status
        const notificationTargets = this.getNotificationTargets(status)

        // Execute notifications concurrently for better performance
        const notifications = []

        if (notificationTargets.notifyApplicant) {
            notifications.push(
                this.publishToLiveApplicants(
                    applicantId,
                    appointment,
                    eventName
                )
            )
        }

        if (notificationTargets.notifyContactPerson) {
            console.log('publishing to contact person::')
            notifications.push(
                this.publishToLiveContactPersons(
                    contactPersonId,
                    appointment,
                    eventName
                )
            )
        }

        await Promise.all(notifications)
    }

    async publishForNewCompanyChat(
        appointment: any,
        applicantId: string,
        contactPersonId: string
    ): Promise<void> {
        console.log('publishing for new company chat::')
        const eventName = this.eventNames.appointmentUpdated
        await this.publishToLiveContactPersons(
            contactPersonId,
            appointment,
            eventName
        )
    }

    /**
     * Determines who should be notified based on appointment status
     * @param status - The appointment status
     * @returns Object indicating which parties should be notified
     */
    private getNotificationTargets(status: string): {
        notifyApplicant: boolean
        notifyContactPerson: boolean
    } {
        switch (status) {
            case 'REQUESTED':
            case 'REJECTED':
            case 'CANCELED':
                return {
                    notifyApplicant: true,
                    notifyContactPerson: true,
                }

            case 'CONFIRMED':
                return {
                    notifyApplicant: true,
                    notifyContactPerson: false,
                }

            default:
                console.warn(`Unknown appointment status: ${status}`)
                return {
                    notifyApplicant: false,
                    notifyContactPerson: false,
                }
        }
    }

    async publishOnCreate(
        appointment: any,
        contactPersonId: string
    ): Promise<void> {
        await this.publishToLiveContactPersons(
            contactPersonId,
            appointment,
            this.eventNames.appointmentCreated
        )

        await this.publishToLiveApplicants(
            appointment.applicantId,
            appointment,
            this.eventNames.appointmentCreated
        )
    }

    async publishOnDelete(
        applicantId: string,
        appointment: any,
        contactPersonId: string
    ) {
        await this.publishToLiveApplicants(
            applicantId,
            appointment,
            this.eventNames.appointmentDeleted
        )

        await this.publishToLiveContactPersons(
            contactPersonId,
            appointment,
            this.eventNames.appointmentDeleted
        )
    }

    async publishToLiveContactPersons(
        contactPersonId: string,
        appointment: any,
        eventName: string
    ) {
        const appointmentData = this.formatSlimAppointment(appointment)

        const channel = `liveContactPersonAppointments.super`

        try {
            await this.pusherService.publish(
                `private-${channel}`,
                eventName,
                appointmentData
            )
        } catch (err) {
            console.log('Contact person appointment publish error', err)
        }
    }

    async publishToLiveApplicants(
        applicantId: string,
        appointment: any,
        eventName: string
    ) {
        const slimAppointment =
            this.formatSlimAppointmentForApplicant(appointment)

        try {
            const channel = `liveApplicantAppointments.${applicantId}`

            await this.pusherService.publish(
                `private-${channel}`,
                eventName,
                slimAppointment
            )
        } catch (err) {
            console.log('Applicant appointment publish error', err)
        }
    }
}
