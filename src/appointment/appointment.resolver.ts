import { Args, Context, Mutation, Query, Resolver } from '@nestjs/graphql'
import { AppointmentService } from './appointment.service'
import {
    Appointment,
    AppointmentPaginated,
} from './entities/appointment.entity'
import { CreateAppointmentInput } from './dto/create-appointment.input'
import { UpdateAppointmentInput } from './dto/update-appointment.input'
import {
    AppointmentFilterInput,
    FilterOptionsResponse,
} from './dto/appointment-filter.input'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { UseGuards } from '@nestjs/common'

@UseGuards(GraphqlAuthGuard)
@Resolver(() => Appointment)
export class AppointmentResolver {
    constructor(private readonly appointmentService: AppointmentService) {}

    @Mutation(() => Appointment)
    createAppointment(
        @Args('createAppointmentInput')
        createAppointmentInput: CreateAppointmentInput,
        @Context() context: any
    ) {
        const bApplicantId =
            context?.req?.user?.applicantId ||
            createAppointmentInput.applicantId
        return this.appointmentService.create(
            {
                ...createAppointmentInput,
                applicantId: bApplicantId,
            },
            context?.req?.user
        )
    }

    @Query(() => [Appointment], { name: 'findAllAppointments' })
    findAll() {
        return this.appointmentService.findAll()
    }

    @Query(() => AppointmentPaginated, { name: 'findAppointmentWithFilters' })
    findWithFilters(
        @Args('filter', { nullable: true }) filter?: AppointmentFilterInput,
        @Args('skip', { nullable: true }) skip?: number,
        @Args('take', { nullable: true }) take?: number
    ) {
        return this.appointmentService.findWithFilters(filter, skip, take)
    }

    @Query(() => FilterOptionsResponse)
    async getFilterOptions(): Promise<FilterOptionsResponse> {
        const appointments = await this.appointmentService.findAll()
        return this.appointmentService.getFilterOptions(appointments)
    }

    @Query(() => Appointment, { name: 'findAppointment' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.appointmentService.findOne(id)
    }

    @Mutation(() => Appointment)
    updateAppointment(
        @Args('updateAppointmentInput')
        updateAppointmentInput: UpdateAppointmentInput,
        @Context() context: any
    ) {
        const bCompanyUserId = context?.req?.user?.companyUserId
        const bApplicantId = context?.req?.user?.applicantId
        const isApplicant = !!bApplicantId

        return this.appointmentService.update(
            updateAppointmentInput.id,
            updateAppointmentInput,
            isApplicant
        )
    }

    @Mutation(() => Appointment)
    removeAppointment(
        @Args('id', { type: () => String }) id: string,
        @Args('rejectReason', { type: () => String, nullable: true })
        rejectReason?: string
    ) {
        return this.appointmentService.remove(id, rejectReason)
    }

    @Query(() => [Appointment], { name: 'findAppointmentsByApplicant' })
    findByApplicant(
        @Context() context: any,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        return this.appointmentService.findByApplicant(bApplicantId)
    }

    @Query(() => [Appointment], { name: 'findAppointmentsByFair' })
    findByFair(@Args('fairId', { type: () => String }) fairId: string) {
        return this.appointmentService.findByFair(fairId)
    }

    @Query(() => [Appointment], { name: 'findAppointmentsByCompany' })
    findByCompany(
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        return this.appointmentService.findByCompany(companyId)
    }

    @Query(() => [Appointment], { name: 'findAppointmentsByContactPerson' })
    findByContactPerson(
        @Args('contactPersonId', { type: () => String }) contactPersonId: string
    ) {
        return this.appointmentService.findByContactPerson(contactPersonId)
    }

    @Mutation(() => Appointment)
    setAppointmentAsOld(
        @Args('appointmentId', { type: () => String }) appointmentId: string
    ) {
        return this.appointmentService.setAppointmentAsOld(appointmentId)
    }
}
