import { ObjectType, Field, registerEnumType } from '@nestjs/graphql'
import { Applicant } from '../../applicant/entities/applicant.entity'
import { ContactPersonTimeslot } from '../../contact-person-timeslot/entities/contact-person-timeslot.entity'
import { AppointmentStatus } from '@prisma/client'
import { ChatRoom } from '../../chat-room/entities/chat-room.entity'

registerEnumType(AppointmentStatus, {
    name: 'AppointmentStatus',
})

@ObjectType()
export class Appointment {
    @Field()
    id: string

    @Field(() => Applicant)
    applicant: Applicant

    @Field()
    applicantId: string

    @Field()
    chatRoomId: string

    @Field()
    applicantIsNew: boolean

    @Field()
    companyIsNew: boolean

    @Field(() => ContactPersonTimeslot)
    contactPersonTimeslot: ContactPersonTimeslot

    @Field(() => ChatRoom)
    chatRoom: ChatRoom

    @Field()
    contactPersonTimeslotId: string

    @Field(() => AppointmentStatus)
    status: AppointmentStatus

    @Field()
    reservationDate: Date

    @Field({ nullable: true })
    rejectReason?: string
}

@ObjectType()
export class AppointmentPaginated {
    @Field(() => [Appointment], { defaultValue: [] })
    items: Appointment[]

    @Field({ nullable: true, defaultValue: 0 })
    count?: number
}
