import { Field, InputType, ObjectType } from '@nestjs/graphql'
import { IsOptional, IsString, IsDate, IsEnum } from 'class-validator'
import { Type } from 'class-transformer'
import { AppointmentStatus } from '@prisma/client'
import { registerEnumType } from '@nestjs/graphql'

registerEnumType(AppointmentStatus, {
    name: 'AppointmentStatus',
})

@InputType()
export class TimeRangeInput {
    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    startTime?: string

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    endTime?: string
}

@InputType()
export class AppointmentFilterInput {
    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    applicantId?: string

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    contactPersonId?: string

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    companyId?: string

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    fairId?: string

    @Field(() => AppointmentStatus, { nullable: true })
    @IsOptional()
    @IsEnum(AppointmentStatus)
    status?: AppointmentStatus

    @Field(() => Date, { nullable: true })
    @IsOptional()
    @IsDate()
    @Type(() => Date)
    date?: Date

    @Field(() => TimeRangeInput, { nullable: true })
    @IsOptional()
    @Type(() => TimeRangeInput)
    timeRange?: TimeRangeInput
}

@ObjectType()
export class IdTextPair {
    @Field(() => String)
    id: string

    @Field(() => String)
    text: string
}

@ObjectType()
export class FilterOptionsResponse {
    @Field(() => [IdTextPair])
    applicants: IdTextPair[]

    @Field(() => [IdTextPair])
    contactPersons: IdTextPair[]

    @Field(() => [IdTextPair])
    companies: IdTextPair[]

    @Field(() => [IdTextPair])
    fairs: IdTextPair[]
}
