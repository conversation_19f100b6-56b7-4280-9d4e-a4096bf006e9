import { InputType, Field, registerEnumType } from '@nestjs/graphql'
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator'
import { AppointmentStatus } from '@prisma/client'

registerEnumType(AppointmentStatus, {
    name: 'AppointmentStatus',
})

@InputType()
export class CreateAppointmentInput {
    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    applicantId: string

    @IsString()
    @Field()
    contactPersonTimeslotId: string

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    companyIsNew?: boolean

    @IsOptional()
    @IsEnum(AppointmentStatus)
    @Field(() => AppointmentStatus, { nullable: true })
    status: AppointmentStatus

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    rejectReason?: string
}
