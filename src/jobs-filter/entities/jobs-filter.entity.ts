import { ObjectType, Field } from '@nestjs/graphql'
import { Applicant } from '../../applicant/entities/applicant.entity'
import { JobCategory } from '../../job-category/entities/job-category.entity'
import { JobAdvertType } from '@prisma/client'

@ObjectType()
export class JobsFilter {
    @Field()
    id: string

    @Field(() => [JobAdvertType])
    type?: JobAdvertType[]

    @Field({ nullable: true })
    currentLocation?: string

    @Field({ nullable: true })
    latitude?: number

    @Field({ nullable: true })
    longitude?: number

    @Field({ nullable: true })
    radius?: number

    @Field(() => [JobCategory])
    categories: JobCategory[]

    @Field(() => Applicant)
    applicant: Applicant
}
