import { Test, TestingModule } from '@nestjs/testing'
import { JobsFilterResolver } from './jobs-filter.resolver'
import { JobsFilterService } from './jobs-filter.service'

describe('JobsFilterResolver', () => {
    let resolver: JobsFilterResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [JobsFilterResolver, JobsFilterService],
        }).compile()

        resolver = module.get<JobsFilterResolver>(JobsFilterResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
