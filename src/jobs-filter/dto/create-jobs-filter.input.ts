import { InputType, Field, registerEnumType } from '@nestjs/graphql'
import { IsArray, IsNumber, IsOptional, IsString } from 'class-validator'
import { JobAdvertType } from '@prisma/client'

registerEnumType(JobAdvertType, { name: 'JobAdvertType' })

@InputType()
export class CreateJobsFilterInput {
    @Field(() => [JobAdvertType], { nullable: true })
    @IsArray()
    @IsOptional()
    type?: JobAdvertType[]

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    currentLocation?: string

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    latitude?: number

    @Field(() => [String])
    @IsArray()
    @IsString({ each: true })
    categories: string[]

    @Field({ nullable: true })
    @IsNumber()
    @IsOptional()
    longitude?: number

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    radius: number
}
