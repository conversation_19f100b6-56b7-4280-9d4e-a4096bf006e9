import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateJobsFilterInput } from './dto/create-jobs-filter.input'
import { UpdateJobsFilterInput } from './dto/update-jobs-filter.input'
import { PrismaService } from '../prisma.service'

@Injectable()
export class JobsFilterService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        createJobsFilterInput: CreateJobsFilterInput,
        applicantId: string
    ) {
        try {
            //check if a filter already exists for the applicant
            const filterExists = await this.prisma.jobsFilter.findUnique({
                where: {
                    applicantId: applicantId,
                },
            })
            if (filterExists) {
                throw new Error(
                    'A jobs filter for this applicant already exists.'
                )
            }

            return await this.prisma.jobsFilter.create({
                data: {
                    ...createJobsFilterInput,
                    categories: {
                        connect: createJobsFilterInput.categories.map(
                            (id: string) => ({
                                id,
                            })
                        ),
                    },
                    applicant: {
                        connect: {
                            id: applicantId,
                        },
                    },
                },
                include: {
                    applicant: true,
                    categories: true,
                },
            })
        } catch (error) {
            if (error) {
                throw new Error(error.message)
            }
        }
    }

    async createOrUpdate(
        createJobsFilterInput: CreateJobsFilterInput,
        applicantId: string
    ) {
        let setJobsFilter = null

        const filterExists = await this.prisma.jobsFilter.findUnique({
            where: {
                applicantId: applicantId,
            },
            include: {
                categories: true,
            },
        })

        if (filterExists) {
            setJobsFilter = this.update(
                {
                    id: filterExists?.id,
                    ...createJobsFilterInput,
                },
                filterExists?.categories?.map((category) => category.id)
            )
        } else {
            setJobsFilter = this.create(createJobsFilterInput, applicantId)
        }

        return setJobsFilter
    }

    findAll() {
        return this.prisma.jobsFilter.findMany({
            include: {
                applicant: true,
                categories: true,
            },
        })
    }

    findOne(id: string) {
        try {
            return this.prisma.jobsFilter.findUnique({
                where: {
                    id: id,
                },
                include: { applicant: true, categories: true },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    async applicantFilter(applicantId: string) {
        try {
            const applicantInfo = await this.prisma.jobsFilter.findUnique({
                where: { applicantId: applicantId },
                include: {
                    applicant: true,
                    categories: true,
                },
            })
            if (!applicantInfo) {
                throw new NotFoundException(
                    'No record found for the provided applicantId'
                )
            }

            return applicantInfo
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    update(
        updateJobsFilterInput: UpdateJobsFilterInput,
        categoriesToDisconnect?: string[]
    ) {
        const { id, categories, ...data } = updateJobsFilterInput
        return this.prisma.jobsFilter.update({
            where: {
                id: id,
            },
            data: {
                categories: {
                    disconnect:
                        categoriesToDisconnect?.map((categoryId) => ({
                            id: categoryId,
                        })) ?? undefined,
                    connect:
                        categories?.map((categoryId: string) => ({
                            id: categoryId,
                        })) ?? undefined,
                },
                ...data,
            },
            include: {
                applicant: true,
                categories: true,
            },
        })
    }

    remove(id: string) {
        const filterToDelete = this.prisma.jobsFilter.findUnique({
            where: { id: id },
        })
        if (!filterToDelete) {
            throw new NotFoundException(
                `Job filter with ID ${id} does not exist`
            )
        }
        return this.prisma.jobAdvert.delete({
            where: { id: id },
        })
    }
}
