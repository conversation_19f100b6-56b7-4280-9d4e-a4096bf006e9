import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { JobsFilterService } from './jobs-filter.service'
import { JobsFilter } from './entities/jobs-filter.entity'
import { CreateJobsFilterInput } from './dto/create-jobs-filter.input'
import { UpdateJobsFilterInput } from './dto/update-jobs-filter.input'
import { UseFilters, UseGuards } from '@nestjs/common'
import { GraphQLErrorFilter } from '../filters/custom-exception.filter'
import { SuperGuard } from '../guards/super-guard'
import { ApplicantGuard } from '../guards/applicant-guard'

@UseFilters(GraphQLErrorFilter)
@Resolver(() => JobsFilter)
export class JobsFilterResolver {
    constructor(private readonly jobsFilterService: JobsFilterService) {}

    @Mutation(() => JobsFilter)
    createJobsFilter(
        @Args('createJobsFilterInput')
        createJobsFilterInput: CreateJobsFilterInput,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string,
        @Context() context: any
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        return this.jobsFilterService.create(
            createJobsFilterInput,
            bApplicantId
        )
    }

    @UseGuards(ApplicantGuard)
    @Mutation(() => JobsFilter)
    setJobsFilter(
        @Args('createJobsFilterInput')
        createJobsFilterInput: CreateJobsFilterInput,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string,
        @Context() context: any
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        return this.jobsFilterService.createOrUpdate(
            createJobsFilterInput,
            bApplicantId
        )
    }

    @UseGuards(SuperGuard)
    @Query(() => [JobsFilter], { name: 'allJobsFilter' })
    findAll() {
        return this.jobsFilterService.findAll()
    }

    @UseGuards(ApplicantGuard)
    @Query(() => JobsFilter, { name: 'jobsFilterById' })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.jobsFilterService.findOne(id)
    }

    @UseGuards(ApplicantGuard)
    @Query(() => JobsFilter, { name: 'getJobsFilterByApplicantId' })
    getFilterByApplicantId(
        @Context() context: any,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string
    ) {
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        return this.jobsFilterService.applicantFilter(bApplicantId)
    }

    @UseGuards(ApplicantGuard)
    @Mutation(() => JobsFilter)
    updateJobsFilter(
        @Args('updateJobsFilterInput')
        updateJobsFilterInput: UpdateJobsFilterInput
    ) {
        return this.jobsFilterService.update(updateJobsFilterInput)
    }

    @UseGuards(ApplicantGuard)
    @Mutation(() => JobsFilter)
    removeJobsFilter(@Args('id', { type: () => String }) id: string) {
        return this.jobsFilterService.remove(id)
    }
}
