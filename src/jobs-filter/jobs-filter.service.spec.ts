import { Test, TestingModule } from '@nestjs/testing'
import { JobsFilterService } from './jobs-filter.service'

describe('JobsFilterService', () => {
    let service: JobsFilterService

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [JobsFilterService],
        }).compile()

        service = module.get<JobsFilterService>(JobsFilterService)
    })

    it('should be defined', () => {
        expect(service).toBeDefined()
    })
})
