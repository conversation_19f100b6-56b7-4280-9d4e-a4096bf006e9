import * as admin from 'firebase-admin'
import configs from '../config'

admin.initializeApp({
    credential: admin.credential.cert({
        projectId: configs().FIREBASE.FIREBASE_PROJECT_ID,
        clientEmail: configs().FIREBASE.FIREBASE_CLIENT_EMAIL,
        // privateKey: process.env.FIREBASE_PRIVATE_KEY,
        privateKey: configs().FIREBASE.FIREBASE_PRIVATE_KEY.replace(
            /\\n/g,
            '\n'
        ),
    }),
})

export { admin }
