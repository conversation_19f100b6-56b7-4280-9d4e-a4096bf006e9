import { Module } from '@nestjs/common'
import { AuthService } from './auth.service'
import { AuthResolver } from './auth.resolver'
import { ApplicantModule } from '../applicant/applicant.module'
import { EmailServerModule } from '../email-server/email-server.module'
import { ConfigModule } from '@nestjs/config'

@Module({
    imports: [ApplicantModule, EmailServerModule, ConfigModule],
    exports: [AuthService],
    providers: [AuthService, AuthResolver],
})
export class AuthModule {}
