import { Args, Mutation, Resolver } from '@nestjs/graphql'
import { AuthService } from './auth.service'

@Resolver()
export class AuthResolver {
    constructor(private readonly authService: AuthService) {}

    @Mutation(() => String, { name: 'resetPassword' })
    resetPassword(
        @Args('email', { type: () => String })
        email: string
    ) {
        return this.authService.handleResetPasswordEmail(email)
    }
}
