import {
    Injectable,
    NotFoundException,
    UnauthorizedException,
} from '@nestjs/common'
import {
    FirebaseCreateUserDto,
    LoginRequestDto,
    RegisterDto,
} from './authTypes/input-dto'
import { PrismaService } from '../prisma.service'
import { admin } from './firebase-admin.module'
import {
    ClaimsResponse,
    FirebaseUserResponse,
    RegisterResponse,
} from './authTypes/response-types'
import { ApplicantService } from '../applicant/applicant.service'
import { EmailServerService } from '../email-server/email-server.service'
import { ConfigService } from '@nestjs/config'

@Injectable()
export class AuthService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly applicantService: ApplicantService,
        private readonly emailService: EmailServerService,
        private readonly configService: ConfigService
    ) {}

    private userType = {
        companyUser: 'companyUser',
        applicant: 'applicant',
        superUser: 'superUser',
    }

    async verifyToken(LoginRequestDto: LoginRequestDto) {
        try {
            const decodedToken = await admin
                .auth()
                .verifyIdToken(LoginRequestDto.token)
            return { user: decodedToken }
        } catch (error) {
            throw new UnauthorizedException()
        }
    }

    async createNewUser(
        firebaseUser: FirebaseCreateUserDto
    ): Promise<FirebaseUserResponse> {
        try {
            const userCredential = await admin.auth().createUser(firebaseUser)
            const newUserUid = userCredential.uid
            return { uid: newUserUid } // Return the user ID
        } catch (error) {
            console.error('Error creating user:', error.message)
            throw new Error('Firebase User creation failed')
        }
    }

    async login(LoginRequestDto: LoginRequestDto) {
        return await this.verifyToken(LoginRequestDto)
    }

    async registerViaFirebase(
        registerDto: RegisterDto
    ): Promise<RegisterResponse> {
        const existingUser = await this.prisma.user.findUnique({
            where: { firebaseUid: registerDto.firebaseUid },
        })

        if (!existingUser) {
            const user = await this.prisma.user.create({
                data: {
                    firebaseUid: registerDto.firebaseUid,
                },
            })
            return { user }
        } else {
            return { user: existingUser }
        }
    }

    async handleResetPasswordEmail(email: string) {
        const resetLink = await this.createResetLink(email)
        await this.sendResetPasswordEmail(email, resetLink)
        return resetLink
    }

    async handleNewCompanyUserEmail(email: string, name: string) {
        const resetLink = await this.createResetLink(email)
        await this.sendNewUserEmail(email, resetLink, name)
        return resetLink
    }

    async createResetLink(email: string): Promise<string> {
        const bridgeHost = this.configService.get('GLOBAL.BRIDGE_FRONT_HOST')
        const bridgeDomain = new URL(`${bridgeHost}/reset-password`)
        // Generate password reset link from Firebase Auth
        const firebaseResetUrl = await admin
            .auth()
            .generatePasswordResetLink(email, {
                url: `${bridgeHost}/reset-password`,
            })

        const urlObject = new URL(firebaseResetUrl)

        const params = urlObject.searchParams

        params.forEach((value, key) => {
            bridgeDomain.searchParams.set(key, value)
        })

        return bridgeDomain.toString()
    }
    async createEmailVerifyLink(email: string): Promise<string> {
        const bridgeHost = this.configService.get('GLOBAL.BRIDGE_FRONT_HOST')
        // Generate password reset link from Firebase Auth
        return await admin.auth().generateEmailVerificationLink(email, {
            url: `${bridgeHost}`,
        })
    }

    async sendResetPasswordEmail(email: string, resetLink: string) {
        await this.emailService.sendResetPasswordEmail({
            email: email,
            resetLink: resetLink,
        })
    }
    async sendNewUserEmail(email: string, resetLink: string, name: string) {
        await this.emailService.sendNewUserEmail({
            email: email,
            resetLink: resetLink,
            name: name,
        })
    }

    async registerApplicant(
        registerDto: RegisterDto
    ): Promise<RegisterResponse> {
        const claimData = {
            applicantId: null,
            bridgeUserId: null,
        }

        const existingUser = await this.prisma.user.findUnique({
            where: { firebaseUid: registerDto.firebaseUid },
        })

        if (!existingUser) {
            const user = await this.prisma.user.create({
                data: {
                    firebaseUid: registerDto.firebaseUid,
                },
            })

            claimData.bridgeUserId = user.id
        } else {
            claimData.bridgeUserId = existingUser.id
        }

        //Set Applicant
        const applicant = await this.applicantService.createOrUpdate(
            registerDto.firebaseUid
        )

        claimData.applicantId = applicant.id

        await admin.auth().setCustomUserClaims(registerDto.firebaseUid, {
            ...claimData,
        })

        return {
            user: {
                id: claimData.bridgeUserId,
                firebaseUid: registerDto.firebaseUid,
            },
        }
    }

    async setUserClaims(registerDto: RegisterDto): Promise<ClaimsResponse> {
        const firebaseUID = registerDto.firebaseUid

        const existingUser = await this.prisma.user.findUnique({
            where: { firebaseUid: firebaseUID },
            include: {
                companyUser: true,
                applicant: true,
            },
        })

        if (!existingUser) {
            throw new NotFoundException(
                'User not found, Please register user first.'
            )
        }

        if (!existingUser?.companyUser && !existingUser?.applicant) {
            throw new NotFoundException(
                'User not found, Please save applicant | companyUser first.'
            )
        }

        //Check if user is applicant or companyUser based on array on user returned
        const userType = existingUser?.companyUser
            ? this.userType.companyUser
            : this.userType.applicant

        //Get User Meta Data
        const userMetaData = await this.getUserMetaData(
            existingUser?.id,
            userType
        )

        //Set User Claims
        await admin.auth().setCustomUserClaims(firebaseUID, {
            ...userMetaData,
        })

        return { claims: { ...userMetaData } }
    }

    async getUserMetaData(userId: string, userType: string) {
        let user = null

        //Set isCompanyUser
        const isCompanyUser = userType === this.userType.companyUser

        if (isCompanyUser) {
            user = await this.prisma.companyUser.findUnique({
                where: { userId: userId },
                include: {
                    companies: true,
                },
            })
        } else {
            user = await this.prisma.applicant.findUnique({
                where: { userId: userId },
            })
        }

        return {
            companyUserId: isCompanyUser ? user?.id : null,
            companyId: isCompanyUser ? user?.activeCompanyId : null,
            applicantId: !isCompanyUser ? user?.id : null,
            bridgeUserId: userId,
        }
    }
}
