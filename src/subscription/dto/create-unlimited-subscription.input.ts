import { InputType, Field } from '@nestjs/graphql'
import {
    IsArray,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator'

@InputType()
export class CreateUnlimitedSubscriptionInput {
    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    customerId: string

    @IsString()
    @Field()
    priceId: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    couponId: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    promoCode: string

    @IsString()
    @Field()
    companyId: string

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    percent_off: number

    @IsNumber()
    @Field()
    durationDays: number

    @IsNumber()
    @Field()
    monthlyPrice: number

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    currency: string
}
