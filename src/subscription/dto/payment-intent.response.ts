import { ObjectType, Field } from '@nestjs/graphql'
import {
    IsString,
    IsNumber,
    IsBoolean,
    IsArray,
    IsOptional,
    ValidateNested,
} from 'class-validator'
import { Type } from 'class-transformer'

@ObjectType()
class Charges {
    @Field()
    @IsString()
    object: string

    @Field(() => [String])
    @IsArray()
    data: string[]

    @Field()
    @IsBoolean()
    has_more: boolean

    @Field()
    @IsNumber()
    total_count: number

    @Field()
    @IsString()
    url: string
}

@ObjectType()
class PaymentMethodOptionsCard {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    installments: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    network: string

    @Field({ nullable: true })
    @IsString()
    request_three_d_secure: string
}

@ObjectType()
class PaymentMethodOptions {
    @Field(() => PaymentMethodOptionsCard)
    @ValidateNested()
    @Type(() => PaymentMethodOptionsCard)
    card: PaymentMethodOptionsCard
}

@ObjectType()
export class PaymentIntentResponseDto {
    @Field()
    @IsString()
    id: string

    @Field()
    @IsString()
    object: string

    @Field(() => [String], { nullable: true })
    @IsArray()
    allowed_source_types: string[]

    @Field()
    @IsNumber()
    amount: number

    @Field({ nullable: true })
    @IsNumber()
    amount_capturable: number

    @Field({ nullable: true })
    @IsNumber()
    amount_received: number

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    application: string

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    application_fee_amount: number

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    canceled_at: number

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    cancellation_reason: string

    @Field()
    @IsString()
    capture_method: string

    @Field(() => Charges)
    @ValidateNested()
    @Type(() => Charges)
    charges: Charges

    @Field()
    @IsString()
    client_secret: string

    @Field({ nullable: true })
    @IsString()
    confirmation_method: string

    @Field()
    @IsNumber()
    created: number

    @Field()
    @IsString()
    currency: string

    @Field({ nullable: true })
    @IsString()
    customer: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    description: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    invoice: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    last_payment_error: string

    @Field()
    @IsBoolean()
    livemode: boolean

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    metadata: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    next_action: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    next_source_action: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    on_behalf_of: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    payment_method: string

    @Field(() => PaymentMethodOptions)
    @ValidateNested()
    @Type(() => PaymentMethodOptions)
    payment_method_options: PaymentMethodOptions

    @Field(() => [String])
    @IsArray()
    payment_method_types: string[]

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    receipt_email: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    review: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    setup_future_usage: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    shipping: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    source: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    statement_descriptor: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    statement_descriptor_suffix: string

    @Field()
    @IsString()
    status: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    transfer_data: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    transfer_group: string
}
