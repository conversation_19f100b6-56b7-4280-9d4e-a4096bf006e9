import { InputType, Field } from '@nestjs/graphql'
import {
    IsArray,
    IsBoolean,
    IsDate,
    IsNumber,
    IsOptional,
    IsString,
} from 'class-validator'
import { PlanType } from '@prisma/client'

@InputType()
export class CreateSubscriptionInput {
    @IsString()
    @IsOptional()
    @Field()
    checkoutSessionId: string

    @IsString()
    @Field()
    plan: PlanType

    @IsNumber()
    @IsOptional()
    @Field()
    amountTotal: number

    @IsString()
    @IsOptional()
    @Field()
    paymentStatus: string

    @IsString()
    @IsOptional()
    @Field()
    invoiceId: string

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    percent_off: number

    @IsString()
    @IsOptional()
    @Field()
    stripeCustomerId: string

    @IsString()
    @IsOptional()
    @Field()
    currency: string

    @IsString()
    @Field()
    stripeSubscriptionId: string

    @IsString()
    @Field({ nullable: true })
    status: string

    @IsBoolean({ message: 'isActive must be a boolean' })
    @Field({ defaultValue: false })
    isActive: boolean

    @Field()
    @IsDate()
    expiresAt: Date
}
@InputType()
export class CreateStripeSubscriptionInput {
    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    customerId: string

    @IsString()
    @IsOptional()
    @Field()
    priceId: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    couponId: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    promoCode: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    jobAdvertId: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    advertTitle: string

    @IsNumber()
    @IsOptional()
    @Field({ nullable: true })
    percent_off: number

    @IsString()
    @IsOptional()
    @Field()
    companyId: string

    @IsArray()
    @IsOptional()
    @Field(() => [String], { nullable: true })
    jobAdverts: string[]
}
