import { Field, ObjectType } from '@nestjs/graphql'
import {
    IsString,
    IsNumber,
    IsBoolean,
    IsOptional,
    ValidateNested,
    IsArray,
    IsObject,
} from 'class-validator'
import { Type } from 'class-transformer'
import { PaymentIntentResponseDto } from './payment-intent.response'

@ObjectType()
class Issuer {
    @Field()
    @IsString()
    type: string
}

@ObjectType()
class Lines {
    @Field()
    @IsString()
    object: string

    @Field()
    @IsBoolean()
    has_more: boolean

    @Field()
    @IsNumber()
    total_count: number

    @Field()
    @IsString()
    url: string
}
@ObjectType()
export class StripeInvoiceResponseDto {
    @Field()
    @IsString()
    id: string

    @Field()
    @IsString()
    object: string

    @Field()
    @IsString()
    account_country: string

    @Field()
    @IsString()
    account_name: string

    @Field()
    @IsNumber()
    amount_due: number

    @Field()
    @IsNumber()
    amount_paid: number

    @Field()
    @IsNumber()
    amount_remaining: number

    @Field()
    @IsNumber()
    amount_shipping: number

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    application: number

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    application_fee_amount: number

    @Field()
    @IsNumber()
    attempt_count: number

    @Field()
    @IsBoolean()
    attempted: boolean

    @Field()
    @IsBoolean()
    auto_advance: boolean

    @Field()
    @IsString()
    billing_reason: string

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    charge: number

    @Field()
    @IsString()
    collection_method: string

    @Field()
    @IsNumber()
    created: number

    @Field()
    @IsString()
    currency: string

    @Field()
    @IsString()
    customer: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    customer_address: string

    @Field()
    @IsString()
    customer_email: string

    @Field()
    @IsString()
    customer_name: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    customer_phone: string

    @Field()
    @IsString()
    customer_tax_exempt: string

    @Field(() => [String])
    @IsArray()
    customer_tax_ids: string[]

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    default_payment_method: string

    @Field(() => [String])
    @IsArray()
    default_tax_rates: string[]

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    description: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    discount: string

    @Field(() => [String])
    @IsArray()
    discounts: string[]

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    due_date: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    ending_balance: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    footer: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    from_invoice: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    hosted_invoice_url: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    invoice_pdf: string

    @Field(() => Issuer)
    @ValidateNested()
    @Type(() => Issuer)
    issuer: Issuer

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    last_finalization_error: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    latest_revision: string

    @Field(() => Lines)
    @ValidateNested()
    @Type(() => Lines)
    lines: Lines

    @Field()
    @IsBoolean()
    livemode: boolean

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    next_payment_attempt: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    number: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    on_behalf_of: string

    @Field()
    @IsBoolean()
    paid: boolean

    @Field()
    @IsBoolean()
    paid_out_of_band: boolean

    @Field(() => PaymentIntentResponseDto)
    @IsObject()
    payment_intent: PaymentIntentResponseDto

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    payment_settings: string

    @Field()
    @IsNumber()
    period_end: number

    @Field()
    @IsNumber()
    period_start: number

    @Field()
    @IsNumber()
    post_payment_credit_notes_amount: number

    @Field()
    @IsNumber()
    pre_payment_credit_notes_amount: number

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    quote: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    receipt_number: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    rendering_options: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    shipping_cost: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    shipping_details: string

    @Field()
    @IsNumber()
    starting_balance: number

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    statement_descriptor: string

    @Field()
    @IsString()
    status: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    subscription: string

    @Field()
    @IsNumber()
    subtotal: number

    @Field()
    @IsNumber()
    subtotal_excluding_tax: number

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    tax: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    test_clock: string

    @Field()
    @IsNumber()
    total: number

    @Field(() => [String])
    @IsArray()
    total_discount_amounts: string[]

    @Field()
    @IsNumber()
    total_excluding_tax: number

    @Field(() => [String])
    @IsArray()
    total_tax_amounts: string[]

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    transfer_data: string

    @Field()
    @IsNumber()
    webhooks_delivered_at: number
}
