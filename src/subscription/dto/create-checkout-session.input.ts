import { InputType, Field } from '@nestjs/graphql'
import { IsEmail, IsOptional, IsString } from 'class-validator'

@InputType()
export class CreateCheckoutSessionInput {
    @IsString()
    @Field({ description: 'Price ID from frontend' })
    priceId: string

    @IsString()
    @Field({ description: 'Advert ID from frontend' })
    advertId: string

    @IsString()
    @Field({ description: 'Advert Title ID from frontend' })
    advertTitle: string

    @IsString()
    @IsEmail()
    @IsOptional()
    @Field({ description: 'User email from frontend' })
    email?: string
}
