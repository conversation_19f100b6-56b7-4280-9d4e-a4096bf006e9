import { InputType, Field, PartialType } from '@nestjs/graphql'
import { IsN<PERSON>ber, IsOptional, IsString } from 'class-validator'

@InputType()
export class SubscriptionIdInput {
    @IsString()
    @Field({ description: 'Stripe Subscription ID' })
    subscriptionId: string
}

@InputType()
export class InvoiceIdInput {
    @IsString()
    @Field({ description: 'Stripe Invoice ID' })
    invoiceId: string
}

@InputType()
export class CustomerIdInput {
    @IsString()
    @Field({ description: 'Stripe Customer ID' })
    customerId: string
}

@InputType()
export class StripeAddressInput {
    @IsString()
    @Field({ description: 'Address line 1' })
    line1: string

    @IsString()
    @IsOptional()
    @Field({ description: 'Address line 2', nullable: true })
    line2: string

    @IsString()
    @Field({ description: 'City' })
    city: string

    @IsString()
    @IsOptional()
    @Field({ description: 'State', nullable: true })
    state: string

    @IsString()
    @Field({ description: 'Postal code' })
    postal_code: string

    @IsString()
    @Field({ description: 'Country' })
    country: string
}

@InputType()
export class StripeShippingInput {
    @IsString()
    @IsOptional()
    @Field({ description: 'Name', nullable: true })
    name: string

    @IsString()
    @IsOptional()
    @Field({ description: 'Phone', nullable: true })
    phone: string

    @Field(() => StripeAddressInput, { description: 'Address' })
    address: StripeAddressInput
}

@InputType()
export class StripeCustomerInput {
    @IsString()
    @Field({ description: 'name' })
    name: string

    @IsString()
    @Field({ nullable: true })
    @IsOptional()
    phone: string

    @IsString()
    @Field({ nullable: true })
    @IsOptional()
    description: string

    @IsString()
    @Field({ description: 'email' })
    email: string

    @Field(() => StripeAddressInput, { description: 'Address', nullable: true })
    @IsOptional()
    address: StripeAddressInput

    @Field(() => StripeShippingInput, {
        description: 'Shipping',
        nullable: true,
    })
    @IsOptional()
    shipping: StripeShippingInput
}

@InputType()
export class UpdateStripeCustomerInput extends PartialType(
    StripeCustomerInput
) {}

@InputType()
export class CreatePaymentIntentInput {
    @IsNumber()
    @Field({ description: 'Payment amount' })
    amount: number

    @IsString()
    @IsOptional()
    @Field({ description: 'Payment currency', nullable: true })
    currency: string

    @IsString()
    @IsOptional()
    @Field({ description: 'Payment customer', nullable: true })
    customer: string
}

@InputType()
export class ConfirmPaymentIntentInput {
    @IsString()
    @Field({ description: 'Payment Intent ID' })
    paymentIntentId: string

    @IsString()
    @Field({ description: 'Payment Method ID' })
    paymentMethodId: string
}
