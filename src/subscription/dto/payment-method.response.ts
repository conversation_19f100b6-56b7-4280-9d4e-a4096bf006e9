import { Field, ObjectType } from '@nestjs/graphql'
import {
    IsString,
    IsBoolean,
    IsNumber,
    IsObject,
    IsOptional,
    IsArray,
} from 'class-validator'

@ObjectType()
class Address {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    city: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    country: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    line1: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    line2: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    postal_code: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    state: string
}

@ObjectType()
class BillingDetails {
    @Field(() => Address, { nullable: true })
    @IsObject()
    address: Address

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    email: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    name: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    phone: string
}

@ObjectType()
class Card {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    brand: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    country: string

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    exp_month: number

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    exp_year: number

    @Field()
    @IsString()
    fingerprint: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    funding: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    generated_from: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    last4: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    wallet: string
}

// {"bank_code":"********","branch_code":"","country":"DE","fingerprint":"vifs0Ho7vwRn1Miu","generated_from":{"charge":null,"setup_attempt":null},"last4":"3000"}

@ObjectType()
class Sepa {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    bank_code: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    branch_code: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    country: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    fingerprint: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    last4: string
}

@ObjectType()
class DataObj {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    object: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    id: string

    @IsOptional()
    @Field(() => BillingDetails, { nullable: true })
    @IsObject()
    @IsOptional()
    billing_details: BillingDetails

    @Field(() => Card, { nullable: true })
    @IsObject()
    @IsOptional()
    card: Card

    @Field(() => Sepa, { nullable: true })
    @IsObject()
    @IsOptional()
    sepa_debit: Sepa

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    created: number

    @Field()
    @IsString()
    customer: string

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    livemode: boolean

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    redaction: string

    @Field()
    @IsString()
    type: string
}

@ObjectType()
export class PaymentMethodResponseDto {
    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    id: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    object: string

    @Field(() => [DataObj], { nullable: true })
    @IsOptional()
    @IsArray()
    data: DataObj[]
}
