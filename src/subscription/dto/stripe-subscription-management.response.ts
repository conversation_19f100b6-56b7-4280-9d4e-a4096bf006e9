import { Field, ObjectType } from '@nestjs/graphql'
import {
    IsN<PERSON>ber,
    IsString,
    IsBoolean,
    IsObject,
    IsDate,
    IsOptional,
} from 'class-validator'

import { StripeInvoiceResponseDto } from './stripe-invoice.reponse'

@ObjectType()
export class CancelSubscriptionResponseDto {
    @Field()
    @IsString()
    id: string

    @Field()
    @IsString()
    object: string

    @Field()
    @IsNumber()
    application_fee_percent: number

    @Field()
    @IsBoolean()
    cancel_at_period_end: boolean

    @Field()
    @IsNumber()
    created: number

    @Field()
    @IsNumber()
    current_period_end: number

    @Field()
    @IsNumber()
    current_period_start: number

    @Field()
    @IsString()
    customer: string

    @Field()
    @IsNumber()
    ended_at: number

    @Field()
    @IsNumber()
    canceled_at: number

    @Field()
    @IsNumber()
    start: number

    @Field()
    @IsString()
    status: string
}

@ObjectType()
export class SubscriptionMetadataResponseDto {
    @Field({ nullable: true })
    @IsString()
    advertId: string

    @Field({ nullable: true })
    @IsString()
    advertIds: string

    @Field({ nullable: true })
    @IsNumber()
    companyId: number

    @Field({ nullable: true })
    @IsString()
    advertTitle: string

    @Field({ nullable: true })
    @IsString()
    advertTitles: string
}

@ObjectType()
export class CreateStripeSubscriptionResponseDto {
    @Field({ nullable: true })
    @IsString()
    id: string

    @Field({ nullable: true })
    @IsString()
    hosted_invoice_url: string

    @Field({ nullable: true })
    @IsString()
    invoice_pdf: string

    @Field({ nullable: true })
    @IsBoolean()
    cancel_at_period_end: boolean

    @Field(() => StripeInvoiceResponseDto)
    @IsObject()
    latest_invoice: StripeInvoiceResponseDto
}

@ObjectType()
export class InvoiceSettings {
    @Field()
    @IsString()
    custom_fields: string

    @Field()
    @IsString()
    footer: string

    @Field({ nullable: true })
    @IsString()
    default_payment_method: string

    @Field()
    @IsString()
    rendering_options: string
}

@ObjectType()
export class CreateStripeCustomerResponseDto {
    @Field()
    @IsString()
    id: string

    @Field({ nullable: true })
    @IsString()
    object: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    address: string

    @Field({ nullable: true })
    @IsNumber()
    balance: number

    @Field({ nullable: true })
    @IsOptional()
    @IsDate()
    created: Date

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    currency: string

    @Field({ nullable: true })
    @IsOptional()
    default_source: string

    @Field({ nullable: true })
    @IsBoolean()
    delinquent: boolean

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    description: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    discount: string

    @Field()
    @IsString()
    email: string

    @Field({ nullable: true })
    @IsString()
    name: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    phone: string
}
