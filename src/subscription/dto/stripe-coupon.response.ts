import { Field, ObjectType } from '@nestjs/graphql'
import {
    IsOptional,
    IsBoolean,
    IsNumber,
    IsString,
    IsObject,
} from 'class-validator'

@ObjectType()
export class StripeCouponResponseDto {
    @Field()
    @IsString()
    id: string

    @Field()
    @IsString()
    object: string

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    amount_off: number

    @Field()
    @IsNumber()
    created: number

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    currency: string

    @Field()
    @IsString()
    duration: string

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    duration_in_months: number

    @Field()
    @IsBoolean()
    livemode: boolean

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    max_redemptions: number

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    name: string

    @Field()
    @IsNumber()
    percent_off: number

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    redeem_by: number

    @Field()
    @IsNumber()
    times_redeemed: number

    @Field()
    @IsBoolean()
    valid: boolean
}

// {
//     id: 'promo_1QtX1HG9yclWrwpcZ8F6462Z',
//         object: 'promotion_code',
//     active: true,
//     code: 'OCSFEXNF',
//     coupon: [Object],
//     created: 1739809087,
//     customer: null,
//     expires_at: null,
//     livemode: false,
//     max_redemptions: 4,
//     metadata: {},
//     restrictions: [Object],
//         times_redeemed: 0
// }

@ObjectType()
export class StripePromoCodeResponseDto {
    @Field()
    @IsString()
    id: string

    @Field()
    @IsString()
    object: string

    @Field()
    @IsNumber()
    created: number

    @Field()
    @IsBoolean()
    livemode: boolean

    @Field({ nullable: true })
    @IsOptional()
    @IsNumber()
    max_redemptions: number

    @Field()
    @IsNumber()
    times_redeemed: number

    @Field(() => StripeCouponResponseDto)
    @IsObject()
    coupon: StripeCouponResponseDto
}
