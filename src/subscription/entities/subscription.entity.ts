import { ObjectType, Field, Float, registerEnumType } from '@nestjs/graphql'
import { JobAdvert } from '../../job-advert/entities/job-advert.entity'
import { PlanType, SubscriptionType } from '@prisma/client'
import { Company } from '../../company/entities/company.entity'
import { PricingPlan } from '../../pricing-plan/pricing-plan.type'

registerEnumType(SubscriptionType, {
    name: 'SubscriptionType',
    description: 'The subscription type for a company',
})

@ObjectType()
export class StripeSubscription {
    @Field()
    id: string

    @Field()
    plan: PlanType

    @Field({ nullable: true })
    stripeCustomerId?: string

    @Field({ nullable: true })
    subscriptionId?: string

    @Field({ nullable: true })
    isActive?: boolean

    @Field({ nullable: true, defaultValue: false })
    cancelAtPeriodEnd?: boolean

    @Field({ nullable: true })
    jobAdvertId: string

    @Field(() => JobAdvert, { nullable: true })
    jobAdvert: JobAdvert

    @Field()
    companyId: string

    @Field(() => Company)
    company: Company

    @Field(() => Date)
    expiresAt: Date

    @Field()
    checkoutSessionId: string

    @Field({ nullable: true, defaultValue: 0 })
    amountTotal: number

    @Field()
    paymentStatus: string

    @Field()
    percent_off: number

    @Field()
    invoiceId: string

    @Field()
    currency: string

    @Field()
    status: string

    @Field()
    stripeSubscriptionId: string

    @Field()
    stripePriceId: string

    @Field({ nullable: true })
    canceledAt?: Date

    @Field({ nullable: true })
    pricingPlanId?: string

    @Field(() => PricingPlan, { nullable: true })
    pricingPlan?: PricingPlan

    @Field(() => Date)
    currentPeriodStart: Date

    @Field(() => Date)
    currentPeriodEnd: Date

    @Field(() => Date)
    lastPaymentDate: Date

    @Field(() => Float)
    lastPaymentAmount: number

    @Field(() => Date)
    createdAt: Date

    @Field(() => Date)
    updatedAt: Date

    @Field(() => SubscriptionType)
    subscriptionType: SubscriptionType
}
