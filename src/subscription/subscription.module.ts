import { MiddlewareConsumer, Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { StripeModule } from '@golevelup/nestjs-stripe'
import { HttpModule } from '@nestjs/axios'
import { RawBodyMiddleware } from '@golevelup/nestjs-webhooks'
import { SubscriptionService } from './subscription.service'
import { SubscriptionResolver } from './subscription.resolver'
import { SubscriptionController } from './subscription.controller'
import { JobActionsModule } from '../job-actions/job-actions.module'
import { AuthModule } from '../auth/auth.module'
import { CompanyModule } from '../company/company.module'
import { JobAdvertModule } from '../job-advert/job-advert.module'

@Module({
    imports: [
        StripeModule.forRootAsync(StripeModule, {
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) =>
                configService.get('STRIPE_CONFIG'),
            inject: [ConfigService],
        }),
        HttpModule,
        JobActionsModule,
        JobAdvertModule,
        AuthModule,
        CompanyModule,
    ],
    providers: [SubscriptionResolver, SubscriptionService],
    controllers: [SubscriptionController],
})
export class SubscriptionModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(RawBodyMiddleware)
            .forRoutes('subscription/handle-webhook')
    }
}
