import {
    Injectable,
    Logger,
    NotFoundException,
    UseGuards,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { PrismaService } from '../prisma.service'
import Stripe from 'stripe'
import { CreateCheckoutSessionInput } from './dto/create-checkout-session.input'
import { CreatePortalSessionInput } from './dto/create-portal-session.input'
import {
    CreateStripeSubscriptionInput,
    CreateSubscriptionInput,
} from './dto/create-subscription.input'
import { CreateUnlimitedSubscriptionInput } from './dto/create-unlimited-subscription.input'
import { UpdateSubscriptionInput } from './dto/update-subscription.input'
import { InjectStripeClient } from '@golevelup/nestjs-stripe'
import { StripeSubscription } from '@prisma/client'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import {
    ConfirmPaymentIntentInput,
    CreatePaymentIntentInput,
    CustomerIdInput,
    InvoiceIdInput,
    StripeCustomerInput,
    SubscriptionIdInput,
    UpdateStripeCustomerInput,
} from './dto/stripe-subscription-management.input'
import { CompanyService } from '../company/company.service'
import { path } from 'ramda'
import configs from '../config'
import { JobAdvertService } from '../job-advert/job-advert.service'
import { CreateStripeSubscriptionResponseDto } from './dto/stripe-subscription-management.response'

type SubscriptionWithPercentOff = Stripe.Subscription & {
    percent_off?: number
}

@Injectable()
export class SubscriptionService {
    constructor(
        @InjectStripeClient() private stripe: Stripe,
        private configService: ConfigService,
        private jobAdvertService: JobAdvertService,
        private prisma: PrismaService,
        private company: CompanyService
    ) {
        this.stripe = new Stripe(configs().STRIPE_CONFIG.apiKey, {
            apiVersion: '2022-11-15',
        })
    }

    async createBulkCheckoutSession(
        customer: string,
        companyId: string,
        createCheckoutSessionInputs: CreateCheckoutSessionInput[]
    ): Promise<Stripe.Response<Stripe.Checkout.Session> | undefined> {
        const items = []
        const taxRates = await this.stripe.taxRates.retrieve(
            configs().STRIPE_TAX_RATE_ID
        )

        // Group inputs by priceId
        const groupedInputs = createCheckoutSessionInputs.reduce(
            (groups, input) => {
                if (!groups[input.priceId]) {
                    groups[input.priceId] = []
                }
                groups[input.priceId].push(input)
                return groups
            },
            {}
        )

        for (const priceId in groupedInputs) {
            const price = await this.stripe.prices.retrieve(priceId, {
                expand: ['product'],
            })

            items.push({
                price: price.id,
                quantity: groupedInputs[priceId].length,
                tax_rates: [taxRates.id],
            })
        }

        const metadata = {
            companyId: companyId,
            advertIds: JSON.stringify(
                createCheckoutSessionInputs.map((input) => input.advertId)
            ),
            advertTitles: JSON.stringify(
                createCheckoutSessionInputs.map((input) => input.advertTitle)
            ),
        }

        return await this.stripe.checkout.sessions.create({
            billing_address_collection: 'auto',
            automatic_tax: { enabled: false },
            customer: customer ? customer : undefined,
            line_items: items,
            mode: 'subscription',
            subscription_data: {
                metadata: metadata,
            },
            tax_id_collection: {
                enabled: true,
            },
            customer_update: {
                name: 'auto',
            },
            metadata: metadata,
            success_url: `${this.configService.get(
                'GLOBAL.BRIDGE_FRONT_HOST'
            )}/company/subscription/landing?success=true&session_id={CHECKOUT_SESSION_ID}`,
            cancel_url: `${this.configService.get(
                'GLOBAL.BRIDGE_FRONT_HOST'
            )}?company/subscription/landing?canceled=true`,
        })
    }

    async createCheckoutSession(
        customer: string,
        companyId: string,
        createCheckoutSessionInput: CreateCheckoutSessionInput
    ): Promise<Stripe.Response<Stripe.Checkout.Session> | undefined> {
        const price = await this.stripe.prices.retrieve(
            createCheckoutSessionInput.priceId,
            {
                expand: ['product'],
            }
        )

        const taxRates = await this.stripe.taxRates.retrieve(
            this.configService.get('STRIPE_LIVE_TAX_RATE_ID')
        )

        const metadata = {
            companyId: companyId,
            advertId: createCheckoutSessionInput.advertId,
            advertTitle: createCheckoutSessionInput.advertTitle,
        }

        return await this.stripe.checkout.sessions.create({
            billing_address_collection: 'auto',
            automatic_tax: { enabled: false },
            customer: customer ? customer : undefined,
            line_items: [
                {
                    price: price.id,
                    quantity: 1,
                    tax_rates: [taxRates.id],
                },
            ],
            mode: 'subscription',
            subscription_data: {
                metadata: metadata,
            },
            tax_id_collection: {
                enabled: true,
            },
            customer_update: {
                name: 'auto',
                address: 'auto',
            },
            metadata: metadata,
            success_url: `${this.configService.get(
                'GLOBAL.BRIDGE_FRONT_HOST'
            )}/company/subscription/landing?success=true&session_id={CHECKOUT_SESSION_ID}&adId=${
                createCheckoutSessionInput.advertId
            }`,
            cancel_url: `${this.configService.get(
                'GLOBAL.BRIDGE_FRONT_HOST'
            )}?company/subscription/landing?canceled=true&adId=${
                createCheckoutSessionInput.advertId
            }`,
        })
    }

    async createSetupCheckout(
        customer: string,
        companyId: string,
        type: string
    ): Promise<Stripe.Response<Stripe.Checkout.Session> | undefined> {
        const metadata = {
            companyId: companyId,
        }

        const returnUrl =
            type === 'checkout'
                ? 'company/subscription/checkout'
                : 'company/subscription'

        return await this.stripe.checkout.sessions.create({
            mode: 'setup',
            billing_address_collection: 'required',
            automatic_tax: { enabled: false },
            tax_id_collection: {
                enabled: true,
            },
            customer_update: {
                name: 'auto',
                address: 'auto',
            },
            payment_method_types: ['card', 'sepa_debit'],
            customer: customer ? customer : undefined,
            metadata: metadata,
            currency: 'eur',
            success_url: `${this.configService.get(
                'GLOBAL.BRIDGE_FRONT_HOST'
            )}/${returnUrl}?success=true&session_id={CHECKOUT_SESSION_ID}&cId=${companyId}`,
            cancel_url: `${this.configService.get(
                'GLOBAL.BRIDGE_FRONT_HOST'
            )}/${returnUrl}?canceled=true&cId=${companyId}`,
        })
    }

    async createPaymentIntent(
        paymentInput: CreatePaymentIntentInput
    ): Promise<Stripe.Response<Stripe.PaymentIntent> | undefined> {
        return await this.stripe.paymentIntents.create({
            amount: paymentInput.amount,
            currency: paymentInput.currency || 'eur',
            customer: paymentInput.customer,
        })
    }

    async retrievePaymentMethodList(
        customerIdInput: CustomerIdInput
    ): Promise<
        Stripe.Response<Stripe.ApiList<Stripe.PaymentMethod>> | undefined
    > {
        const payList = await this.stripe.customers.listPaymentMethods(
            customerIdInput.customerId
        )

        return payList
    }

    async confirmPayment(
        paymentInput: ConfirmPaymentIntentInput,
        customerIp: string,
        userAgent: string
    ): Promise<Stripe.Response<Stripe.PaymentIntent> | undefined> {
        // Retrieve the payment method to check its type
        const paymentMethod = await this.stripe.paymentMethods.retrieve(
            paymentInput.paymentMethodId
        )

        let confirmParams: Stripe.PaymentIntentConfirmParams = {
            payment_method: paymentInput.paymentMethodId,
            return_url: `${this.configService.get(
                'GLOBAL.BRIDGE_FRONT_HOST'
            )}/company/subscription?success=true`,
        }

        // If the payment method type is 'sepa_debit', include the mandate_data
        if (paymentMethod.type === 'sepa_debit') {
            confirmParams = {
                ...confirmParams,
                mandate_data: {
                    customer_acceptance: {
                        type: 'online',
                        online: {
                            ip_address: customerIp,
                            user_agent: userAgent,
                        },
                    },
                },
            }
        }

        return await this.stripe.paymentIntents.confirm(
            paymentInput.paymentIntentId,
            confirmParams
        )
    }

    async createPortalSession(
        createPortalSessionInput: CreatePortalSessionInput
    ): Promise<Stripe.Response<Stripe.BillingPortal.Session>> {
        Logger.error(
            'Websocket server initiated and ready to receive connections'
        )
        return this.stripe.billingPortal.sessions.create({
            customer: createPortalSessionInput.customerId,
        })
    }

    async handleSubCancel(subscriptionIdInput: SubscriptionIdInput) {
        return this.prisma.stripeSubscription.updateMany({
            where: {
                stripeSubscriptionId: subscriptionIdInput.subscriptionId,
            },
            data: {
                status: 'canceled',
                cancelAtPeriodEnd: true,
            },
        })
    }

    async cancelStripeSubscription(
        subscriptionIdInput: SubscriptionIdInput
    ): Promise<Stripe.Response<Stripe.Subscription>> {
        const stripeCancelled = this.stripe.subscriptions.update(
            subscriptionIdInput.subscriptionId,
            {
                cancel_at_period_end: true,
            }
        )

        try {
            this.handleSubCancel(subscriptionIdInput)
        } catch (err) {}

        return stripeCancelled
    }

    async resumeStripeSubscription(
        subscriptionIdInput: SubscriptionIdInput
    ): Promise<Stripe.Response<Stripe.Subscription>> {
        const resumedSub = this.stripe.subscriptions.update(
            subscriptionIdInput.subscriptionId,
            {
                cancel_at_period_end: false,
            }
        )

        await this.prisma.stripeSubscription.updateMany({
            where: {
                stripeSubscriptionId: subscriptionIdInput.subscriptionId,
            },
            data: {
                status: 'active',
                cancelAtPeriodEnd: false,
            },
        })

        return resumedSub
    }

    async retrieveStripeInvoice(
        invoiceIdInput: InvoiceIdInput
    ): Promise<Stripe.Response<Stripe.Invoice>> {
        return this.stripe.invoices.retrieve(invoiceIdInput.invoiceId)
    }
    async sendStripeInvoice(
        invoiceIdInput: InvoiceIdInput
    ): Promise<Stripe.Response<Stripe.Invoice>> {
        return await this.stripe.invoices.sendInvoice(invoiceIdInput.invoiceId)
    }

    async createStripeCustomer(
        companyId: string,
        customerInput: StripeCustomerInput
    ): Promise<Stripe.Response<Stripe.Customer>> {
        const strCustomer = await this.stripe.customers.create(customerInput)
        await this.company.update(companyId, {
            stripeCustomerId: strCustomer.id,
        })
        return strCustomer
    }

    async updateStripeCustomer(
        customerId: string,
        customerInput: UpdateStripeCustomerInput
    ): Promise<Stripe.Response<Stripe.Customer>> {
        return await this.stripe.customers.update(customerId, {
            ...customerInput,
        })
    }

    async retrieveSubscriptionMetadata(
        subscriptionIdInput: SubscriptionIdInput
    ): Promise<any> {
        const subscription = await this.stripe.subscriptions.retrieve(
            subscriptionIdInput.subscriptionId
        )
        return subscription.metadata
    }

    async getStripeCustomer(customerIdInput: CustomerIdInput): Promise<any> {
        if (!customerIdInput?.customerId)
            throw new Error('Customer ID is required')

        return await this.stripe.customers.retrieve(customerIdInput.customerId)
    }

    async getStripeCouponById(couponId: string): Promise<Stripe.Coupon | null> {
        if (!couponId) {
            throw new Error('Coupon ID is required')
        }

        try {
            const coupon = await this.stripe.coupons.retrieve(couponId)
            if (!coupon.valid) {
                throw new Error('Coupon has already been redeemed')
            }

            return coupon
        } catch (error) {
            console.error('Error retrieving coupon:', error.message)
            throw new Error('Failed to retrieve coupon')
        }
    }

    async getStripeCouponByPromoCode(
        promoCode: string
    ): Promise<Stripe.Coupon | null> {
        if (!promoCode) {
            throw new Error('Promo code is required')
        }

        try {
            const promotionCodes = await this.stripe.promotionCodes.list({
                code: promoCode,
                limit: 1,
            })

            if (promotionCodes.data.length === 0) {
                throw new Error('Promotion code not found')
            }

            const promotionCode = promotionCodes.data[0]

            const coupon = await this.stripe.coupons.retrieve(
                promotionCode.coupon.id
            )
            return coupon
        } catch (error) {
            console.error('Error retrieving coupon:', error.message)
            throw new Error('Failed to retrieve coupon')
        }
    }

    async getStripePromoCodeDetails(
        promoCode: string
    ): Promise<Stripe.PromotionCode | null> {
        if (!promoCode) {
            throw new Error('Promo code is required')
        }

        try {
            const promotionCodes = await this.stripe.promotionCodes.list({
                code: promoCode,
                limit: 1,
            })

            if (promotionCodes.data.length === 0) {
                throw new Error('Promotion code not found')
            }

            return promotionCodes.data[0]
        } catch (error) {
            console.error('Error retrieving promotion code:', error.message)
            throw new Error('Failed to retrieve promotion code')
        }
    }

    async getStripeCouponByPromoCodeOld(
        promoCode: string
    ): Promise<Stripe.Coupon | null> {
        if (!promoCode) {
            throw new Error('Promo code is required')
        }

        try {
            const promotionCodes = await this.stripe.promotionCodes.list({
                code: promoCode,
                limit: 1,
            })

            if (promotionCodes.data.length === 0) {
                throw new Error('Promotion code not found')
            }

            const promotionCode = promotionCodes.data[0]

            const coupon = await this.stripe.coupons.retrieve(
                promotionCode.coupon.id
            )
            return coupon
        } catch (error) {
            console.error('Error retrieving coupon:', error.message)
            throw new Error('Failed to retrieve coupon')
        }
    }

    async createStripeSubscription(
        createStripeSubscriptionInput: CreateStripeSubscriptionInput,
        subType = 'charge_automatically'
    ): Promise<SubscriptionWithPercentOff> {
        let couponDet = null

        const priceId = configs().STRIPE_PRODUCT_PRICE_ID

        try {
            const price = await this.stripe.prices.retrieve(priceId, {
                expand: ['product'],
            })

            const taxRates = await this.stripe.taxRates.retrieve(
                configs().STRIPE_TAX_RATE_ID
            )

            const metadata = {
                companyId: createStripeSubscriptionInput.companyId,
                jobAdvertId: createStripeSubscriptionInput.jobAdvertId,
            }

            const subscriptionParams: Stripe.SubscriptionCreateParams = {
                customer: createStripeSubscriptionInput.customerId,
                items: [
                    {
                        price: price.id,
                    },
                ],
                metadata: metadata,
                payment_behavior: 'default_incomplete',
                automatic_tax: { enabled: false },
                default_tax_rates: [taxRates.id],
                payment_settings: {
                    save_default_payment_method: 'on_subscription',
                },
                expand: ['latest_invoice.payment_intent'],
            }

            if (createStripeSubscriptionInput?.couponId) {
                couponDet = await this.getStripeCouponById(
                    createStripeSubscriptionInput.couponId
                )
                // subscriptionParams.coupon =
                //     createStripeSubscriptionInput?.couponId
                subscriptionParams.promotion_code =
                    createStripeSubscriptionInput?.promoCode
            }

            if (subType === 'send_invoice') {
                subscriptionParams.collection_method = 'send_invoice'
                subscriptionParams.days_until_due = 14
            }

            const subscription =
                await this.stripe.subscriptions.create(subscriptionParams)

            if (subType === 'send_invoice') {
                // Finalize the invoice
                const invoiceID = path(['latest_invoice', 'id'], subscription)
                const invoice = await this.stripe.invoices.finalizeInvoice(
                    invoiceID as string
                )

                // Send the invoice
                await this.stripe.invoices.sendInvoice(invoice.id)

                // Refresh the subscription to get the updated status
                const updatedSubscription =
                    await this.stripe.subscriptions.retrieve(subscription.id)
                return updatedSubscription
            }

            return { ...subscription, percent_off: couponDet?.percent_off || 0 }
        } catch (error) {
            throw new Error(error.message)
        }
    }

    async handleCustomerCheckout(
        createStripeSubscriptionInput: CreateStripeSubscriptionInput,
        paymentMethodId: string,
        customerIp: string,
        userAgent: string
    ) {
        const createdStripeSub = await this.createStripeSubscription(
            createStripeSubscriptionInput
        )
        let pmId = null

        if (!paymentMethodId) {
            const paymentMethod = await this.retrievePaymentMethodList({
                customerId: createStripeSubscriptionInput.customerId,
            })

            pmId = path(['data', 0, 'id'], paymentMethod)
        }

        const paymentIntentId: string = path(
            ['latest_invoice', 'payment_intent', 'id'],
            createdStripeSub
        )

        const confirmPayment = await this.confirmPayment(
            {
                paymentIntentId: paymentIntentId,
                paymentMethodId: paymentMethodId || pmId,
            },
            customerIp,
            userAgent
        )

        const createDbSubscriptionPayload: CreateSubscriptionInput = {
            checkoutSessionId: confirmPayment.id,
            amountTotal:
                createdStripeSub?.items.data?.[0]?.plan?.amount /
                    createdStripeSub?.items.data?.[0]?.plan?.interval_count ||
                0,
            stripeCustomerId: createStripeSubscriptionInput.customerId,
            invoiceId: confirmPayment.invoice as string,
            paymentStatus: confirmPayment.status,
            currency: confirmPayment.currency,
            percent_off: createdStripeSub?.discount?.coupon?.percent_off || 0,
            isActive: true,
            status:
                confirmPayment.status === 'succeeded'
                    ? 'active'
                    : createdStripeSub.status,
            expiresAt: new Date(createdStripeSub?.current_period_end * 1000),
            stripeSubscriptionId: createdStripeSub.id as string,
            plan: 'PREMIUM',
        }

        await this.create(
            createDbSubscriptionPayload,
            createdStripeSub.metadata.jobAdvertId,
            createdStripeSub.metadata?.companyId
        )

        return confirmPayment
    }

    async generateSubscriptionInvoice(
        companyId: string,
        createStripeSubscriptionInput: CreateStripeSubscriptionInput,
        customerDetails: UpdateStripeCustomerInput
    ) {
        if (
            !createStripeSubscriptionInput.customerId &&
            customerDetails.email
        ) {
            const createdCustomer = await this.createStripeCustomer(
                createStripeSubscriptionInput.companyId,
                {
                    name: customerDetails.name,
                    email: customerDetails.email,
                    phone: customerDetails?.phone,
                    description: customerDetails?.description,
                    shipping: null,
                    address: {
                        line1: customerDetails.address.line1,
                        line2: customerDetails.address.line2,
                        city: customerDetails.address.city,
                        postal_code: customerDetails.address.postal_code,
                        state: customerDetails.address.state,
                        country: customerDetails.address.country,
                    },
                }
            )

            createStripeSubscriptionInput.customerId = createdCustomer.id
            await this.company.update(companyId, {
                stripeCustomerId: createdCustomer.id,
            })
        }

        const customer = await this.getStripeCustomer({
            customerId: createStripeSubscriptionInput.customerId,
        })

        if (!customer.address) {
            await this.updateStripeCustomer(
                createStripeSubscriptionInput.customerId,
                {
                    address: {
                        line1: customerDetails.address.line1,
                        line2: customerDetails.address.line2,
                        city: customerDetails.address.city,
                        postal_code: customerDetails.address.postal_code,
                        state: customerDetails.address.state,
                        country: customerDetails.address.country,
                    },
                }
            )
        }

        const createdStripeSub = await this.createStripeSubscription(
            createStripeSubscriptionInput,
            'send_invoice'
        )

        const latestInvoiceId =
            path(['latest_invoice', 'id'], createdStripeSub) ||
            createdStripeSub?.latest_invoice

        const createDbSubscriptionPayload: CreateSubscriptionInput = {
            checkoutSessionId: createdStripeSub.id,
            amountTotal:
                createdStripeSub?.items.data?.[0]?.plan?.amount /
                    createdStripeSub?.items.data?.[0]?.plan?.interval_count ||
                0,
            stripeCustomerId: createdStripeSub.customer as string,
            invoiceId: latestInvoiceId as string,
            percent_off: createdStripeSub?.discount?.coupon?.percent_off || 0,
            paymentStatus:
                createdStripeSub.status === 'active' ? 'succeeded' : 'pending',
            currency: createdStripeSub.currency,
            isActive: true,
            status: createdStripeSub.status,
            expiresAt: new Date(createdStripeSub?.current_period_end * 1000),
            stripeSubscriptionId: createdStripeSub.id as string,
            plan: 'PREMIUM',
        }

        await this.create(
            createDbSubscriptionPayload,
            createdStripeSub.metadata.jobAdvertId,
            createdStripeSub.metadata?.companyId
        )

        return createdStripeSub
    }

    async generateBulkSubscriptionInvoices(
        companyId: string,
        createStripeSubscriptionInput: CreateStripeSubscriptionInput,
        customerDetails: UpdateStripeCustomerInput
    ) {
        const { jobAdverts } = createStripeSubscriptionInput
        if (!jobAdverts || jobAdverts.length === 0) {
            throw new Error('No job adverts provided for bulk subscription.')
        }

        let customerId = createStripeSubscriptionInput.customerId
        if (!customerId && customerDetails.email) {
            const createdCustomer = await this.createStripeCustomer(companyId, {
                name: customerDetails.name,
                email: customerDetails.email,
                phone: customerDetails?.phone,
                description: customerDetails?.description,
                shipping: null,
                address: {
                    line1: customerDetails.address.line1,
                    line2: customerDetails.address.line2,
                    city: customerDetails.address.city,
                    postal_code: customerDetails.address.postal_code,
                    state: customerDetails.address.state,
                    country: customerDetails.address.country,
                },
            })

            customerId = createdCustomer.id
            await this.company.update(companyId, {
                stripeCustomerId: createdCustomer.id,
            })

            createStripeSubscriptionInput.customerId = customerId
        }

        if (customerId) {
            const customer = await this.getStripeCustomer({ customerId })

            if (!customer.address) {
                await this.updateStripeCustomer(customerId, {
                    address: {
                        line1: customerDetails.address.line1,
                        line2: customerDetails.address.line2,
                        city: customerDetails.address.city,
                        postal_code: customerDetails.address.postal_code,
                        state: customerDetails.address.state,
                        country: customerDetails.address.country,
                    },
                })
            }
        }

        const subscriptionPromises = jobAdverts?.map(async (jobAdvert) => {
            try {
                const singleSubscriptionInput: CreateStripeSubscriptionInput = {
                    ...createStripeSubscriptionInput,
                    jobAdverts: [jobAdvert],
                    jobAdvertId: jobAdvert.split('|')[0],
                }

                const createdStripeSub = await this.createStripeSubscription(
                    singleSubscriptionInput,
                    'send_invoice'
                )

                const latestInvoiceId =
                    path(['latest_invoice', 'id'], createdStripeSub) ||
                    createdStripeSub?.latest_invoice

                // Prepare the database payload
                const createDbSubscriptionPayload: CreateSubscriptionInput = {
                    checkoutSessionId: createdStripeSub.id,
                    amountTotal:
                        createdStripeSub?.items.data?.[0]?.plan?.amount /
                            createdStripeSub?.items.data?.[0]?.plan
                                ?.interval_count || 0,
                    stripeCustomerId: createdStripeSub.customer as string,
                    invoiceId: latestInvoiceId as string,
                    percent_off:
                        createdStripeSub?.discount?.coupon?.percent_off || 0,
                    paymentStatus:
                        createdStripeSub.status === 'active'
                            ? 'succeeded'
                            : 'pending',
                    currency: createdStripeSub.currency,
                    isActive: true,
                    status: createdStripeSub.status,
                    expiresAt: new Date(
                        createdStripeSub.current_period_end * 1000
                    ),
                    stripeSubscriptionId: createdStripeSub.id as string,
                    plan: 'PREMIUM',
                }

                // Save the subscription to the database
                await this.create(
                    createDbSubscriptionPayload,
                    createdStripeSub.metadata.jobAdvertId,
                    createdStripeSub.metadata?.companyId
                )

                return createdStripeSub
            } catch (error) {
                console.error(
                    `Failed to create subscription for job advert ${jobAdvert}:`,
                    error
                )
                throw error
            }
        })

        // Step 4: Execute all subscription creation promises concurrently
        const results = await Promise.allSettled(subscriptionPromises)

        // Step 5: Filter out successful subscriptions
        const successfulSubscriptions = results
            .filter((result) => result.status === 'fulfilled')
            .map((result) => (result as PromiseFulfilledResult<any>).value)

        return successfulSubscriptions
    }

    async handleBulkCustomerCheckout(
        createStripeSubscriptionInput: CreateStripeSubscriptionInput,
        paymentMethodId: string,
        customerIpAddress: string,
        customerUserAgent: string
    ) {
        const response = []
        for (const jobAdvert of createStripeSubscriptionInput.jobAdverts) {
            const jobAdvertTitle = jobAdvert.split('|')[1]
            const jobAdvertId = jobAdvert.split('|')[0]
            const confirmPayment = await this.handleCustomerCheckout(
                {
                    ...createStripeSubscriptionInput,
                    jobAdvertId: jobAdvertId,
                    advertTitle: jobAdvertTitle,
                },
                paymentMethodId,
                customerIpAddress,
                customerUserAgent
            )
            response.push({ ...confirmPayment, jobAdvert: jobAdvert })
        }

        return response[0]
    }

    @UseGuards(GraphqlAuthGuard)
    async create(
        createSubscriptionInput: CreateSubscriptionInput,
        jobAdvertId: string,
        companyId?: string
    ): Promise<StripeSubscription> {
        const stripeSub = this.prisma.stripeSubscription.create({
            data: {
                ...createSubscriptionInput,
                jobAdvert: {
                    connect: {
                        id: jobAdvertId,
                    },
                },
                company: companyId && {
                    connect: {
                        id: companyId,
                    },
                },
            },
        })
        //Update the isDraft of the jobAdvert to false
        await this.prisma.jobAdvert.update({
            where: {
                id: jobAdvertId,
            },
            data: {
                isDraft: false,
            },
        })
        const jobAdvert = await this.prisma.jobAdvert.findUnique({
            where: {
                id: jobAdvertId,
            },
            include: {
                jobAction: true,
                subscriptions: true,
            },
        })
        await this.jobAdvertService.publishJobAdvertEvent(jobAdvert)

        return stripeSub
    }

    @UseGuards(GraphqlAuthGuard)
    findAll() {
        return this.prisma.stripeSubscription.findMany({
            include: {
                jobAdvert: true,
                company: true,
            },
        })
    }
    @UseGuards(GraphqlAuthGuard)
    findByCompany(companyId: string) {
        return this.prisma.stripeSubscription.findMany({
            where: {
                companyId: companyId,
            },
            include: {
                jobAdvert: true,
            },
        })
    }

    @UseGuards(GraphqlAuthGuard)
    findOne(id: string) {
        try {
            return this.prisma.stripeSubscription.findUnique({
                where: {
                    id: id,
                },
                include: {
                    jobAdvert: true,
                },
            })
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    @UseGuards(GraphqlAuthGuard)
    update(updateSubscriptionInput: UpdateSubscriptionInput) {
        return this.prisma.stripeSubscription.update({
            where: {
                id: updateSubscriptionInput.id,
            },
            data: updateSubscriptionInput,
        })
    }

    async getSubByStripeSubId(subscriptionId: string) {
        const sub = this.prisma.stripeSubscription.findFirst({
            where: {
                stripeSubscriptionId: subscriptionId,
            },
        })

        if (!sub) {
            throw new Error('Subscription not found')
        }

        return sub
    }

    async handleUpdateSubscription(
        subscriptionId: string,
        status: string,
        toCancelAtPeriodEnd = false
    ) {
        const subscription = await this.getSubByStripeSubId(subscriptionId)

        await this.prisma.stripeSubscription.update({
            where: {
                id: subscription.id,
            },
            data: {
                status: status,
                cancelAtPeriodEnd: toCancelAtPeriodEnd,
            },
        })
    }
    async handleDeletedSubscription(
        subscriptionId: string,
        cancelAtPeriodEnd: boolean,
        status: string
    ) {
        const subscription = await this.getSubByStripeSubId(subscriptionId)

        await this.prisma.stripeSubscription.update({
            where: {
                id: subscription.id,
            },
            data: {
                isActive: false,
                status: status,
                plan: 'BASIC',
                cancelAtPeriodEnd: cancelAtPeriodEnd,
            },
        })
    }

    async handleWebhookEvent(payload: any, sig: string): Promise<void> {
        try {
            const event = this.stripe.webhooks.constructEvent(
                payload.toString(),
                sig,
                configs().STRIPE_CONFIG.webhookConfig.stripeSecrets.account
            )

            switch (event.type) {
                case 'customer.subscription.deleted':
                    const subscriptionDeleted: Stripe.Subscription = event.data
                        .object as Stripe.Subscription

                    await this.handleDeletedSubscription(
                        subscriptionDeleted.id,
                        subscriptionDeleted.cancel_at_period_end,
                        subscriptionDeleted.status
                    )
                    break

                case 'customer.subscription.updated':
                    const subscriptionUpdated: Stripe.Subscription = event.data
                        .object as Stripe.Subscription

                    await this.handleUpdateSubscription(
                        subscriptionUpdated.id,
                        subscriptionUpdated.status,
                        subscriptionUpdated.cancel_at_period_end
                    )

                    break

                case 'invoice.paid':
                    const invoicePaymentSucceeded: Stripe.Invoice = event.data
                        .object as Stripe.Invoice

                    await this.handleUpdateSubscription(
                        invoicePaymentSucceeded.subscription as string,
                        invoicePaymentSucceeded.status
                    )
                    break

                default:
                    console.log(`Unhandled event type: ${event.type}`)
            }
        } catch (err) {
            console.error('Error handling webhook event:', err)
            throw new Error('Webhook handling error')
        }
    }

    async createUnlimitedStripeSubscription(
        createUnlimitedSubscriptionInput: CreateUnlimitedSubscriptionInput,
        subType?: string
    ): Promise<SubscriptionWithPercentOff> {
        try {
            let couponDet = null
            const price = await this.stripe.prices.retrieve(
                createUnlimitedSubscriptionInput.priceId
            )

            const taxRates = await this.stripe.taxRates.retrieve(
                configs().STRIPE_TAX_RATE_ID
            )

            const metadata = {
                companyId: createUnlimitedSubscriptionInput.companyId,
                subscriptionType: 'unlimited',
                durationDays:
                    createUnlimitedSubscriptionInput.durationDays.toString(),
            }

            const subscriptionParams: Stripe.SubscriptionCreateParams = {
                customer: createUnlimitedSubscriptionInput.customerId,
                items: [
                    {
                        price: price.id,
                    },
                ],
                metadata: metadata,
                payment_behavior: 'default_incomplete',
                automatic_tax: { enabled: false },
                default_tax_rates: [taxRates.id],
                payment_settings: {
                    save_default_payment_method: 'on_subscription',
                },
                expand: ['latest_invoice.payment_intent'],
            }

            if (createUnlimitedSubscriptionInput?.couponId) {
                couponDet = await this.getStripeCouponById(
                    createUnlimitedSubscriptionInput.couponId
                )
                subscriptionParams.promotion_code =
                    createUnlimitedSubscriptionInput?.promoCode
            }

            if (subType === 'send_invoice') {
                subscriptionParams.collection_method = 'send_invoice'
                subscriptionParams.days_until_due = 14
            }

            const subscription =
                await this.stripe.subscriptions.create(subscriptionParams)

            if (subType === 'send_invoice') {
                // Finalize the invoice
                const invoiceID = path(['latest_invoice', 'id'], subscription)
                const invoice = await this.stripe.invoices.finalizeInvoice(
                    invoiceID as string
                )

                // Send the invoice
                await this.stripe.invoices.sendInvoice(invoice.id)

                // Refresh the subscription to get the updated status
                const updatedSubscription =
                    await this.stripe.subscriptions.retrieve(subscription.id)
                return updatedSubscription
            }

            return { ...subscription, percent_off: couponDet?.percent_off || 0 }
        } catch (error) {
            throw new Error(error.message)
        }
    }

    async handleUnlimitedSubscriptionCheckout(
        createUnlimitedSubscriptionInput: CreateUnlimitedSubscriptionInput,
        paymentMethodId: string,
        customerIp: string,
        userAgent: string
    ) {
        const createdStripeSub = await this.createUnlimitedStripeSubscription(
            createUnlimitedSubscriptionInput
        )
        let pmId = null

        if (!paymentMethodId) {
            const paymentMethod = await this.retrievePaymentMethodList({
                customerId: createUnlimitedSubscriptionInput.customerId,
            })

            pmId = path(['data', 0, 'id'], paymentMethod)
        }

        const paymentIntentId: string = path(
            ['latest_invoice', 'payment_intent', 'id'],
            createdStripeSub
        )

        const confirmPayment = await this.confirmPayment(
            {
                paymentIntentId: paymentIntentId,
                paymentMethodId: paymentMethodId || pmId,
            },
            customerIp,
            userAgent
        )

        // Store unlimited subscription in StripeSubscription table
        const createDbSubscriptionPayload: CreateSubscriptionInput = {
            checkoutSessionId: confirmPayment.id,
            amountTotal:
                createdStripeSub?.items.data?.[0]?.plan?.amount ||
                createUnlimitedSubscriptionInput.monthlyPrice * 100, // Convert to cents
            stripeCustomerId: createUnlimitedSubscriptionInput.customerId,
            invoiceId: confirmPayment.invoice as string,
            paymentStatus: confirmPayment.status,
            currency: createUnlimitedSubscriptionInput.currency || createdStripeSub.currency,
            percent_off: createdStripeSub?.discount?.coupon?.percent_off || 0,
            isActive: confirmPayment.status === 'succeeded',
            status:
                confirmPayment.status === 'succeeded'
                    ? 'active'
                    : createdStripeSub.status,
            expiresAt: new Date(createdStripeSub?.current_period_end * 1000),
            stripeSubscriptionId: createdStripeSub.id as string,
            plan: 'PREMIUM',
        }

        await this.prisma.stripeSubscription.create({
            data: {
                ...createDbSubscriptionPayload,
                subscriptionType: 'COMPANY_UNLIMITED',
                stripePriceId: createUnlimitedSubscriptionInput.priceId,
                currentPeriodStart: new Date(createdStripeSub?.current_period_start * 1000),
                currentPeriodEnd: new Date(createdStripeSub?.current_period_end * 1000),
                metadata: {
                    subscriptionType: 'unlimited',
                    durationDays: createUnlimitedSubscriptionInput.durationDays,
                    monthlyPrice: createUnlimitedSubscriptionInput.monthlyPrice,
                },
                company: {
                    connect: {
                        id: createUnlimitedSubscriptionInput.companyId,
                    },
                },
            },
        })

        // Activate all existing job adverts for this company
        await this.activateAllJobAdvertsForCompany(
            createUnlimitedSubscriptionInput.companyId
        )

        return confirmPayment
    }

    async generateUnlimitedSubscriptionInvoice(
        companyId: string,
        createUnlimitedSubscriptionInput: CreateUnlimitedSubscriptionInput,
        customerDetails: UpdateStripeCustomerInput
    ) {
        if (
            !createUnlimitedSubscriptionInput.customerId &&
            customerDetails.email
        ) {
            const createdCustomer = await this.createStripeCustomer(
                createUnlimitedSubscriptionInput.companyId,
                {
                    name: customerDetails.name,
                    email: customerDetails.email,
                    phone: customerDetails?.phone,
                    description: customerDetails?.description,
                    shipping: null,
                    address: {
                        line1: customerDetails.address.line1,
                        line2: customerDetails.address.line2,
                        city: customerDetails.address.city,
                        postal_code: customerDetails.address.postal_code,
                        state: customerDetails.address.state,
                        country: customerDetails.address.country,
                    },
                }
            )

            createUnlimitedSubscriptionInput.customerId = createdCustomer.id
            await this.company.update(companyId, {
                stripeCustomerId: createdCustomer.id,
            })
        }

        const customer = await this.getStripeCustomer({
            customerId: createUnlimitedSubscriptionInput.customerId,
        })

        if (!customer.address) {
            await this.updateStripeCustomer(
                createUnlimitedSubscriptionInput.customerId,
                {
                    address: {
                        line1: customerDetails.address.line1,
                        line2: customerDetails.address.line2,
                        city: customerDetails.address.city,
                        postal_code: customerDetails.address.postal_code,
                        state: customerDetails.address.state,
                        country: customerDetails.address.country,
                    },
                }
            )
        }

        const createdStripeSub = await this.createUnlimitedStripeSubscription(
            createUnlimitedSubscriptionInput,
            'send_invoice'
        )

        const latestInvoiceId =
            path(['latest_invoice', 'id'], createdStripeSub) ||
            createdStripeSub?.latest_invoice

        // Store unlimited subscription in StripeSubscription table
        const createDbSubscriptionPayload: CreateSubscriptionInput = {
            checkoutSessionId: createdStripeSub.id,
            amountTotal:
                createdStripeSub?.items.data?.[0]?.plan?.amount ||
                createUnlimitedSubscriptionInput.monthlyPrice * 100, // Convert to cents
            stripeCustomerId: createdStripeSub.customer as string,
            invoiceId: latestInvoiceId as string,
            percent_off: createdStripeSub?.discount?.coupon?.percent_off || 0,
            paymentStatus:
                createdStripeSub.status === 'active'
                    ? 'succeeded'
                    : 'pending',
            currency: createUnlimitedSubscriptionInput.currency || createdStripeSub.currency,
            isActive: createdStripeSub.status === 'active',
            status: createdStripeSub.status,
            expiresAt: new Date(
                createdStripeSub.current_period_end * 1000
            ),
            stripeSubscriptionId: createdStripeSub.id as string,
            plan: 'PREMIUM',
        }

        await this.prisma.stripeSubscription.create({
            data: {
                ...createDbSubscriptionPayload,
                subscriptionType: 'COMPANY_UNLIMITED',
                stripePriceId: createUnlimitedSubscriptionInput.priceId,
                currentPeriodStart: new Date(createdStripeSub?.current_period_start * 1000),
                currentPeriodEnd: new Date(createdStripeSub?.current_period_end * 1000),
                metadata: {
                    subscriptionType: 'unlimited',
                    durationDays: createUnlimitedSubscriptionInput.durationDays,
                    monthlyPrice: createUnlimitedSubscriptionInput.monthlyPrice,
                },
                company: {
                    connect: {
                        id: companyId,
                    },
                },
            },
        })

        // Activate all existing job adverts for this company
        await this.activateAllJobAdvertsForCompany(companyId)

        return createdStripeSub
    }

    async activateAllJobAdvertsForCompany(companyId: string): Promise<boolean> {
        try {
            // Update all draft job adverts for this company to be active
            await this.prisma.jobAdvert.updateMany({
                where: {
                    companyId: companyId,
                    isDraft: true,
                },
                data: {
                    isDraft: false,
                },
            })

            // Publish events for all job adverts
            const jobAdverts = await this.prisma.jobAdvert.findMany({
                where: {
                    companyId: companyId,
                },
                include: {
                    jobAction: true,
                    subscriptions: true,
                },
            })

            for (const jobAdvert of jobAdverts) {
                await this.jobAdvertService.publishJobAdvertEvent(jobAdvert)
            }

            return true
        } catch (error) {
            console.error('Error activating job adverts:', error)
            return false
        }
    }

    async syncUnlimitedSubscription(companyId: string): Promise<boolean> {
        try {
            // Find the active unlimited subscription for this company
            const unlimitedSubscription = await this.prisma.stripeSubscription.findFirst({
                where: {
                    companyId: companyId,
                    subscriptionType: 'COMPANY_UNLIMITED',
                    isActive: true,
                },
                orderBy: {
                    createdAt: 'desc',
                },
            })
            
            if (!unlimitedSubscription?.stripeSubscriptionId) {
                return false
            }

            // Check if the subscription is still active in Stripe
            const subscription = await this.stripe.subscriptions.retrieve(
                unlimitedSubscription.stripeSubscriptionId
            )

            const isActive = subscription.status === 'active' || subscription.status === 'trialing'
            const currentPeriodEnd = new Date(subscription.current_period_end * 1000)
            const currentPeriodStart = new Date(subscription.current_period_start * 1000)

            // Update subscription record with current status from Stripe
            await this.prisma.stripeSubscription.update({
                where: {
                    id: unlimitedSubscription.id,
                },
                data: {
                    isActive: isActive,
                    status: subscription.status,
                    currentPeriodEnd: currentPeriodEnd,
                    currentPeriodStart: currentPeriodStart,
                    expiresAt: currentPeriodEnd,
                    cancelAtPeriodEnd: subscription.cancel_at_period_end,
                    canceledAt: subscription.canceled_at 
                        ? new Date(subscription.canceled_at * 1000) 
                        : null,
                },
            })

            return isActive
        } catch (error) {
            console.error('Error syncing unlimited subscription:', error)
            return false
        }
    }
}
