import { <PERSON>, <PERSON>, Headers, Body } from '@nestjs/common'
import { SubscriptionService } from './subscription.service'

@Controller('subscription')
export class SubscriptionController {
    constructor(private subscriptionService: SubscriptionService) {}

    @Post('handle-webhook')
    async handleWebhook(
        @Body() rawBody: Buffer,
        @Headers('stripe-signature') sig: string
    ): Promise<any> {
        try {
            await this.subscriptionService.handleWebhookEvent(rawBody, sig)
            return { received: true }
        } catch (err) {
            console.error('Error handling webhook:', err)
            return { received: false, error: err.message }
        }
    }
}
