import { InjectStripeClient } from '@golevelup/nestjs-stripe'
import { Injectable } from '@nestjs/common'
import Stripe from 'stripe'

@Injectable()
export class SubscriptionWebhookService {
    constructor(@InjectStripeClient() private stripe: Stripe) {}

    // @StripeWebhookHandler('payment_intent.succeeded')
    // async handlePaymentIntentCreated(event: Stripe.Event): Promise<void> {
    //     Logger.log(
    //         'Handling payment_intent.succeeded event',
    //         'SubscriptionWebhookService'
    //     )
    //     console.log(event)
    //     const paymentIntent: Stripe.PaymentIntent = event.data
    //         .object as Stripe.PaymentIntent
    //     Logger.log('[ handlePaymentIntentSucceeded ]', paymentIntent)
    //     // implement here subscription create in our Database
    //     // ...
    // }
    //
    // @StripeWebhookHandler('customer.subscription.deleted')
    // async handleSubscriptionDelete(event: Stripe.Event): Promise<void> {
    //     const dataObject = event.data.object as Stripe.Subscription
    //     console.log(dataObject)
    //     const subscriptionId = dataObject?.id
    //
    //     if (!subscriptionId) {
    //         throw new Error('Subscription ID not found')
    //     }
    //
    //     // implement here subscription delete in our Database
    //     // ...
    // }
    // @StripeWebhookHandler('subscription_schedule.canceled')
    // async handleSubscriptionScheduleCancelled(
    //     event: Stripe.Event
    // ): Promise<void> {
    //     const dataObject = event.data.object as Stripe.SubscriptionSchedule
    //     console.log('Stripe Subscription Schedule::', dataObject)
    //
    //     // implement here subscription delete in our Database
    //     // ...
    // }
}
