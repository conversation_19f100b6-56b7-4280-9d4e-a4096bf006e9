import {
    ObjectType,
    Field,
    ID,
    InputType,
    registerEnumType,
} from '@nestjs/graphql'
import { SubscriptionType } from '@prisma/client'
import {
    IsUUID,
    IsEnum,
    IsOptional,
    IsString,
    IsArray,
    ArrayNotEmpty,
    IsNotEmpty,
} from 'class-validator'
import { Company } from '../company/entities/company.entity'
import { PricingPlan } from '../pricing-plan/pricing-plan.type'

// Register enum for GraphQL
registerEnumType(SubscriptionType, {
    name: 'SubscriptionType',
    description: 'The subscription type for a company',
})

@ObjectType()
export class CompanySubscriptionConfig {
    @Field(() => ID)
    id: string

    @Field(() => ID)
    companyId: string

    @Field(() => Company)
    company?: Company

    @Field(() => SubscriptionType)
    subscriptionType: SubscriptionType

    @Field(() => [PricingPlan], { nullable: true })
    availablePricingPlans: PricingPlan[]

    @Field({ nullable: true })
    notes?: string

    @Field({ nullable: true })
    createdBy?: string

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date
}

@InputType()
export class CreateCompanySubscriptionConfigInput {
    @Field(() => ID)
    @IsUUID()
    @IsNotEmpty()
    companyId: string

    @Field(() => SubscriptionType, {
        defaultValue: SubscriptionType.COMPANY_UNLIMITED,
    })
    @IsEnum(SubscriptionType)
    @IsOptional()
    subscriptionType?: SubscriptionType

    @Field(() => [ID], { defaultValue: [] })
    @IsArray()
    @IsUUID('4', { each: true })
    @IsOptional()
    availablePricingPlanIds?: string[]

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    notes?: string
}

@InputType()
export class UpdateCompanySubscriptionConfigInput {
    @Field(() => ID)
    @IsUUID()
    @IsNotEmpty()
    id: string

    @Field(() => SubscriptionType, { nullable: true })
    @IsEnum(SubscriptionType)
    @IsOptional()
    subscriptionType?: SubscriptionType

    @Field(() => [ID], { nullable: true })
    @IsArray()
    @IsUUID('4', { each: true })
    @IsOptional()
    availablePricingPlanIds?: string[]

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    notes?: string
}

@InputType()
export class BulkUpdateCompanySubscriptionConfigInput {
    @Field(() => [ID])
    @IsArray()
    @ArrayNotEmpty()
    @IsUUID('4', { each: true })
    configIds: string[]

    @Field(() => SubscriptionType, { nullable: true })
    @IsEnum(SubscriptionType)
    @IsOptional()
    subscriptionType?: SubscriptionType

    @Field(() => [ID], { nullable: true })
    @IsArray()
    @IsUUID('4', { each: true })
    @IsOptional()
    availablePricingPlanIds?: string[]

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    notes?: string
}

@InputType()
export class CompanySubscriptionConfigFilterInput {
    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    searchQuery?: string

    @Field(() => SubscriptionType, { nullable: true })
    @IsEnum(SubscriptionType)
    @IsOptional()
    subscriptionType?: SubscriptionType

    @Field(() => ID, { nullable: true })
    @IsUUID()
    @IsOptional()
    pricingPlanId?: string
}
