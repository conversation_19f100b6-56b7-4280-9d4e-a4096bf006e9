import {
    Injectable,
    Logger,
    BadRequestException,
    NotFoundException,
} from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import {
    CompanySubscriptionConfig as PrismaCompanySubscriptionConfig,
    SubscriptionType,
} from '@prisma/client'
import {
    CreateCompanySubscriptionConfigInput,
    UpdateCompanySubscriptionConfigInput,
    BulkUpdateCompanySubscriptionConfigInput,
    CompanySubscriptionConfigFilterInput,
} from './company-subscription-config.type'

@Injectable()
export class CompanySubscriptionConfigService {
    private readonly logger = new Logger(CompanySubscriptionConfigService.name)

    constructor(private prisma: PrismaService) {}

    async findAll(
        filter?: CompanySubscriptionConfigFilterInput
    ): Promise<PrismaCompanySubscriptionConfig[]> {
        const where: any = {}

        if (filter?.searchQuery) {
            where.OR = [
                {
                    company: {
                        name: {
                            contains: filter.searchQuery,
                            mode: 'insensitive',
                        },
                    },
                },
                {
                    company: {
                        city: {
                            contains: filter.searchQuery,
                            mode: 'insensitive',
                        },
                    },
                },
            ]
        }

        if (filter?.subscriptionType) {
            where.subscriptionType = filter.subscriptionType
        }

        if (filter?.pricingPlanId) {
            where.availablePricingPlans = {
                some: {
                    id: filter.pricingPlanId,
                },
            }
        }

        return await this.prisma.companySubscriptionConfig.findMany({
            where,
            include: {
                company: true,
                availablePricingPlans: true,
            },
            orderBy: [{ company: { name: 'asc' } }, { createdAt: 'desc' }],
        })
    }

    async findById(id: string): Promise<PrismaCompanySubscriptionConfig> {
        const config = await this.prisma.companySubscriptionConfig.findUnique({
            where: { id },
            include: {
                company: true,
                availablePricingPlans: true,
            },
        })

        if (!config) {
            throw new NotFoundException(
                `Subscription configuration with ID ${id} not found`
            )
        }

        return config
    }

    async findByCompanyId(
        companyId: string
    ): Promise<PrismaCompanySubscriptionConfig | null> {
        return this.prisma.companySubscriptionConfig.findUnique({
            where: { companyId },
            include: {
                company: true,
                availablePricingPlans: true,
            },
        })
    }

    async create(
        input: CreateCompanySubscriptionConfigInput,
        createdBy?: string
    ): Promise<PrismaCompanySubscriptionConfig> {
        try {
            // Check if company exists
            const company = await this.prisma.company.findUnique({
                where: { id: input.companyId },
            })

            if (!company) {
                throw new BadRequestException(
                    `Company with ID ${input.companyId} not found`
                )
            }

            // Check if configuration already exists for this company
            const existingConfig = await this.findByCompanyId(input.companyId)
            if (existingConfig) {
                throw new BadRequestException(
                    `Subscription configuration already exists for company ${company.name}`
                )
            }

            // Validate pricing plan IDs if provided
            if (input.availablePricingPlanIds?.length) {
                const plansCount = await this.prisma.pricingPlan.count({
                    where: {
                        id: { in: input.availablePricingPlanIds },
                        isActive: true,
                    },
                })

                if (plansCount !== input.availablePricingPlanIds.length) {
                    throw new BadRequestException(
                        'One or more pricing plan IDs are invalid or inactive'
                    )
                }
            }

            const config = await this.prisma.companySubscriptionConfig.create({
                data: {
                    companyId: input.companyId,
                    subscriptionType:
                        input.subscriptionType ||
                        SubscriptionType.COMPANY_UNLIMITED,
                    notes: input.notes,
                    createdBy,
                    ...(input.availablePricingPlanIds?.length && {
                        availablePricingPlans: {
                            connect: input.availablePricingPlanIds.map(
                                (id) => ({
                                    id,
                                })
                            ),
                        },
                    }),
                },
                include: {
                    company: true,
                    availablePricingPlans: true,
                },
            })

            this.logger.log(
                `Created subscription config for company: ${company.name}`
            )
            return config
        } catch (error) {
            this.logger.error('Error creating subscription config:', error)
            if (error instanceof BadRequestException) {
                throw error
            }
            throw new BadRequestException(
                'Failed to create subscription configuration: ' + error.message
            )
        }
    }

    async update(
        input: UpdateCompanySubscriptionConfigInput,
        updatedBy?: string
    ): Promise<PrismaCompanySubscriptionConfig> {
        const { id, ...updateData } = input

        // Find existing config
        const existingConfig = await this.findById(id)

        try {
            // Validate pricing plan IDs if provided
            if (updateData.availablePricingPlanIds !== undefined) {
                if (updateData.availablePricingPlanIds.length > 0) {
                    const plansCount = await this.prisma.pricingPlan.count({
                        where: {
                            id: { in: updateData.availablePricingPlanIds },
                            isActive: true,
                        },
                    })

                    if (
                        plansCount !== updateData.availablePricingPlanIds.length
                    ) {
                        throw new BadRequestException(
                            'One or more pricing plan IDs are invalid or inactive'
                        )
                    }
                }
            }

            const config = await this.prisma.companySubscriptionConfig.update({
                where: { id },
                data: {
                    ...(updateData.subscriptionType !== undefined && {
                        subscriptionType: updateData.subscriptionType,
                    }),
                    ...(updateData.notes !== undefined && {
                        notes: updateData.notes,
                    }),
                    ...(updateData.availablePricingPlanIds !== undefined && {
                        availablePricingPlans: {
                            set: [], // Clear existing connections
                            connect: updateData.availablePricingPlanIds.map(
                                (planId) => ({ id: planId })
                            ),
                        },
                    }),
                },
                include: {
                    company: true,
                    availablePricingPlans: true,
                },
            })

            this.logger.log(`Updated subscription config: ${id}`)
            return config
        } catch (error) {
            this.logger.error('Error updating subscription config:', error)
            if (error instanceof BadRequestException) {
                throw error
            }
            throw new BadRequestException(
                'Failed to update subscription configuration: ' + error.message
            )
        }
    }

    async bulkUpdate(
        input: BulkUpdateCompanySubscriptionConfigInput,
        updatedBy?: string
    ): Promise<PrismaCompanySubscriptionConfig[]> {
        const { configIds, ...updateData } = input

        try {
            // Validate that all configs exist
            const configsCount =
                await this.prisma.companySubscriptionConfig.count({
                    where: { id: { in: configIds } },
                })

            if (configsCount !== configIds.length) {
                throw new BadRequestException(
                    'One or more configuration IDs are invalid'
                )
            }

            // Validate pricing plan IDs if provided
            if (updateData.availablePricingPlanIds !== undefined) {
                if (updateData.availablePricingPlanIds.length > 0) {
                    const plansCount = await this.prisma.pricingPlan.count({
                        where: {
                            id: { in: updateData.availablePricingPlanIds },
                            isActive: true,
                        },
                    })

                    if (
                        plansCount !== updateData.availablePricingPlanIds.length
                    ) {
                        throw new BadRequestException(
                            'One or more pricing plan IDs are invalid or inactive'
                        )
                    }
                }
            }

            // Perform bulk update using transaction
            const updatedConfigs = await this.prisma.$transaction(
                configIds.map((configId) =>
                    this.prisma.companySubscriptionConfig.update({
                        where: { id: configId },
                        data: {
                            ...(updateData.subscriptionType !== undefined && {
                                subscriptionType: updateData.subscriptionType,
                            }),
                            ...(updateData.notes !== undefined && {
                                notes: updateData.notes,
                            }),
                            ...(updateData.availablePricingPlanIds !==
                                undefined && {
                                availablePricingPlans: {
                                    set: [], // Clear existing connections
                                    connect:
                                        updateData.availablePricingPlanIds.map(
                                            (planId) => ({ id: planId })
                                        ),
                                },
                            }),
                        },
                        include: {
                            company: true,
                            availablePricingPlans: true,
                        },
                    })
                )
            )

            this.logger.log(
                `Bulk updated ${configIds.length} subscription configs`
            )
            return updatedConfigs
        } catch (error) {
            this.logger.error(
                'Error bulk updating subscription configs:',
                error
            )
            if (error instanceof BadRequestException) {
                throw error
            }
            throw new BadRequestException(
                'Failed to bulk update subscription configurations: ' +
                    error.message
            )
        }
    }

    async delete(id: string): Promise<boolean> {
        try {
            await this.findById(id) // Ensure it exists

            await this.prisma.companySubscriptionConfig.delete({
                where: { id },
            })

            this.logger.log(`Deleted subscription config: ${id}`)
            return true
        } catch (error) {
            this.logger.error('Error deleting subscription config:', error)
            if (error instanceof NotFoundException) {
                throw error
            }
            throw new BadRequestException(
                'Failed to delete subscription configuration: ' + error.message
            )
        }
    }
}
