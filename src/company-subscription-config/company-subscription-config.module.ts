import { Module } from '@nestjs/common'
import { CompanySubscriptionConfigService } from './company-subscription-config.service'
import { CompanySubscriptionConfigResolver } from './company-subscription-config.resolver'
import { PrismaModule } from '../prisma.module'
import { CompanyModule } from '../company/company.module'
import { PricingPlanModule } from '../pricing-plan/pricing-plan.module'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [PrismaModule, CompanyModule, PricingPlanModule, AuthModule],
    providers: [
        CompanySubscriptionConfigService,
        CompanySubscriptionConfigResolver,
    ],
    exports: [CompanySubscriptionConfigService],
})
export class CompanySubscriptionConfigModule {}
