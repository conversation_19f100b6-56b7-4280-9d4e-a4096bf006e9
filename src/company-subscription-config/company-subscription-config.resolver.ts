import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql'
import { UseGuards } from '@nestjs/common'
import { CompanySubscriptionConfigService } from './company-subscription-config.service'
import {
    CompanySubscriptionConfig,
    CreateCompanySubscriptionConfigInput,
    UpdateCompanySubscriptionConfigInput,
    BulkUpdateCompanySubscriptionConfigInput,
    CompanySubscriptionConfigFilterInput,
} from './company-subscription-config.type'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { SuperGuard } from '../guards/super-guard'

@Resolver(() => CompanySubscriptionConfig)
export class CompanySubscriptionConfigResolver {
    constructor(
        private readonly companySubscriptionConfigService: CompanySubscriptionConfigService
    ) {}

    @Query(() => [CompanySubscriptionConfig], {
        name: 'allCompanySubscriptionConfigs',
    })
    // @UseGuards(GraphqlAuthGuard, SuperGuard)
    async getAllCompanySubscriptionConfigs(
        @Args('filter', {
            type: () => CompanySubscriptionConfigFilterInput,
            nullable: true,
        })
        filter?: CompanySubscriptionConfigFilterInput
    ) {
        return await this.companySubscriptionConfigService.findAll(filter)
    }

    @Query(() => CompanySubscriptionConfig, {
        name: 'companySubscriptionConfig',
    })
    @UseGuards(GraphqlAuthGuard, SuperGuard)
    async getCompanySubscriptionConfig(
        @Args('id', { type: () => String }) id: string
    ) {
        return await this.companySubscriptionConfigService.findById(id)
    }

    @Query(() => CompanySubscriptionConfig, {
        name: 'companySubscriptionConfigByCompanyId',
        nullable: true,
    })
    @UseGuards(GraphqlAuthGuard)
    async getCompanySubscriptionConfigByCompanyId(
        @Args('companyId', { type: () => ID }) companyId: string
    ) {
        return await this.companySubscriptionConfigService.findByCompanyId(
            companyId
        )
    }

    @Mutation(() => CompanySubscriptionConfig, {
        name: 'createCompanySubscriptionConfig',
    })
    @UseGuards(GraphqlAuthGuard, SuperGuard)
    async createCompanySubscriptionConfig(
        @Args('input', { type: () => CreateCompanySubscriptionConfigInput })
        input: CreateCompanySubscriptionConfigInput,
        @Context() context: any
    ) {
        const createdBy = context.user?.uid || context.user?.sub
        return await this.companySubscriptionConfigService.create(
            input,
            createdBy
        )
    }

    @Mutation(() => CompanySubscriptionConfig, {
        name: 'updateCompanySubscriptionConfig',
    })
    @UseGuards(GraphqlAuthGuard, SuperGuard)
    async updateCompanySubscriptionConfig(
        @Args('input', { type: () => UpdateCompanySubscriptionConfigInput })
        input: UpdateCompanySubscriptionConfigInput,
        @Context() context: any
    ) {
        const updatedBy = context.user?.uid || context.user?.sub
        return await this.companySubscriptionConfigService.update(
            input,
            updatedBy
        )
    }

    @Mutation(() => [CompanySubscriptionConfig], {
        name: 'bulkUpdateCompanySubscriptionConfigs',
    })
    @UseGuards(GraphqlAuthGuard, SuperGuard)
    async bulkUpdateCompanySubscriptionConfigs(
        @Args('input', { type: () => BulkUpdateCompanySubscriptionConfigInput })
        input: BulkUpdateCompanySubscriptionConfigInput,
        @Context() context: any
    ) {
        const updatedBy = context.user?.uid || context.user?.sub
        return await this.companySubscriptionConfigService.bulkUpdate(
            input,
            updatedBy
        )
    }

    @Mutation(() => Boolean, {
        name: 'deleteCompanySubscriptionConfig',
    })
    @UseGuards(GraphqlAuthGuard, SuperGuard)
    async deleteCompanySubscriptionConfig(
        @Args('id', { type: () => ID }) id: string
    ): Promise<boolean> {
        return await this.companySubscriptionConfigService.delete(id)
    }
}
