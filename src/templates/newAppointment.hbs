<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta name="format-detection" content="telephone=no,date=no,address=no,email=no" />
    <title>Neuer Termin erstellt</title>
    <!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
    <style type="text/css">
        /* Reset styles */
        body, table, td, p, a, li, blockquote { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
        table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
        img { -ms-interpolation-mode: bicubic; border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none; }

        /* Mobile styles */
        @media only screen and (max-width: 600px) {
            .container { width: 100% !important; max-width: 100% !important; }
            .mobile-padding { padding-left: 20px !important; padding-right: 20px !important; }
            .mobile-center { text-align: center !important; }
            .mobile-hide { display: none !important; }
            .mobile-full { width: 100% !important; display: block !important; }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f1f5f9; font-family: 'Segoe UI', Arial, sans-serif; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;">
<!-- Outer wrapper table -->
<table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 0; padding: 0; background-color: #f1f5f9;">
    <tr>
        <td align="center" style="padding: 40px 20px;">

            <!-- Main container table -->
            <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="600" class="container" style="max-width: 600px; width: 100%; background-color: #ffffff; border-radius: 12px; overflow: hidden;">

                <!-- Header section with gradient -->
                <tr>
                    <td style="padding: 0;">
                        <!--[if gte mso 9]>
                        <v:rect xmlns:v="urn:schemas-microsoft-com:vml" fill="true" stroke="false" style="width:600px;height:140px;">
                            <v:fill type="gradient" color="#E32652" color2="#C41E3A" angle="135" />
                            <v:textbox inset="0,0,0,0" style="mso-fit-shape-to-text:true">
                        <![endif]-->
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #e32652; background-image: linear-gradient(135deg, #e32652 0%, #c41e3a 100%);">
                            <tr>
                                <td align="center" style="padding: 30px 40px 30px 40px;" class="mobile-padding">

                                    <!-- Header text -->
                                    <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                                        <tr>
                                            <td align="center">
                                                <h1 style="margin: 0; padding: 0; color: #ffffff; font-size: 28px; font-weight: 700; line-height: 1.3; font-family: 'Segoe UI', Arial, sans-serif;">Neuer Termin vereinbart!</h1>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align="center" style="padding-top: 10px;">
                                                <p style="margin: 0; padding: 0; color: rgba(255,255,255,0.9); font-size: 16px; line-height: 1.5; font-family: 'Segoe UI', Arial, sans-serif;">Ein wichtiger Termin wurde für Sie erstellt</p>
                                            </td>
                                        </tr>
                                    </table>

                                </td>
                            </tr>
                        </table>
                        <!--[if gte mso 9]>
                        </v:textbox>
                        </v:rect>
                        <![endif]-->
                    </td>
                </tr>

                <!-- Content section -->
                <tr>
                    <td style="padding: 40px 40px 40px 40px; background-color: #ffffff;" class="mobile-padding">

                        <!-- Intro message -->
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-bottom: 25px; background-color: #f0f9ff; border-left: 4px solid #0ea5e9;">
                            <tr>
                                <td style="padding: 16px 20px;">
                                    <p style="margin: 0; padding: 0; color: #0c4a6e; font-size: 16px; line-height: 1.5; font-family: 'Segoe UI', Arial, sans-serif;">
                                        <strong>Großartig!</strong> Ein neuer Termin wurde erfolgreich in Ihrem System erstellt. Alle relevanten Details finden Sie unten aufgelistet.
                                    </p>
                                </td>
                            </tr>
                        </table>

                        <!-- Appointment details section -->
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-bottom: 25px; background-color: #f8fafc; border: 1px solid #e2e8f0;">
                            <tr>
                                <td style="padding: 25px;">

                                    <!-- Section header -->
                                    <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                                        <tr>
                                            <td style="padding-bottom: 20px;">
                                                <h3 style="margin: 0; padding: 0; color: #1a202c; font-size: 18px; font-weight: 600; font-family: 'Segoe UI', Arial, sans-serif;">Termindetails</h3>
                                            </td>
                                        </tr>
                                    </table>

                                    <!-- Detail rows -->
                                    <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                                        <tr>
                                            <td width="160" style="padding: 0 15px 12px 0; font-weight: 600; color: #4a5568; font-size: 14px; font-family: 'Segoe UI', Arial, sans-serif; vertical-align: top;">Bewerber:</td>
                                            <td style="padding: 0 0 12px 0; font-weight: 500; color: #1a202c; font-size: 14px; font-family: 'Segoe UI', Arial, sans-serif;">{{applicantName}}</td>
                                        </tr>
                                        <tr>
                                            <td width="160" style="padding: 0 15px 12px 0; font-weight: 600; color: #4a5568; font-size: 14px; font-family: 'Segoe UI', Arial, sans-serif; vertical-align: top;">Messe:</td>
                                            <td style="padding: 0 0 12px 0; font-weight: 500; color: #1a202c; font-size: 14px; font-family: 'Segoe UI', Arial, sans-serif;">{{fairName}}</td>
                                        </tr>
                                        <tr>
                                            <td width="160" style="padding: 0 15px 12px 0; font-weight: 600; color: #4a5568; font-size: 14px; font-family: 'Segoe UI', Arial, sans-serif; vertical-align: top;">Kontaktperson:</td>
                                            <td style="padding: 0 0 12px 0; font-weight: 500; color: #1a202c; font-size: 14px; font-family: 'Segoe UI', Arial, sans-serif;">{{contactPersonName}}</td>
                                        </tr>
                                        <tr>
                                            <td width="160" style="padding: 0 15px 12px 0; font-weight: 600; color: #4a5568; font-size: 14px; font-family: 'Segoe UI', Arial, sans-serif; vertical-align: top;">Termin:</td>
                                            <td style="padding: 0 0 12px 0; font-weight: 500; color: #1a202c; font-size: 14px; font-family: 'Segoe UI', Arial, sans-serif;">{{appointmentDate}}</td>
                                        </tr>
                                        <tr>
                                            <td width="160" style="padding: 0 15px 0 0; font-weight: 600; color: #4a5568; font-size: 14px; font-family: 'Segoe UI', Arial, sans-serif; vertical-align: top;">Status:</td>
                                            <td style="padding: 0;">
                                                <table role="presentation" cellpadding="0" cellspacing="0" border="0">
                                                    <tr>
                                                        <td style="background-color: #fef3c7; color: #92400e; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; padding: 4px 12px; font-family: 'Segoe UI', Arial, sans-serif;">{{appointmentStatus}}</td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>

                                </td>
                            </tr>
                        </table>

                        <!-- Bulletproof CTA Button -->
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin: 35px 0 25px 0;">
                            <tr>
                                <td align="center">
                                    <table role="presentation" cellpadding="0" cellspacing="0" border="0">
                                        <tr>
                                            <td style="background-color: #e32652; border-radius: 8px;">
                                                <a href="{{appointmentLink}}" style="display: inline-block; padding: 16px 32px; color: #ffffff; text-decoration: none; font-weight: 600; font-size: 16px; line-height: 1; font-family: 'Segoe UI', Arial, sans-serif; border-radius: 8px;">Termin im Dashboard ansehen</a>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>

                        <!-- Important notice -->
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%" style="margin-bottom: 25px; background-color: #fffbeb; border: 1px solid #fbbf24;">
                            <tr>
                                <td style="padding: 16px 20px;">
                                    <p style="margin: 0; padding: 0; color: #92400e; font-size: 14px; line-height: 1.5; font-family: 'Segoe UI', Arial, sans-serif;">
                                        <strong>Wichtiger Hinweis:</strong> Diese E-Mail wurde automatisch generiert. Bei Änderungen oder Fragen wenden Sie sich bitte direkt über die Bridge-App an den Bewerber.
                                    </p>
                                </td>
                            </tr>
                        </table>

                    </td>
                </tr>

                <!-- Footer section -->
                <tr>
                    <td align="center" style="padding: 40px 40px 40px 40px; background-color: #ffffff;" class="mobile-padding">

                        <!-- Logo -->
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                            <tr>
                                <td align="center" style="padding-bottom: 20px;">
                                    <img src="https://firebasestorage.googleapis.com/v0/b/bridge-public-v2/o/dist%2Fimages%2Fbridge-logo-fae1080e.png?alt=media&token=a0a04c7d-33b4-44bb-b97a-fd1a96a493e3" alt="Bridge Logo" width="120" style="display: block; max-width: 120px; height: auto; opacity: 0.8;" />
                                </td>
                            </tr>
                        </table>

                        <!-- Footer text -->
                        <table role="presentation" cellpadding="0" cellspacing="0" border="0" width="100%">
                            <tr>
                                <td align="center">
                                    <p style="margin: 0; padding: 0; color: #718096; font-size: 14px; line-height: 1.5; font-family: 'Segoe UI', Arial, sans-serif;">
                                        Diese E-Mail wurde automatisch von der Bridge App gesendet.
                                    </p>
                                    <p style="margin: 20px 0 0 0; padding: 0; color: #718096; font-size: 14px; line-height: 1.5; font-family: 'Segoe UI', Arial, sans-serif;">
                                        <strong>Berufe4you UG</strong><br/>
                                        Logistikpark 7<br/>
                                        95448 Bayreuth
                                    </p>
                                    <p style="margin: 20px 0 0 0; padding: 0; color: #718096; font-size: 14px; line-height: 1.5; font-family: 'Segoe UI', Arial, sans-serif;">
                                        <a href="mailto:<EMAIL>" style="color: #e32652; text-decoration: none;"><EMAIL></a>
                                    </p>
                                </td>
                            </tr>
                        </table>

                    </td>
                </tr>

            </table>

        </td>
    </tr>
</table>
</body>
</html>