import { ObjectType, Field } from '@nestjs/graphql'
import { ApplicantDocument } from '../../applicant-document/entities/applicant-document.entity'
import { JobAction } from '../../job-actions/entities/job-action.entity'
import { JobsFilter } from '../../jobs-filter/entities/jobs-filter.entity'
import { DeviceToken } from '../../device-token/entities/device-token.entity'

@ObjectType()
export class Applicant {
    @Field()
    id: string

    @Field({ nullable: true })
    userId?: string

    @Field({ nullable: true })
    availableFrom?: Date

    @Field({ nullable: true })
    birthDate?: Date

    @Field({ nullable: true })
    city?: string

    @Field({ nullable: true })
    description?: string

    @Field({ nullable: true })
    phoneNumber?: string

    @Field({ nullable: true })
    environment?: number

    @Field({ nullable: true })
    firstName?: string

    @Field({ nullable: true })
    lastName?: string

    @Field({ nullable: true })
    graduation?: string

    @Field({ nullable: true })
    lastActive: Date

    @Field({ nullable: true })
    profileImageUrl?: string

    @Field({ nullable: true })
    personality?: number

    @Field({ nullable: true })
    receiveNotifications: boolean

    @Field({ nullable: true })
    schoolName: string

    @Field(() => [String], { nullable: true })
    strengths: string[]

    @Field(() => [String], { nullable: true })
    subjects: string[]

    @Field(() => [String], { nullable: true })
    weaknesses: string[]

    @Field(() => [JobAction], { nullable: true })
    jobAction: JobAction[]

    @Field(() => JobsFilter, { nullable: true })
    jobsFilter: JobsFilter

    @Field(() => [ApplicantDocument], { nullable: true })
    applicantDocuments: ApplicantDocument[]

    @Field(() => [DeviceToken], { nullable: true })
    deviceTokens: DeviceToken[]
}
