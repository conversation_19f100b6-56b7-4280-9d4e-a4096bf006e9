import { InputType, Field, Int } from '@nestjs/graphql'
import {
    IsNumber,
    IsString,
    IsBoolean,
    IsArray,
    IsOptional,
    IsDate,
} from 'class-validator'

@InputType()
export class CreateApplicantInput {
    @IsDate()
    @IsOptional()
    @Field({ nullable: true })
    availableFrom?: Date

    @IsDate()
    @IsOptional()
    @Field({ nullable: true })
    birthDate?: Date

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    city?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    description?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    firstName?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    lastActive?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    lastName?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    graduation?: string

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    profileImageUrl?: string

    @IsBoolean()
    @IsOptional()
    @Field({ nullable: true })
    receiveNotifications?: boolean

    @IsString()
    @IsOptional()
    @Field({ nullable: true })
    schoolName?: string

    @Field(() => [String], { nullable: true })
    @IsArray()
    @IsOptional()
    @IsString({ each: true })
    subjects?: string[]

    @Field(() => [String], { nullable: true })
    @IsArray()
    @IsOptional()
    @IsString({ each: true })
    strengths?: string[]

    @Field(() => [String], { nullable: true })
    @IsArray()
    @IsOptional()
    @IsString({ each: true })
    weaknesses?: string[]

    @Field(() => Int, { nullable: true })
    @IsNumber()
    @IsOptional()
    personality?: number

    @Field(() => Int, { nullable: true })
    @IsNumber()
    @IsOptional()
    environment?: number
}
