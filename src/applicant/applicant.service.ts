import { Injectable, NotFoundException } from '@nestjs/common'
import { CreateApplicantInput } from './dto/create-applicant.input'
import { UpdateApplicantInput } from './dto/update-applicant.input'
import { PrismaService } from '../prisma.service'
import { Applicant } from '@prisma/client'
import { admin } from '../auth/firebase-admin.module'
import { PaginationInput } from '../common/dto/pagination.input'
import { IPaginatedType } from '../common/dto/pagination-response'

@Injectable()
export class ApplicantService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        userId: string,
        createApplicantInput?: CreateApplicantInput
    ): Promise<Applicant> {
        const applicantExists = await this.prisma.applicant.findUnique({
            where: {
                userId: userId,
            },
            include: {
                applicantDocuments: true,
            },
        })

        if (applicantExists) {
            return applicantExists
        }

        return this.prisma.applicant.create({
            data: {
                ...createApplicantInput,
                user: {
                    connect: {
                        id: userId,
                    },
                },
            },
        })
    }

    async createOrUpdate(
        uid: string,
        userId?: string,
        createApplicantInput?: CreateApplicantInput
    ): Promise<Applicant> {
        let bUserId = userId ? userId : null

        if (!userId) {
            const findUser = await this.prisma.user.findUnique({
                where: {
                    firebaseUid: uid,
                },
            })

            if (findUser) {
                bUserId = findUser.id
            }
        }

        const applicantExists = await this.prisma.applicant.findUnique({
            where: {
                userId: bUserId,
            },
        })

        if (applicantExists) {
            return this.update(applicantExists.id, {
                ...createApplicantInput,
            })
        } else {
            return this.create(bUserId, createApplicantInput)
        }
    }

    async findAll() {
        return this.prisma.applicant.findMany({
            include: {
                jobAction: true,
                applicantDocuments: true,
                deviceTokens: true,
                jobsFilter: true,
            },
        })
    }

    async findPaginated(
        paginationInput: PaginationInput
    ): Promise<IPaginatedType<any>> {
        const { page = 1, limit = 10, search = '' } = paginationInput
        const skip = (page - 1) * limit

        const where = search
            ? {
                  OR: [
                      {
                          firstName: {
                              contains: search,
                              mode: 'insensitive' as const,
                          },
                      },
                      {
                          lastName: {
                              contains: search,
                              mode: 'insensitive' as const,
                          },
                      },
                      {
                          city: {
                              contains: search,
                              mode: 'insensitive' as const,
                          },
                      },
                  ],
              }
            : {}

        const [items, totalItems] = await Promise.all([
            this.prisma.applicant.findMany({
                where,
                include: {
                    jobAction: true,
                    applicantDocuments: true,
                    deviceTokens: true,
                    jobsFilter: true,
                },
                skip,
                take: limit,
                orderBy: {
                    createdAt: 'desc',
                },
            }),
            this.prisma.applicant.count({
                where,
            }),
        ])

        const totalPages = Math.ceil(totalItems / limit)

        return {
            items,
            meta: {
                totalItems,
                itemCount: items.length,
                itemsPerPage: limit,
                totalPages,
                currentPage: page,
            },
        }
    }

    async findOne(
        applicantId: string,
        companyId: string,
        isApplicant: boolean,
        isSuperUser: boolean
    ): Promise<Applicant> {
        if (!isApplicant && !isSuperUser) {
            const jobAction = await this.prisma.jobAction.findFirst({
                where: {
                    applicantId: applicantId,
                    jobAdvert: {
                        companyId: companyId,
                    },
                    state: {
                        in: ['MATCHED', 'LIKED'],
                    },
                    deletedFromApplicant: false,
                    deletedFromCompany: false,
                },
            })

            if (!jobAction) {
                throw new Error(
                    'Sorry you are not authorized to access to this applicant.'
                )
            }
        }

        try {
            const applicantData = await this.prisma.applicant.findUnique({
                where: {
                    id: applicantId,
                },
                include: {
                    user: true,
                    jobAction: true,
                    deviceTokens: true,
                    jobsFilter: true,
                    applicantDocuments: true,
                },
            })

            const userData = await admin
                .auth()
                .getUser(applicantData.user.firebaseUid)

            if (!applicantData) {
                throw new NotFoundException(
                    `Applicant with ID ${applicantId} does not exist`
                )
            }

            return { ...applicantData, phoneNumber: userData?.phoneNumber }
        } catch (error) {
            throw new NotFoundException(error.message)
        }
    }

    async update(
        id: string,
        updateApplicantInput: UpdateApplicantInput
    ): Promise<Applicant> {
        return this.prisma.applicant.update({
            where: {
                id: id,
            },
            include: {
                jobAction: true,
                deviceTokens: true,
                jobsFilter: true,
                applicantDocuments: true,
            },
            data: updateApplicantInput,
        })
    }

    async isValidApplicant(
        checkApplicantId: string,
        applicantId: string
    ): Promise<boolean> {
        if (checkApplicantId && checkApplicantId !== applicantId) {
            throw new NotFoundException('Invalid applicant ID')
        }
        const applicant = await this.prisma.applicant.findUnique({
            where: {
                id: applicantId,
            },
        })
        if (!applicant) throw new NotFoundException('Applicant not found')

        return !!applicant
    }

    async remove(id: string) {
        const applicantToDelete = await this.prisma.applicant.findUnique({
            where: { id: id },
        })
        if (!applicantToDelete) {
            throw new NotFoundException(
                `Applicant with ID ${id} does not exist`
            )
        }
        let batchDeleted = null
        const deleteJobAction = this.prisma.jobAction.deleteMany({
            where: { applicantId: id },
        })

        const deleteDeviceTokens = this.prisma.deviceToken.deleteMany({
            where: { applicantId: id },
        })

        const deleteJobsFilter = this.prisma.jobsFilter.deleteMany({
            where: { applicantId: id },
        })

        const deleteApplicantDocuments =
            this.prisma.applicantDocument.deleteMany({
                where: { applicantId: id },
            })

        const deleteApplicant = this.prisma.applicant.delete({
            where: { id: id },
        })
        try {
            batchDeleted = await this.prisma.$transaction([
                deleteJobAction,
                deleteDeviceTokens,
                deleteJobsFilter,
                deleteApplicantDocuments,
                deleteApplicant,
            ])

            if (batchDeleted) {
                //TODO:: Send notification to the company that the applicant has been deleted
            }
        } catch (e) {
            throw new Error(`Error deleting applicant: ${e.message}`)
        }

        return applicantToDelete
    }
}
