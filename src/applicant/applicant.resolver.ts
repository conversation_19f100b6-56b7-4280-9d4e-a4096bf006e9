import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql'
import { ApplicantService } from './applicant.service'
import { CreateApplicantInput } from './dto/create-applicant.input'
import { UpdateApplicantInput } from './dto/update-applicant.input'
import { Applicant } from './entities/applicant.entity'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { UseGuards } from '@nestjs/common'
import { SuperGuard } from '../guards/super-guard'
import { ApplicantGuard } from '../guards/applicant-guard'
import { PaginationInput } from '../common/dto/pagination.input'
import { PaginatedApplicantsResponse } from './dto/paginated-applicants.response'

@Resolver(() => Applicant)
export class ApplicantResolver {
    constructor(private readonly applicantService: ApplicantService) { }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Applicant)
    createApplicant(
        @Context() context: any,
        @Args('createApplicantInput', { nullable: true })
        createApplicantInput?: CreateApplicantInput
    ) {
        const userId = context?.req?.user?.bridgeUserId
        return this.applicantService.create(userId, createApplicantInput)
    }

    @UseGuards(GraphqlAuthGuard)
    @Mutation(() => Applicant)
    setApplicant(
        @Context() context: any,
        @Args('createApplicantInput', { nullable: true })
        createApplicantInput?: CreateApplicantInput
    ) {
        const userId = context?.req?.user?.bridgeUserId
        const uid = context?.req?.user?.uid
        return this.applicantService.createOrUpdate(
            uid,
            userId,
            createApplicantInput
        )
    }

    @UseGuards(SuperGuard)
    @Query(() => [Applicant], { name: 'allApplicants' })
    findAll() {
        return this.applicantService.findAll()
    }

    @UseGuards(SuperGuard)
    @Query(() => PaginatedApplicantsResponse, { name: 'paginatedApplicants' })
    findPaginated(@Args('paginationInput', { nullable: true }) paginationInput?: PaginationInput) {
        return this.applicantService.findPaginated(paginationInput || { page: 1, limit: 10 })
    }

    @UseGuards(GraphqlAuthGuard)
    @Query(() => Applicant, { name: 'getApplicant' })
    findOne(
        @Context() context: any,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string
    ) {
        const companyId = context?.req?.user?.companyId
        const bApplicantId = context?.req?.user?.applicantId || applicantId
        const isApplicant = !!context?.req?.user?.applicantId
        const isSuperUser =
            !!context?.req?.user?.isSuperUser || !!context?.req?.user?.isFGAdmin
        return this.applicantService.findOne(
            bApplicantId,
            companyId,
            isApplicant,
            isSuperUser
        )
    }

    @UseGuards(ApplicantGuard)
    @Mutation(() => Applicant)
    updateApplicant(
        @Context() context: any,
        @Args('applicantId', { type: () => String, nullable: true })
        applicantId: string,
        @Args('updateApplicantInput') updateApplicantInput: UpdateApplicantInput
    ) {
        const id = context?.req?.user?.applicantId || applicantId

        return this.applicantService.update(id, updateApplicantInput)
    }

    @UseGuards(SuperGuard)
    @Mutation(() => Applicant)
    removeApplicant(
        @Context() context: any,
        @Args('id', { type: () => String, nullable: true }) applicantId: string
    ) {
        const id = context?.req?.user?.applicantId || applicantId
        return this.applicantService.remove(id)
    }
}
