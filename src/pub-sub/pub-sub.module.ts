import {
    forwardRef,
    Module,
    MiddlewareConsumer,
    NestModule,
} from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { PubSub } from 'graphql-subscriptions'
import { PubSubController } from './pub-sub.controller'
import { json, urlencoded } from 'express'
import { PusherPubSubModule } from './pusher-pub-sub.module'
import { ChannelAuthorizerService } from './channel-authorizer.service'
import { ChatRoomModule } from '../chat-room/chat-room.module'
import { CompanyModule } from '../company/company.module'
import { ApplicantModule } from '../applicant/applicant.module'
import { JobActionsModule } from '../job-actions/job-actions.module'
import { ContactPersonModule } from '../contact-person/contact-person.module'

@Module({
    imports: [
        ConfigModule,
        PusherPubSubModule,
        CompanyModule,
        ApplicantModule,
        ContactPersonModule,
        forwardRef(() => JobActionsModule),
        forwardRef(() => ChatRoomModule),
    ],
    providers: [
        {
            provide: 'PUB_SUB',
            useFactory: () => {
                return new PubSub() // Use the in-memory PubSub
            },
        },
        ChannelAuthorizerService,
    ],
    exports: ['PUB_SUB'],
    controllers: [PubSubController],
})
export class PubSubModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer
            .apply(json(), urlencoded({ extended: true }))
            .forRoutes(PubSubController)
    }
}
