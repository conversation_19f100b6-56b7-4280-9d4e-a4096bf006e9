import { Injectable } from '@nestjs/common'
import * as Pusher from 'pusher'
import configs from '../config'

@Injectable()
export class PusherPubSubService {
    private readonly pusher: Pusher
    constructor() {
        this.pusher = new Pusher({
            appId: configs().PUSHER.PUSHER_APP_ID,
            key: configs().PUSHER.PUSHER_KEY,
            secret: configs().PUSHER.PUSHER_SECRET,
            cluster: configs().PUSHER.PUSHER_CLUSTER,
            useTLS: true,
        })
    }

    getPusher(): Pusher {
        return this.pusher
    }

    async publish(channel: string, event: string, data: any): Promise<void> {
        try {
            await this.pusher.trigger(channel, event, data)
        } catch (error) {
            throw new Error(`Failed to publish event: ${error.message}`)
        }
    }
}
