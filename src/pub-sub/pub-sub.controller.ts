import {
    Controller,
    Post,
    Headers,
    Body,
    UnauthorizedException,
    Header,
    HttpCode,
} from '@nestjs/common'
import * as admin from 'firebase-admin'
import * as Pusher from 'pusher'
import { PusherPubSubService } from './pusher-pub-sub.service'
import { ChannelAuthorizerService } from './channel-authorizer.service'

@Controller('pusher')
export class PubSubController {
    private pusher: Pusher

    constructor(
        private readonly pusherPubSubService: PusherPubSubService,
        private readonly channelAuthorizer: ChannelAuthorizerService
    ) {
        this.pusher = this.pusherPubSubService.getPusher()
    }

    @Post('auth')
    @Header('Content-Type', 'application/json')
    @HttpCode(200)
    async authenticatePusher(
        @Headers('Authorization') authHeader: string,
        @Body('socket_id') socketId: string,
        @Body('channel_name') channelName: string
    ): Promise<any> {
        const token = authHeader?.split(' ')[1]

        if (!token) {
            throw new UnauthorizedException('No token provided')
        }
        if (!socketId) {
            console.log('Missing socket_id in request body')
            throw new UnauthorizedException('Missing required parameters')
        }

        try {
            const user = await admin.auth().verifyIdToken(token)
            const hasAccess = await this.channelAuthorizer.authorize(
                user,
                channelName
            )
            if (!user) {
                throw new UnauthorizedException('Invalid token')
            }

            //if user isSuperUser || user.isFGAdmin return true
            if (user.isSuperUser || user.isFGAdmin) {
                return this.pusher.authorizeChannel(socketId, channelName)
            }

            if (!hasAccess) {
                console.log(
                    `User ${user.uid} attempted to access unauthorized channel: ${channelName}`
                )
                throw new UnauthorizedException('Channel access denied')
            }

            const authResponse = {
                auth: `${channelName}`,
                user_info: {
                    id: user.uid,
                    name: user.name,
                },
            }

            if (channelName.startsWith('private-')) {
                return this.pusher.authorizeChannel(socketId, channelName)
            }

            return authResponse
        } catch (error) {
            console.error('Failed to authenticate Pusher:', error)
            throw new UnauthorizedException('Token verification failed')
        }
    }
}
