import { Injectable } from '@nestjs/common'
import { ChatRoomService } from '../chat-room/chat-room.service'
import { CompanyService } from '../company/company.service'
import { ApplicantService } from '../applicant/applicant.service'
import { JobActionsService } from '../job-actions/job-actions.service'
import { ContactPersonService } from '../contact-person/contact-person.service'

export interface User {
    uid: string
    name?: string
    applicantId?: string
    companyUserId?: string
    companyId?: string
    isSuperUser?: boolean
    isFGAdmin?: boolean
}

@Injectable()
export class ChannelAuthorizerService {
    private readonly channelHandlers: Record<
        string,
        (user: User, id: string) => Promise<boolean>
    > = {
        liveChatMessages: (user, chatRoomId) =>
            this.canListenOnChatRoom(user, chatRoomId),
        liveCompanyJobAdverts: (user, companyId) =>
            this.hasCompanyAccess(user, companyId),
        liveNotifications: (user, companyUserId) =>
            this.hasCompanyUserAccess(user, companyUserId),
        liveCompanyJobActions: (user, companyId) =>
            this.hasCompanyAccess(user, companyId),
        liveApplicantJobActions: (user, applicantId) =>
            this.hasApplicantAccess(user, applicantId),
        liveJobAdvertActions: (user, jobAdvertId) =>
            this.canListenOnJobAdvert(user, jobAdvertId),
        liveContactPersonAppointments: (user) =>
            this.hasContactPersonAccess(user),
        liveApplicantAppointments: (user, applicantId) =>
            this.hasApplicantAccess(user, applicantId),
    }
    constructor(
        private readonly companyService: CompanyService,
        private readonly chatRoomService: ChatRoomService,
        private readonly applicantService: ApplicantService,
        private readonly jobActionsService: JobActionsService
    ) {}

    async authorize(user: User, channelName: string): Promise<boolean> {
        const [prefix, rest] = channelName.split('-', 2)
        if (prefix !== 'private') {
            return false
        }
        const type = rest.split('.', 2)[0]
        const id = channelName.split('.', 2)[1]

        if (user?.isFGAdmin) return true

        if (id) {
            return await this.channelHandlers[type](user, id)
        }

        return false
    }

    private async canListenOnChatRoom(
        user: User,
        chatRoomId: string
    ): Promise<boolean> {
        if (this.isSuperUser(user)) {
            return true
        }

        if (!this.hasValidCredentials(user, chatRoomId)) {
            return false
        }

        const isAppointmentChatRoom =
            await this.chatRoomService.isAppointmentChatRoom(chatRoomId)
        if (isAppointmentChatRoom) {
            return this.validateAppointmentChatRoomAccess(user)
        }

        return this.chatRoomService.userHasAccessToChatRoom(
            this.isSuperUser(user),
            chatRoomId,
            user.applicantId,
            user.companyUserId
        )
    }

    private isSuperUser(user: User): boolean {
        return user.isSuperUser || user?.isFGAdmin
    }

    private hasValidCredentials(user: User, chatRoomId: string): boolean {
        return !!chatRoomId && (!!user.applicantId || !!user.companyUserId)
    }

    private async validateAppointmentChatRoomAccess(
        user: User
    ): Promise<boolean> {
        if (user.companyUserId) {
            return this.companyService.isValidCompanyUser(
                user.companyUserId,
                user.companyId
            )
        }

        if (user.applicantId) {
            return this.applicantService.isValidApplicant(
                user.applicantId,
                user.applicantId
            )
        }

        return false
    }

    // private async canListenOnChatRoom(
    //     user: User,
    //     chatRoomId: string
    // ): Promise<boolean> {
    //     const isSuper = user.isSuper || user?.isFGAdmin
    //     if (isSuper) {
    //         return true
    //     }
    //
    //     if (!chatRoomId || (!user.applicantId && !user.companyUserId)) {
    //         return false
    //     }
    //
    //     const isAppointmentChatRoom =
    //         await this.chatRoomService.isAppointmentChatRoom(chatRoomId)
    //     if (isAppointmentChatRoom) {
    //         if (user.companyUserId) {
    //             return this.companyService.isValidCompanyUser(
    //                 user.companyUserId,
    //                 user.companyId
    //             )
    //         } else if (user.applicantId) {
    //             return this.applicantService.isValidApplicant(
    //                 user.applicantId,
    //                 user.applicantId
    //             )
    //         }
    //     }
    //
    //     return this.chatRoomService.userHasAccessToChatRoom(
    //         isSuper,
    //         chatRoomId,
    //         user.applicantId,
    //         user.companyUserId
    //     )
    // }

    private async hasCompanyUserAccess(
        user: User,
        companyUserId: string
    ): Promise<boolean> {
        if (user.isSuperUser || user.isFGAdmin) return true
        return this.companyService.isValidCompanyUser(
            companyUserId,
            user.companyId
        )
    }
    private async hasCompanyAccess(
        user: User,
        companyId: string
    ): Promise<boolean> {
        if (user.isSuperUser || user.isFGAdmin) return true
        return this.companyService.isValidCompany(companyId, user.companyId)
    }
    private async hasApplicantAccess(
        user: User,
        applicantId: string
    ): Promise<boolean> {
        return await this.applicantService.isValidApplicant(
            applicantId,
            user.applicantId
        )
    }
    private async canListenOnJobAdvert(
        user: User,
        jobAdvertId: string
    ): Promise<boolean> {
        return this.jobActionsService.canAccessJobAction(
            user.companyUserId,
            jobAdvertId,
            user.isSuperUser
        )
    }

    private async hasContactPersonAccess(user: User): Promise<boolean> {
        if (user.isSuperUser || user.isFGAdmin) return true
    }
}
