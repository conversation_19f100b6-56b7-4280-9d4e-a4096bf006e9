import { ObjectType, Field } from '@nestjs/graphql'
import { User } from '../../user/entities/user.entity'

@ObjectType()
export class SuperUser {
    @Field()
    id: string

    @Field({ nullable: true })
    email: string

    @Field({ nullable: true })
    name: string

    @Field({ nullable: true })
    avatarImageUrl: string

    @Field()
    createdAt: Date

    @Field()
    updatedAt: Date

    @Field(() => User)
    user: User

    @Field()
    userId: string
}
