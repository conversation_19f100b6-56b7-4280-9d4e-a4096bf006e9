import { Test, TestingModule } from '@nestjs/testing'
import { SuperUserResolver } from './super-user.resolver'
import { SuperUserService } from './super-user.service'

describe('SuperUserResolver', () => {
    let resolver: SuperUserResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [SuperUserResolver, SuperUserService],
        }).compile()

        resolver = module.get<SuperUserResolver>(SuperUserResolver)
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
