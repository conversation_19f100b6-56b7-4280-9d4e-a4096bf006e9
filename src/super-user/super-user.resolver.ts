import { Resolver, Mutation, Args, Context } from '@nestjs/graphql'
import { SuperUserService } from './super-user.service'
import { SuperUser } from './entities/super-user.entity'
import { CreateSuperUserInput } from './dto/create-super-user.input'
import { UseGuards } from '@nestjs/common'
import { SuperGuard } from '../guards/super-guard'

@Resolver(() => SuperUser)
export class SuperUserResolver {
    constructor(private readonly superUserService: SuperUserService) {}

    @UseGuards(SuperGuard)
    @Mutation(() => SuperUser)
    createSuperUser(
        @Context() context: any,
        @Args('createSuperUserInput')
        createSuperUserInput: CreateSuperUserInput,
        @Args('userId', { type: () => String }) userId: string
    ) {
        return this.superUserService.create(userId, createSuperUserInput)
    }

    @Mutation(() => SuperUser)
    verifySuperUser(@Args('email', { type: () => String }) email: string) {
        return this.superUserService.verifySuperUser(email)
    }
}
