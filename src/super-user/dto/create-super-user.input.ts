import { InputType, Field } from '@nestjs/graphql'
import { IsEmail, IsOptional, IsString } from 'class-validator'

@InputType()
export class CreateSuperUserInput {
    @Field()
    @IsEmail()
    @IsString()
    email: string

    @Field({ nullable: true })
    @IsString()
    @IsOptional()
    name?: string

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    avatarImageUrl?: string
}
