import { Injectable } from '@nestjs/common'
import { CreateSuperUserInput } from './dto/create-super-user.input'
import { PrismaService } from '../prisma.service'
import { admin } from '../auth/firebase-admin.module'

@Injectable()
export class SuperUserService {
    constructor(private readonly prisma: PrismaService) {}

    async create(userId: string, createSuperUserInput: CreateSuperUserInput) {
        const superUserExists = await this.prisma.superUser.findUnique({
            where: {
                userId: userId,
            },
        })
        if (superUserExists) {
            throw new Error('Super User already exists')
        }

        const bridgeUser = await this.prisma.user.findUnique({
            where: {
                id: userId,
            },
        })

        const newSuperUser = await this.prisma.superUser.create({
            data: {
                ...createSuperUserInput,
                user: {
                    connect: {
                        id: userId,
                    },
                },
            },
            include: {
                user: true,
            },
        })

        const userMetaData = {
            superUserId: newSuperUser?.id,
            isSuperUser: true,
            bridgeUserId: bridgeUser?.id,
        }

        //setClaims
        await admin.auth().setCustomUserClaims(bridgeUser?.firebaseUid, {
            ...userMetaData,
        })

        return newSuperUser
    }

    async verifySuperUser(email: string) {
        const superUser = this.prisma.superUser.findUnique({
            where: {
                email: email,
            },
        })

        if (!superUser) {
            throw new Error('Super User not found')
        }

        return superUser
    }
}
