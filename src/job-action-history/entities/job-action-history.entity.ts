import { ObjectType, Field } from '@nestjs/graphql'
import { JobAction } from '../../job-actions/entities/job-action.entity'

@ObjectType()
export class JobActionHistory {
    @Field()
    id: string

    @Field({ nullable: true })
    prevState?: string

    @Field({ nullable: true })
    newState?: string

    @Field(() => JobAction)
    jobAction: JobAction

    @Field({ nullable: true })
    createdAt?: Date

    @Field({ nullable: true })
    updatedAt?: Date
}
