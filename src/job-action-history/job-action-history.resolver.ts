import { Resolver, Query, Mutation, Args } from '@nestjs/graphql'
import { JobActionHistoryService } from './job-action-history.service'
import { JobActionHistory } from './entities/job-action-history.entity'
import { CreateJobActionHistoryInput } from './dto/create-job-action-history.input'
import { UseGuards } from '@nestjs/common'
import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import { SuperGuard } from '../guards/super-guard'

@UseGuards(GraphqlAuthGuard)
@Resolver(() => JobActionHistory)
export class JobActionHistoryResolver {
    constructor(
        private readonly jobActionHistoryService: JobActionHistoryService
    ) {}

    @Mutation(() => JobActionHistory)
    createJobActionHistory(
        @Args('createJobActionHistoryInput')
        createJobActionHistoryInput: CreateJobActionHistoryInput,
        @Args('jobActionId', { type: () => String }) jobActionId: string
    ) {
        return this.jobActionHistoryService.create(
            jobActionId,
            createJobActionHistoryInput
        )
    }

    @UseGuards(SuperGuard)
    @Query(() => [JobActionHistory], { name: 'jobActionHistory' })
    findAll() {
        return this.jobActionHistoryService.findAll()
    }
}
