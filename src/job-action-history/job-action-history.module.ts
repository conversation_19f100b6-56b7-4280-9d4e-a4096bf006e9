import { Module } from '@nestjs/common'
import { JobActionHistoryService } from './job-action-history.service'
import { JobActionHistoryResolver } from './job-action-history.resolver'
import { AuthModule } from '../auth/auth.module'

@Module({
    imports: [AuthModule],
    exports: [JobActionHistoryService],
    providers: [JobActionHistoryResolver, JobActionHistoryService],
})
export class JobActionHistoryModule {}
