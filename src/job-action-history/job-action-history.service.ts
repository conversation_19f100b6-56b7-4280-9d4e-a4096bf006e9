import { Injectable } from '@nestjs/common'
import { CreateJobActionHistoryInput } from './dto/create-job-action-history.input'
import { PrismaService } from '../prisma.service'

@Injectable()
export class JobActionHistoryService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        jobActionId: string,
        createJobActionHistoryInput: CreateJobActionHistoryInput
    ) {
        return this.prisma.jobActionHistory.create({
            data: {
                ...createJobActionHistoryInput,
                jobAction: {
                    connect: {
                        id: jobActionId,
                    },
                },
            },
            include: {
                jobAction: true,
            },
        })
    }

    findAll() {
        return this.prisma.jobActionHistory.findMany({
            include: {
                jobAction: true,
            },
        })
    }
}
