import { Test, TestingModule } from '@nestjs/testing'
import { JobActionHistoryResolver } from './job-action-history.resolver'
import { JobActionHistoryService } from './job-action-history.service'

describe('JobActionHistoryResolver', () => {
    let resolver: JobActionHistoryResolver

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [JobActionHistoryResolver, JobActionHistoryService],
        }).compile()

        resolver = module.get<JobActionHistoryResolver>(
            JobActionHistoryResolver
        )
    })

    it('should be defined', () => {
        expect(resolver).toBeDefined()
    })
})
