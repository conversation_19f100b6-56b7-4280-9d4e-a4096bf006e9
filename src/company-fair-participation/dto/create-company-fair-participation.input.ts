import { InputType, Field } from '@nestjs/graphql'
import { IsString, IsOptional, IsArray } from 'class-validator'

@InputType()
export class CreateCompanyFairParticipationInput {
    @IsString()
    @Field()
    companyId: string

    @IsString()
    @Field()
    fairId: string

    @IsString({ each: true })
    @Field(() => [String])
    categoryIds: string[]

    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    @Field(() => [String], { nullable: true })
    partnerLinkIds?: string[]
}
