import { Injectable, NotFoundException } from '@nestjs/common'
import { PrismaService } from '../prisma.service'
import { CreateCompanyFairParticipationInput } from './dto/create-company-fair-participation.input'
import { UpdateCompanyFairParticipationInput } from './dto/update-company-fair-participation.input'

@Injectable()
export class CompanyFairParticipationService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        createCompanyFairParticipationInput: CreateCompanyFairParticipationInput
    ) {
        const { companyId, fairId } = createCompanyFairParticipationInput

        const existingParticipation =
            await this.prisma.companyFairParticipation.findFirst({
                where: {
                    companyId,
                    fairId,
                },
            })

        if (existingParticipation) {
            throw new Error('Company is already participating in this fair')
        }

        return this.prisma.companyFairParticipation.create({
            data: {
                company: {
                    connect: { id: companyId },
                },
                fair: {
                    connect: { id: fairId },
                },
                categories: {
                    connect:
                        createCompanyFairParticipationInput.categoryIds?.map(
                            (id) => ({ id })
                        ),
                },
                partnerLinks: {
                    connect:
                        createCompanyFairParticipationInput.partnerLinkIds?.map(
                            (id) => ({ id })
                        ),
                },
            },
            include: {
                company: true,
                fair: true,
                categories: true,
                partnerLinks: true,
            },
        })
    }

    async update(
        id: string,
        updateCompanyFairParticipationInput: UpdateCompanyFairParticipationInput
    ) {
        const existingParticipation =
            await this.prisma.companyFairParticipation.findUnique({
                where: { id },
                include: { categories: true, partnerLinks: true },
            })

        if (!existingParticipation) {
            throw new NotFoundException('Company fair participation not found')
        }

        const { categoryIds, partnerLinkIds } =
            updateCompanyFairParticipationInput

        // Build the data object conditionally
        const data: any = {}

        if (Array.isArray(categoryIds)) {
            data.categories = {
                disconnect: existingParticipation.categories.map(
                    (category) => ({ id: category.id })
                ),
                connect: categoryIds.map((id) => ({ id })),
            }
        }

        if (Array.isArray(partnerLinkIds)) {
            data.partnerLinks = {
                disconnect: existingParticipation.partnerLinks.map(
                    (partnerLink) => ({ id: partnerLink.id })
                ),
                connect: partnerLinkIds.map((id) => ({ id })),
            }
        }

        return this.prisma.companyFairParticipation.update({
            where: { id },
            data,
            include: {
                company: true,
                fair: true,
                categories: true,
                partnerLinks: true,
            },
        })
    }

    async findAll() {
        return this.prisma.companyFairParticipation.findMany({
            include: {
                company: true,
                fair: true,
            },
        })
    }

    async findOne(id: string) {
        const participation =
            await this.prisma.companyFairParticipation.findUnique({
                where: { id },
                include: this.participationIncludes(),
            })

        if (!participation) {
            throw new Error('Company fair participation not found')
        }

        return participation
    }

    async removeByCompanyAndFair(companyId: string, fairId: string) {
        const participation =
            await this.prisma.companyFairParticipation.findFirst({
                where: {
                    companyId,
                    fairId,
                },
                include: this.participationIncludes(),
            })

        if (!participation) {
            throw new NotFoundException('Company fair participation not found')
        }

        // Check if there are associated companyFairJobs or companyFairContactPersons
        const hasJobs =
            participation.companyFairJobs &&
            participation.companyFairJobs.length > 0
        const hasContactPersons =
            participation.companyFairContactPersons &&
            participation.companyFairContactPersons.length > 0

        if (hasJobs || hasContactPersons) {
            let errorMessage =
                'Cannot delete this company participation because it has '

            if (hasJobs) {
                errorMessage += `${participation.companyFairJobs.length} associated Fair Job(s)`
            }

            if (hasJobs && hasContactPersons) {
                errorMessage += ' and '
            }

            if (hasContactPersons) {
                errorMessage += `${participation.companyFairContactPersons.length} associated Contact Person(s)`
            }

            errorMessage +=
                '. Please remove all associated Fair Jobs and Contact Persons first.'

            throw new Error(errorMessage)
        }

        await this.prisma.companyFairParticipation.deleteMany({
            where: {
                companyId,
                fairId,
            },
        })

        // Return the participation that was deleted
        return participation
    }

    async remove(id: string) {
        const participation =
            await this.prisma.companyFairParticipation.findUnique({
                where: { id },
                include: this.participationIncludes(),
            })

        if (!participation) {
            throw new NotFoundException('Company fair participation not found')
        }

        const hasJobs =
            participation.companyFairJobs &&
            participation.companyFairJobs.length > 0
        const hasContactPersons =
            participation.companyFairContactPersons &&
            participation.companyFairContactPersons.length > 0

        if (hasJobs || hasContactPersons) {
            let errorMessage =
                'Cannot delete this company participation because it has '

            if (hasJobs) {
                errorMessage += `${participation.companyFairJobs.length} associated Fair Job(s)`
            }

            if (hasJobs && hasContactPersons) {
                errorMessage += ' and '
            }

            if (hasContactPersons) {
                errorMessage += `${participation.companyFairContactPersons.length} associated Contact Person(s)`
            }

            errorMessage +=
                '. Please remove all associated Fair Jobs and Contact Persons first.'

            throw new Error(errorMessage)
        }

        return this.prisma.companyFairParticipation.delete({
            where: { id },
        })
    }

    private participationIncludes() {
        return {
            company: true,
            fair: {
                include: {
                    fairDays: true,
                },
            },
            categories: true,
            partnerLinks: true,
            companyFairJobs: {
                include: {
                    fairJob: {
                        select: {
                            title: true,
                        },
                    },
                },
            },
            companyFairContactPersons: {
                include: {
                    contactPerson: true,
                    ContactPersonTimeslot: {
                        include: {
                            Appointment: {
                                include: {
                                    applicant: true,
                                },
                            },
                        },
                    },
                },
            },
        }
    }

    async findByCompany(companyId: string) {
        return this.prisma.companyFairParticipation.findMany({
            where: { companyId },
            include: this.participationIncludes(),
        })
    }

    async findByFair(fairId: string) {
        const participations =
            await this.prisma.companyFairParticipation.findMany({
                where: { fairId },
                include: this.participationIncludes(),
            })

        return participations
    }
    async findByFairAndCompany(fairId: string, companyId: string) {
        return this.prisma.companyFairParticipation.findFirst({
            where: {
                fairId,
                companyId,
            },
            include: this.participationIncludes(),
        })
    }
}
