import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql'
import { CompanyFairParticipationService } from './company-fair-participation.service'
import { CompanyFairParticipation } from './entities/company-fair-participation.entity'
import { CreateCompanyFairParticipationInput } from './dto/create-company-fair-participation.input'
import { UpdateCompanyFairParticipationInput } from './dto/update-company-fair-participation.input'

@Resolver(() => CompanyFairParticipation)
export class CompanyFairParticipationResolver {
    constructor(
        private readonly companyFairParticipationService: CompanyFairParticipationService
    ) {}

    @Mutation(() => CompanyFairParticipation)
    createCompanyFairParticipation(
        @Args('createCompanyFairParticipationInput')
        createCompanyFairParticipationInput: CreateCompanyFairParticipationInput
    ) {
        return this.companyFairParticipationService.create(
            createCompanyFairParticipationInput
        )
    }

    //Mutation to update company fair participation
    @Mutation(() => CompanyFairParticipation)
    updateCompanyFairParticipation(
        @Args('updateCompanyFairParticipationInput')
        updateCompanyFairParticipationInput: UpdateCompanyFairParticipationInput
    ) {
        return this.companyFairParticipationService.update(
            updateCompanyFairParticipationInput.id,
            updateCompanyFairParticipationInput
        )
    }

    @Query(() => [CompanyFairParticipation], {
        name: 'allCompanyFairParticipation',
    })
    findAll() {
        return this.companyFairParticipationService.findAll()
    }

    @Query(() => CompanyFairParticipation, {
        name: 'getCompanyFairParticipation',
    })
    findOne(@Args('id', { type: () => String }) id: string) {
        return this.companyFairParticipationService.findOne(id)
    }

    @Mutation(() => CompanyFairParticipation)
    removeCompanyFromFair(
        @Args('companyId', { type: () => String }) companyId: string,
        @Args('fairId', { type: () => String }) fairId: string
    ) {
        return this.companyFairParticipationService.removeByCompanyAndFair(
            companyId,
            fairId
        )
    }

    @Query(() => [CompanyFairParticipation], { name: 'findFairByCompanyId' })
    findParticipationByCompanyId(
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        return this.companyFairParticipationService.findByCompany(companyId)
    }

    @Query(() => [CompanyFairParticipation], {
        name: 'findParticipationByFairId',
    })
    findParticipationByFairId(
        @Args('fairId', { type: () => String }) fairId: string
    ) {
        console.log('FairID::', fairId)
        return this.companyFairParticipationService.findByFair(fairId)
    }

    @Query(() => CompanyFairParticipation, {
        name: 'findParticipationByFairAndCompany',
    })
    findParticipationByFairAndCompany(
        @Args('fairId', { type: () => String }) fairId: string,
        @Args('companyId', { type: () => String }) companyId: string
    ) {
        return this.companyFairParticipationService.findByFairAndCompany(
            fairId,
            companyId
        )
    }

    @Mutation(() => CompanyFairParticipation)
    removeCompanyFairParticipation(
        @Args('id', { type: () => String }) id: string
    ) {
        return this.companyFairParticipationService.remove(id)
    }
}
