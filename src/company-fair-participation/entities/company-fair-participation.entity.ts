import { ObjectType, Field } from '@nestjs/graphql'
import { Company } from '../../company/entities/company.entity'
import { Fair } from '../../fair/entities/fair.entity'
import { CompanyFairJob } from '../../company-fair-job/entities/company-fair-job.entity'
import { CompanyFairContactPerson } from '../../company-fair-contact-person/entities/company-fair-contact-person.entity'
import { JobCategory } from '../../job-category/entities/job-category.entity'
import { PartnerLink } from '../../partner-link/entities/partner-link.entity'

@ObjectType()
export class CompanyFairParticipation {
    @Field()
    id: string

    @Field(() => Company)
    company: Company

    @Field()
    companyId: string

    @Field(() => Fair)
    fair: Fair

    @Field()
    fairId: string

    @Field(() => [CompanyFairJob], { nullable: true })
    companyFairJobs: CompanyFairJob[]

    @Field(() => [JobCategory], { nullable: true })
    categories?: JobCategory[]

    @Field(() => [PartnerLink], { nullable: true })
    partnerLinks?: PartnerLink[]

    @Field(() => [CompanyFairContactPerson], { nullable: true })
    companyFairContactPersons: CompanyFairContactPerson[]
}
