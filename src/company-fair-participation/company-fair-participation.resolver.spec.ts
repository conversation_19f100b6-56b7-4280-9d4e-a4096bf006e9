import { Test, TestingModule } from '@nestjs/testing';
import { CompanyFairParticipationResolver } from './company-fair-participation.resolver';
import { CompanyFairParticipationService } from './company-fair-participation.service';

describe('CompanyFairParticipationResolver', () => {
  let resolver: CompanyFairParticipationResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CompanyFairParticipationResolver, CompanyFairParticipationService],
    }).compile();

    resolver = module.get<CompanyFairParticipationResolver>(CompanyFairParticipationResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
