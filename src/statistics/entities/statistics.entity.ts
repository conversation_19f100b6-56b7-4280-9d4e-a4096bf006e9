import { ObjectType, Field } from '@nestjs/graphql'

@ObjectType()
export class Statistics {
    @Field()
    id: string

    @Field()
    companies: number

    @Field()
    applicants: number

    @Field()
    activeApplicants: number

    @Field()
    jobAdverts: number

    @Field()
    activeJobAdverts: number

    @Field()
    likes: number

    @Field()
    bookmarks: number

    @Field()
    matches: number

    @Field()
    chatMessages: number

    @Field()
    createdAt: Date
}
