import {
    BadRequestException,
    Controller,
    Headers,
    HttpCode,
    HttpStatus,
    Post,
} from '@nestjs/common'
import { StatisticsService } from './statistics.service'

@Controller('statistics')
export class StatisticsController {
    constructor(private readonly statisticsService: StatisticsService) {}

    @Post('daily-statistics')
    @HttpCode(HttpStatus.OK)
    async createDailyStatistics(@Headers('accessToken') accessToken: string) {
        if (!accessToken || accessToken !== 'sqWPlITlgCJX0u8VXd2J') {
            throw new BadRequestException('No access - wrong token')
        }
        await this.statisticsService.collectAndSaveStatistics()
        return { status: 'success' }
    }
}
