import { PrismaService } from 'src/prisma.service'
import { CreateStatisticsInput } from './dto/create-statistics.input'
import { ActionState, Statistics } from '@prisma/client'
import { Injectable } from '@nestjs/common'

@Injectable()
export class StatisticsService {
    constructor(private readonly prisma: PrismaService) {}

    async create(
        createStatisticsInput: CreateStatisticsInput
    ): Promise<Statistics> {
        const entry = this.prisma.statistics.create({
            data: {
                ...createStatisticsInput,
            },
        })
        return entry
    }

    async collectAndSaveStatistics(): Promise<Statistics> {
        const companiesCount = await this.prisma.company.count()

        const applicantsCount = await this.prisma.applicant.count()

        const activeApplicantsCount = await this.prisma.applicant.count({
            where: {
                lastActive: {
                    gte: new Date(
                        new Date().setDate(new Date().getDate() - 21)
                    ),
                },
            },
        })

        const jobAdvertsCount = await this.prisma.jobAdvert.count()

        const activeJobAdvertsCount = await this.prisma.jobAdvert.count({
            where: {
                paused: false,
                approved: true,
                //TODO check for subscription
            },
        })

        const likesCount = await this.prisma.jobAction.count({
            where: {
                state: ActionState.LIKED,
            },
        })

        const bookmarksCount = await this.prisma.jobAction.count({
            where: {
                state: ActionState.BOOKMARKED,
            },
        })

        const matchesCount = await this.prisma.jobAction.count({
            where: {
                state: ActionState.MATCHED,
            },
        })

        const chatMessagesCount = await this.prisma.message.count()

        // const chatMessagesCompanyCount = await this.prisma.message.count({
        //     where: {
        //         isCompany: true,
        //     },
        // })

        const newStatistic = await this.create({
            companies: companiesCount,
            activeApplicants: activeApplicantsCount,
            activeJobAdverts: activeJobAdvertsCount,
            applicants: applicantsCount,
            bookmarks: bookmarksCount,
            chatMessages: chatMessagesCount,
            jobAdverts: jobAdvertsCount,
            likes: likesCount,
            matches: matchesCount,
        })

        return newStatistic
    }
}
