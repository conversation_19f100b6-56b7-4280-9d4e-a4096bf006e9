import { InputType, Field } from '@nestjs/graphql'
import { IsNumber } from 'class-validator'

@InputType()
export class CreateStatisticsInput {
    @Field()
    @IsNumber()
    companies: number

    @Field()
    @IsNumber()
    applicants: number

    @Field()
    @IsNumber()
    activeApplicants: number

    @Field()
    @IsNumber()
    jobAdverts: number

    @Field()
    @IsNumber()
    activeJobAdverts: number

    @Field()
    @IsNumber()
    likes: number

    @Field()
    @IsNumber()
    bookmarks: number

    @Field()
    @IsNumber()
    matches: number

    @Field()
    @IsNumber()
    chatMessages: number
}
