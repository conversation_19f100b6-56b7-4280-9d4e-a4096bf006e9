import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import {
    ExecutionContext,
    Injectable,
    UnauthorizedException,
} from '@nestjs/common'

@Injectable()
export class ApplicantGuard extends GraphqlAuthGuard {
    async canActivate(context: ExecutionContext) {
        await super.canActivate(context)

        const isApplicant = !!this.user?.applicantId
        const isSuperUser = this.user?.isSuperUser
        const isFGAmin = this.user?.isFGAdmin

        if (isSuperUser || isFGAmin) return true

        if (
            !isApplicant ||
            (this.varsApplicantId &&
                this.varsApplicantId !== this.user.applicantId)
        ) {
            throw new UnauthorizedException(
                'UnAuthorized:: You are not an authorized applicant'
            )
        }
        return true
    }
}
