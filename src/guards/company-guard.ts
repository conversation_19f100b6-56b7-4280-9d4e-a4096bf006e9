import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'
import {
    ExecutionContext,
    Injectable,
    UnauthorizedException,
} from '@nestjs/common'

@Injectable()
export class CompanyGuard extends GraphqlAuthGuard {
    async canActivate(context: ExecutionContext) {
        await super.canActivate(context)

        const isCompanyUser = !!this.user?.companyId
        const isSuperUser = this.user?.isSuperUser
        const isFGAmin = this.user?.isFGAdmin

        if (isSuperUser || isFGAmin) return true

        if (
            !isCompanyUser ||
            (this.varsCompanyId && this.varsCompanyId !== this.user.companyId)
        ) {
            throw new UnauthorizedException(
                'UnAuthorized:: You are not an authorized company user'
            )
        }
        return true
    }
}
