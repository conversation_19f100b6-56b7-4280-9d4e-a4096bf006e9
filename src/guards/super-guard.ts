import { GraphqlAuthGuard } from '../auth/graphql-auth/graphql-auth.guard'

import {
    ExecutionContext,
    Injectable,
    UnauthorizedException,
} from '@nestjs/common'

@Injectable()
export class SuperGuard extends GraphqlAuthGuard {
    async canActivate(context: ExecutionContext) {
        await super.canActivate(context)

        const isSuperUser = this.user?.isSuperUser
        const isFGAmin = this.user?.isFGAdmin
        if (!isSuperUser && !isFGAmin) {
            throw new UnauthorizedException(
                'UnAuthorized:: You are not a super user'
            )
        }
        return true
    }
}
